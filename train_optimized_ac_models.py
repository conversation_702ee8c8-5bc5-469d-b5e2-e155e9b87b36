#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空调负荷柔性调控能力分析系统 - 优化模型训练脚本

专门针对空调负荷识别任务的两个优化模型：
1. XGBoost模型 - 使用网格搜索调参
2. 深度学习模型 - 针对性架构设计

作者: AI Assistant
创建时间: 2025-07-14
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
import logging
import os
import joblib
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple

# 机器学习库
from sklearn.model_selection import train_test_split, GridSearchCV, cross_val_score
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.feature_selection import SelectKBest, f_regression
import xgboost as xgb

# 深度学习库
try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers, callbacks, optimizers
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    print("TensorFlow未安装，将跳过深度学习模型")

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/optimized_model_training.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class OptimizedACLoadModelTrainer:
    """优化的空调负荷模型训练器"""
    
    def __init__(self):
        """初始化"""
        self.data = None
        self.X_train = None
        self.X_val = None
        self.X_test = None
        self.y_train = None
        self.y_val = None
        self.y_test = None
        self.scaler = None
        self.feature_selector = None
        self.best_xgb_model = None
        self.best_dl_model = None
        self.feature_names = None
        
        # 创建必要目录
        Path('logs').mkdir(exist_ok=True)
        Path('models/optimized').mkdir(parents=True, exist_ok=True)
        Path('figures/optimized_models').mkdir(parents=True, exist_ok=True)
        
        logger.info("优化空调负荷模型训练器初始化完成")
    
    def load_and_prepare_data(self, file_path: str = None) -> None:
        """加载并准备数据"""
        logger.info("加载并准备数据...")
        
        if file_path is None:
            # 查找最新的大宽表文件
            processed_dir = Path('data/processed')
            wide_table_files = list(processed_dir.glob('final_wide_table_*.csv'))
            
            if not wide_table_files:
                raise FileNotFoundError("未找到大宽表文件")
            
            file_path = max(wide_table_files, key=lambda x: x.stat().st_mtime)
        
        logger.info(f"加载数据文件: {file_path}")
        
        # 分块加载大文件
        chunk_size = 50000
        chunks = []
        
        for i, chunk in enumerate(pd.read_csv(file_path, chunksize=chunk_size)):
            chunks.append(chunk)
            if (i + 1) % 5 == 0:
                logger.info(f"已加载 {(i + 1) * chunk_size} 行数据...")
        
        self.data = pd.concat(chunks, ignore_index=True)
        logger.info(f"数据加载完成，形状: {self.data.shape}")
        
        # 创建目标变量
        self._create_target_variable()
        
        # 准备特征
        self._prepare_features()
        
        # 数据分割和预处理
        self._split_and_preprocess_data()
    
    def _create_target_variable(self) -> None:
        """创建空调负荷目标变量"""
        logger.info("创建空调负荷目标变量...")
        
        df = self.data.copy()
        
        # 初始化空调负荷比例
        df['ac_load_ratio'] = 0.0
        
        # 找到可用的温度列
        temp_cols = [col for col in df.columns if 'Temperature' in col and df[col].notna().sum() > 1000]
        
        if temp_cols:
            temp_col = temp_cols[0]
            logger.info(f"使用温度列: {temp_col}")
            
            # 基于温度的空调负荷建模
            # 制冷需求（夏季）
            cooling_mask = (df[temp_col] > 26) & (df['Month'].isin([6, 7, 8, 9]))
            df.loc[cooling_mask, 'ac_load_ratio'] = np.minimum(
                (df.loc[cooling_mask, temp_col] - 26) / 12 * 0.8, 0.85
            )
            
            # 制热需求（冬季）
            heating_mask = (df[temp_col] < 16) & (df['Month'].isin([12, 1, 2, 3]))
            df.loc[heating_mask, 'ac_load_ratio'] = np.minimum(
                (16 - df.loc[heating_mask, temp_col]) / 12 * 0.7, 0.75
            )
            
            # 过渡季节的轻微调节
            transition_mask = (~cooling_mask) & (~heating_mask) & (
                ((df[temp_col] > 24) & (df[temp_col] <= 26)) |
                ((df[temp_col] >= 16) & (df[temp_col] < 18))
            )
            df.loc[transition_mask, 'ac_load_ratio'] = 0.1
            
        else:
            logger.warning("未找到温度数据，使用简化的空调负荷估算")
            # 基于时间和季节的简化估算
            summer_peak = df['Month'].isin([7, 8]) & df['Hour'].isin(range(14, 18))
            summer_normal = df['Month'].isin([6, 9]) & df['Hour'].isin(range(10, 20))
            winter_peak = df['Month'].isin([1, 12]) & df['Hour'].isin(range(8, 10, 18, 22))
            
            df.loc[summer_peak, 'ac_load_ratio'] = 0.6
            df.loc[summer_normal, 'ac_load_ratio'] = 0.4
            df.loc[winter_peak, 'ac_load_ratio'] = 0.5
        
        # 添加随机噪声
        noise = np.random.normal(0, 0.03, len(df))
        df['ac_load_ratio'] = np.clip(df['ac_load_ratio'] + noise, 0, 1)
        
        # 计算空调负荷
        if 'HourlyAvgLoad' in df.columns:
            self.target = df['HourlyAvgLoad'] * df['ac_load_ratio']
        else:
            # 创建模拟负荷
            base_load = 50 + 30 * np.sin(2 * np.pi * df['Hour'] / 24)
            seasonal_factor = 1 + 0.3 * np.sin(2 * np.pi * (df['Month'] - 1) / 12)
            self.target = base_load * seasonal_factor * df['ac_load_ratio']
        
        logger.info(f"空调负荷统计: 均值={self.target.mean():.2f}, 标准差={self.target.std():.2f}")
        logger.info(f"非零空调负荷比例: {(self.target > 0).mean():.2%}")
    
    def _prepare_features(self) -> None:
        """准备特征数据"""
        logger.info("准备特征数据...")
        
        df = self.data.copy()
        feature_cols = []
        
        # 1. 时间特征
        time_features = ['Hour', 'DayOfWeek', 'Month', 'IsWeekend']
        feature_cols.extend([col for col in time_features if col in df.columns])
        
        # 2. 负荷特征（排除目标相关）
        load_features = [col for col in df.columns if any(keyword in col for keyword in 
                        ['Load', 'Energy', 'Power', 'Factor']) and 'Avg' not in col]
        feature_cols.extend(load_features[:8])
        
        # 3. 气象特征
        weather_features = [col for col in df.columns if any(keyword in col for keyword in 
                           ['Temperature', 'Humidity', 'Pressure', 'Precipitation', 'WindSpeed'])]
        feature_cols.extend(weather_features)
        
        # 4. 空气质量特征（选择覆盖率高的）
        aq_features = [col for col in df.columns if any(keyword in col for keyword in 
                      ['AQI', 'PM2_5', 'PM10', 'SO2', 'NO2', 'O3', 'CO'])]
        
        # 按覆盖率排序
        aq_coverage = {}
        for col in aq_features:
            coverage = df[col].notna().sum() / len(df)
            if coverage > 0.05:  # 至少5%覆盖率
                aq_coverage[col] = coverage
        
        top_aq_features = sorted(aq_coverage.items(), key=lambda x: x[1], reverse=True)[:15]
        feature_cols.extend([col for col, _ in top_aq_features])
        
        # 5. 创建交互特征
        if 'Hour' in df.columns and 'Month' in df.columns:
            df['Hour_Month_Interaction'] = df['Hour'] * df['Month']
            feature_cols.append('Hour_Month_Interaction')
        
        if 'IsWeekend' in df.columns and 'Hour' in df.columns:
            df['Weekend_Hour_Interaction'] = df['IsWeekend'] * df['Hour']
            feature_cols.append('Weekend_Hour_Interaction')
        
        # 如果有温度数据，创建温度相关特征
        temp_cols = [col for col in df.columns if 'Temperature' in col and df[col].notna().sum() > 1000]
        if temp_cols:
            temp_col = temp_cols[0]
            df['Temp_Squared'] = df[temp_col] ** 2
            df['Cooling_Degree_Days'] = np.maximum(df[temp_col] - 18, 0)
            df['Heating_Degree_Days'] = np.maximum(18 - df[temp_col], 0)
            feature_cols.extend(['Temp_Squared', 'Cooling_Degree_Days', 'Heating_Degree_Days'])
        
        # 验证特征有效性
        valid_features = []
        for col in set(feature_cols):
            if col in df.columns:
                try:
                    # 检查是否为数值类型
                    test_series = pd.to_numeric(df[col].dropna().head(1000), errors='coerce')
                    if test_series.notna().sum() > 500:  # 至少50%能转换为数值
                        non_null_ratio = df[col].notna().sum() / len(df)
                        if non_null_ratio > 0.01:  # 至少1%的数据非空
                            valid_features.append(col)
                except:
                    continue
        
        self.feature_names = valid_features
        self.features = df[valid_features].fillna(0)  # 用0填充缺失值
        
        logger.info(f"选择了 {len(valid_features)} 个有效特征")
        logger.info(f"特征列表前10个: {valid_features[:10]}")
    
    def _split_and_preprocess_data(self) -> None:
        """数据分割和预处理"""
        logger.info("数据分割和预处理...")
        
        # 移除异常值
        Q1 = self.target.quantile(0.25)
        Q3 = self.target.quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        valid_mask = (self.target >= lower_bound) & (self.target <= upper_bound)
        X_clean = self.features[valid_mask]
        y_clean = self.target[valid_mask]
        
        logger.info(f"移除异常值后数据形状: X={X_clean.shape}, y={y_clean.shape}")
        
        # 数据分割
        X_temp, self.X_test, y_temp, self.y_test = train_test_split(
            X_clean, y_clean, test_size=0.2, random_state=42, stratify=None
        )
        
        self.X_train, self.X_val, self.y_train, self.y_val = train_test_split(
            X_temp, y_temp, test_size=0.25, random_state=42
        )
        
        # 特征选择
        logger.info("进行特征选择...")
        self.feature_selector = SelectKBest(score_func=f_regression, k=min(40, len(self.feature_names)))
        X_train_selected = self.feature_selector.fit_transform(self.X_train, self.y_train)
        X_val_selected = self.feature_selector.transform(self.X_val)
        X_test_selected = self.feature_selector.transform(self.X_test)
        
        # 获取选中的特征名
        selected_features_mask = self.feature_selector.get_support()
        self.selected_feature_names = [name for name, selected in zip(self.feature_names, selected_features_mask) if selected]
        
        # 更新特征数据
        self.X_train = pd.DataFrame(X_train_selected, columns=self.selected_feature_names)
        self.X_val = pd.DataFrame(X_val_selected, columns=self.selected_feature_names)
        self.X_test = pd.DataFrame(X_test_selected, columns=self.selected_feature_names)
        
        # 数据标准化（用于深度学习）
        self.scaler = RobustScaler()  # 使用RobustScaler对异常值更鲁棒
        self.X_train_scaled = self.scaler.fit_transform(self.X_train)
        self.X_val_scaled = self.scaler.transform(self.X_val)
        self.X_test_scaled = self.scaler.transform(self.X_test)
        
        logger.info(f"最终数据形状:")
        logger.info(f"  训练集: {self.X_train.shape}")
        logger.info(f"  验证集: {self.X_val.shape}")
        logger.info(f"  测试集: {self.X_test.shape}")
        logger.info(f"  选中特征数: {len(self.selected_feature_names)}")
    
    def train_optimized_xgboost(self) -> Dict:
        """训练优化的XGBoost模型"""
        logger.info("训练优化的XGBoost模型...")
        
        # 定义参数网格
        param_grid = {
            'n_estimators': [200, 300, 500],
            'max_depth': [4, 6, 8],
            'learning_rate': [0.05, 0.1, 0.15],
            'subsample': [0.8, 0.9],
            'colsample_bytree': [0.8, 0.9],
            'reg_alpha': [0, 0.1],
            'reg_lambda': [1, 1.5]
        }
        
        # 创建基础模型
        base_model = xgb.XGBRegressor(
            random_state=42,
            n_jobs=-1,
            tree_method='hist'  # 更快的训练
        )
        
        # 网格搜索
        logger.info("开始网格搜索调参...")
        grid_search = GridSearchCV(
            base_model,
            param_grid,
            cv=3,  # 3折交叉验证
            scoring='r2',
            n_jobs=-1,
            verbose=1
        )
        
        # 使用部分数据进行调参（加速）
        sample_size = min(50000, len(self.X_train))
        sample_indices = np.random.choice(len(self.X_train), sample_size, replace=False)
        
        X_train_sample = self.X_train.iloc[sample_indices]
        y_train_sample = self.y_train.iloc[sample_indices]
        
        grid_search.fit(X_train_sample, y_train_sample)
        
        logger.info(f"最佳参数: {grid_search.best_params_}")
        logger.info(f"最佳交叉验证分数: {grid_search.best_score_:.4f}")
        
        # 使用最佳参数训练完整模型
        self.best_xgb_model = grid_search.best_estimator_
        self.best_xgb_model.fit(self.X_train, self.y_train)
        
        # 评估模型
        train_pred = self.best_xgb_model.predict(self.X_train)
        val_pred = self.best_xgb_model.predict(self.X_val)
        test_pred = self.best_xgb_model.predict(self.X_test)
        
        results = {
            'model': self.best_xgb_model,
            'best_params': grid_search.best_params_,
            'train_r2': r2_score(self.y_train, train_pred),
            'val_r2': r2_score(self.y_val, val_pred),
            'test_r2': r2_score(self.y_test, test_pred),
            'train_rmse': np.sqrt(mean_squared_error(self.y_train, train_pred)),
            'val_rmse': np.sqrt(mean_squared_error(self.y_val, val_pred)),
            'test_rmse': np.sqrt(mean_squared_error(self.y_test, test_pred)),
            'predictions': {
                'train': train_pred,
                'val': val_pred,
                'test': test_pred
            }
        }
        
        logger.info(f"XGBoost模型性能:")
        logger.info(f"  验证集 R²: {results['val_r2']:.4f}")
        logger.info(f"  测试集 R²: {results['test_r2']:.4f}")
        logger.info(f"  测试集 RMSE: {results['test_rmse']:.4f}")
        
        return results

    def create_optimized_neural_network(self, input_dim: int) -> keras.Model:
        """创建优化的神经网络模型"""

        # 使用函数式API构建更复杂的网络
        inputs = keras.Input(shape=(input_dim,))

        # 第一个分支：主要特征处理
        x1 = layers.Dense(128, activation='relu')(inputs)
        x1 = layers.BatchNormalization()(x1)
        x1 = layers.Dropout(0.3)(x1)

        x1 = layers.Dense(64, activation='relu')(x1)
        x1 = layers.BatchNormalization()(x1)
        x1 = layers.Dropout(0.2)(x1)

        # 第二个分支：残差连接
        x2 = layers.Dense(64, activation='relu')(inputs)
        x2 = layers.BatchNormalization()(x2)

        # 合并分支
        merged = layers.Add()([x1, x2])

        # 最终层
        x = layers.Dense(32, activation='relu')(merged)
        x = layers.Dropout(0.1)(x)

        x = layers.Dense(16, activation='relu')(x)

        # 输出层
        outputs = layers.Dense(1, activation='linear')(x)

        model = keras.Model(inputs=inputs, outputs=outputs)

        # 使用自适应学习率优化器
        optimizer = optimizers.Adam(
            learning_rate=0.001,
            beta_1=0.9,
            beta_2=0.999,
            epsilon=1e-7
        )

        model.compile(
            optimizer=optimizer,
            loss='mse',
            metrics=['mae', 'mse']
        )

        return model

    def train_optimized_deep_learning(self) -> Dict:
        """训练优化的深度学习模型"""
        if not TENSORFLOW_AVAILABLE:
            logger.warning("TensorFlow不可用，跳过深度学习模型")
            return {}

        logger.info("训练优化的深度学习模型...")

        # 创建模型
        model = self.create_optimized_neural_network(self.X_train_scaled.shape[1])

        # 定义回调函数
        callbacks_list = [
            # 早停
            callbacks.EarlyStopping(
                monitor='val_loss',
                patience=15,
                restore_best_weights=True,
                verbose=1
            ),

            # 学习率调度
            callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=8,
                min_lr=1e-6,
                verbose=1
            ),

            # 模型检查点
            callbacks.ModelCheckpoint(
                'models/optimized/best_dl_model_temp.h5',
                monitor='val_loss',
                save_best_only=True,
                verbose=0
            )
        ]

        # 训练模型
        logger.info("开始训练深度学习模型...")
        history = model.fit(
            self.X_train_scaled, self.y_train,
            validation_data=(self.X_val_scaled, self.y_val),
            epochs=150,
            batch_size=256,
            callbacks=callbacks_list,
            verbose=1
        )

        # 加载最佳模型
        model.load_weights('models/optimized/best_dl_model_temp.h5')
        self.best_dl_model = model

        # 评估模型
        train_pred = model.predict(self.X_train_scaled, verbose=0).flatten()
        val_pred = model.predict(self.X_val_scaled, verbose=0).flatten()
        test_pred = model.predict(self.X_test_scaled, verbose=0).flatten()

        results = {
            'model': model,
            'train_r2': r2_score(self.y_train, train_pred),
            'val_r2': r2_score(self.y_val, val_pred),
            'test_r2': r2_score(self.y_test, test_pred),
            'train_rmse': np.sqrt(mean_squared_error(self.y_train, train_pred)),
            'val_rmse': np.sqrt(mean_squared_error(self.y_val, val_pred)),
            'test_rmse': np.sqrt(mean_squared_error(self.y_test, test_pred)),
            'history': history.history,
            'predictions': {
                'train': train_pred,
                'val': val_pred,
                'test': test_pred
            }
        }

        logger.info(f"深度学习模型性能:")
        logger.info(f"  验证集 R²: {results['val_r2']:.4f}")
        logger.info(f"  测试集 R²: {results['test_r2']:.4f}")
        logger.info(f"  测试集 RMSE: {results['test_rmse']:.4f}")

        return results

    def create_comparison_plots(self, xgb_results: Dict, dl_results: Dict) -> None:
        """创建模型比较图表"""
        logger.info("创建模型比较图表...")

        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('优化空调负荷识别模型比较分析', fontsize=16, fontweight='bold')

        # 1. 模型性能比较
        ax1 = axes[0, 0]
        models = ['XGBoost', 'Deep Learning']
        val_r2 = [xgb_results['val_r2'], dl_results.get('val_r2', 0)]
        test_r2 = [xgb_results['test_r2'], dl_results.get('test_r2', 0)]

        x = np.arange(len(models))
        width = 0.35

        ax1.bar(x - width/2, val_r2, width, label='验证集 R²', alpha=0.8)
        ax1.bar(x + width/2, test_r2, width, label='测试集 R²', alpha=0.8)
        ax1.set_xlabel('模型')
        ax1.set_ylabel('R² 分数')
        ax1.set_title('模型 R² 分数比较')
        ax1.set_xticks(x)
        ax1.set_xticklabels(models)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. RMSE比较
        ax2 = axes[0, 1]
        val_rmse = [xgb_results['val_rmse'], dl_results.get('val_rmse', 0)]
        test_rmse = [xgb_results['test_rmse'], dl_results.get('test_rmse', 0)]

        ax2.bar(x - width/2, val_rmse, width, label='验证集 RMSE', alpha=0.8)
        ax2.bar(x + width/2, test_rmse, width, label='测试集 RMSE', alpha=0.8)
        ax2.set_xlabel('模型')
        ax2.set_ylabel('RMSE')
        ax2.set_title('模型 RMSE 比较')
        ax2.set_xticks(x)
        ax2.set_xticklabels(models)
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. XGBoost预测vs实际值
        ax3 = axes[0, 2]
        test_pred_xgb = xgb_results['predictions']['test']
        ax3.scatter(self.y_test, test_pred_xgb, alpha=0.5, s=1)
        ax3.plot([self.y_test.min(), self.y_test.max()],
                [self.y_test.min(), self.y_test.max()], 'r--', lw=2)
        ax3.set_xlabel('实际值')
        ax3.set_ylabel('预测值')
        ax3.set_title(f'XGBoost - 预测vs实际值 (R²={xgb_results["test_r2"]:.3f})')
        ax3.grid(True, alpha=0.3)

        # 4. 深度学习预测vs实际值
        ax4 = axes[1, 0]
        if dl_results:
            test_pred_dl = dl_results['predictions']['test']
            ax4.scatter(self.y_test, test_pred_dl, alpha=0.5, s=1)
            ax4.plot([self.y_test.min(), self.y_test.max()],
                    [self.y_test.min(), self.y_test.max()], 'r--', lw=2)
            ax4.set_xlabel('实际值')
            ax4.set_ylabel('预测值')
            ax4.set_title(f'Deep Learning - 预测vs实际值 (R²={dl_results["test_r2"]:.3f})')
        else:
            ax4.text(0.5, 0.5, '深度学习模型不可用', ha='center', va='center', transform=ax4.transAxes)
            ax4.set_title('Deep Learning - 预测vs实际值')
        ax4.grid(True, alpha=0.3)

        # 5. 特征重要性（XGBoost）
        ax5 = axes[1, 1]
        if hasattr(self.best_xgb_model, 'feature_importances_'):
            importance = self.best_xgb_model.feature_importances_
            feature_importance = pd.DataFrame({
                'feature': self.selected_feature_names,
                'importance': importance
            }).sort_values('importance', ascending=False)

            top_features = feature_importance.head(15)
            ax5.barh(range(len(top_features)), top_features['importance'])
            ax5.set_yticks(range(len(top_features)))
            ax5.set_yticklabels(top_features['feature'], fontsize=8)
            ax5.set_xlabel('特征重要性')
            ax5.set_title('XGBoost - 前15个重要特征')
            ax5.invert_yaxis()

        # 6. 训练历史（深度学习）
        ax6 = axes[1, 2]
        if dl_results and 'history' in dl_results:
            history = dl_results['history']
            epochs = range(1, len(history['loss']) + 1)
            ax6.plot(epochs, history['loss'], 'b-', label='训练损失')
            ax6.plot(epochs, history['val_loss'], 'r-', label='验证损失')
            ax6.set_xlabel('Epoch')
            ax6.set_ylabel('Loss')
            ax6.set_title('深度学习模型训练历史')
            ax6.legend()
            ax6.grid(True, alpha=0.3)
        else:
            ax6.text(0.5, 0.5, '深度学习训练历史不可用', ha='center', va='center', transform=ax6.transAxes)
            ax6.set_title('深度学习模型训练历史')

        plt.tight_layout()

        # 保存图表
        plot_file = 'figures/optimized_models/model_comparison.png'
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        logger.info(f"模型比较图表已保存至: {plot_file}")
        plt.close()

    def save_models(self, xgb_results: Dict, dl_results: Dict) -> Dict[str, str]:
        """保存训练好的模型"""
        logger.info("保存训练好的模型...")

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        saved_files = {}

        # 保存XGBoost模型
        xgb_model_file = f'models/optimized/xgboost_model_{timestamp}.pkl'
        joblib.dump(self.best_xgb_model, xgb_model_file)
        saved_files['xgboost_model'] = xgb_model_file

        # 保存深度学习模型
        if dl_results and self.best_dl_model:
            dl_model_file = f'models/optimized/dl_model_{timestamp}.h5'
            self.best_dl_model.save(dl_model_file)
            saved_files['dl_model'] = dl_model_file

        # 保存预处理器
        scaler_file = f'models/optimized/scaler_{timestamp}.pkl'
        joblib.dump(self.scaler, scaler_file)
        saved_files['scaler'] = scaler_file

        # 保存特征选择器
        selector_file = f'models/optimized/feature_selector_{timestamp}.pkl'
        joblib.dump(self.feature_selector, selector_file)
        saved_files['feature_selector'] = selector_file

        # 保存模型信息
        model_info = {
            'timestamp': timestamp,
            'data_shape': self.data.shape,
            'selected_features': self.selected_feature_names,
            'feature_count': len(self.selected_feature_names),
            'xgboost_performance': {
                'best_params': xgb_results['best_params'],
                'val_r2': xgb_results['val_r2'],
                'test_r2': xgb_results['test_r2'],
                'test_rmse': xgb_results['test_rmse']
            }
        }

        if dl_results:
            model_info['dl_performance'] = {
                'val_r2': dl_results['val_r2'],
                'test_r2': dl_results['test_r2'],
                'test_rmse': dl_results['test_rmse']
            }

        info_file = f'models/optimized/model_info_{timestamp}.json'
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(model_info, f, ensure_ascii=False, indent=2)
        saved_files['model_info'] = info_file

        logger.info("模型保存完成:")
        for key, file_path in saved_files.items():
            logger.info(f"  {key}: {file_path}")

        return saved_files

def main():
    """主函数"""
    print("空调负荷柔性调控能力分析系统 - 优化模型训练")
    print("=" * 70)

    try:
        # 创建训练器
        trainer = OptimizedACLoadModelTrainer()

        # 1. 加载和准备数据
        print("1. 加载和准备数据...")
        trainer.load_and_prepare_data()

        # 2. 训练优化的XGBoost模型
        print("2. 训练优化的XGBoost模型...")
        xgb_results = trainer.train_optimized_xgboost()

        # 3. 训练优化的深度学习模型
        print("3. 训练优化的深度学习模型...")
        if TENSORFLOW_AVAILABLE:
            dl_results = trainer.train_optimized_deep_learning()
        else:
            dl_results = {}
            print("   TensorFlow不可用，跳过深度学习模型")

        # 4. 创建比较图表
        print("4. 创建模型比较图表...")
        trainer.create_comparison_plots(xgb_results, dl_results)

        # 5. 保存模型
        print("5. 保存训练好的模型...")
        saved_files = trainer.save_models(xgb_results, dl_results)

        # 6. 显示最终结果
        print("\n" + "=" * 70)
        print("优化模型训练完成！")

        print(f"\nXGBoost模型性能:")
        print(f"  最佳参数: {xgb_results['best_params']}")
        print(f"  验证集 R²: {xgb_results['val_r2']:.4f}")
        print(f"  测试集 R²: {xgb_results['test_r2']:.4f}")
        print(f"  测试集 RMSE: {xgb_results['test_rmse']:.4f}")

        if dl_results:
            print(f"\n深度学习模型性能:")
            print(f"  验证集 R²: {dl_results['val_r2']:.4f}")
            print(f"  测试集 R²: {dl_results['test_r2']:.4f}")
            print(f"  测试集 RMSE: {dl_results['test_rmse']:.4f}")

            # 比较两个模型
            if xgb_results['test_r2'] > dl_results['test_r2']:
                print(f"\n🏆 XGBoost模型表现更好 (R²差异: {xgb_results['test_r2'] - dl_results['test_r2']:.4f})")
            else:
                print(f"\n🏆 深度学习模型表现更好 (R²差异: {dl_results['test_r2'] - xgb_results['test_r2']:.4f})")

        print(f"\n模型文件已保存:")
        for key, file_path in saved_files.items():
            print(f"  {key}: {file_path}")

        print(f"\n查看详细结果:")
        print("- 模型比较图表: figures/optimized_models/model_comparison.png")
        print("- 训练日志: logs/optimized_model_training.log")

        return 0

    except Exception as e:
        logger.error(f"优化模型训练失败: {e}")
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
