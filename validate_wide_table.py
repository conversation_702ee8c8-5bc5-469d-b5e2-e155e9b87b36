#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空调负荷柔性调控能力分析系统 - 大宽表数据验证脚本

验证生成的大宽表数据质量，并生成基础分析报告

作者: AI Assistant
创建时间: 2025-07-13
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
import logging
import os
from pathlib import Path
from datetime import datetime

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WideTableValidator:
    """大宽表数据验证器"""
    
    def __init__(self, file_path: str):
        """
        初始化验证器
        
        Args:
            file_path: 大宽表文件路径
        """
        self.file_path = file_path
        self.data = None
        self.validation_results = {}
        
        # 创建输出目录
        Path('figures').mkdir(exist_ok=True)
        Path('results').mkdir(exist_ok=True)
        
        logger.info(f"大宽表验证器初始化完成，文件: {file_path}")
    
    def load_data(self) -> pd.DataFrame:
        """加载大宽表数据"""
        logger.info("加载大宽表数据...")
        
        try:
            self.data = pd.read_csv(self.file_path)
            self.data['Timestamp'] = pd.to_datetime(self.data['Timestamp'])
            
            logger.info(f"数据加载完成，形状: {self.data.shape}")
            return self.data
            
        except Exception as e:
            logger.error(f"数据加载失败: {e}")
            raise
    
    def basic_validation(self) -> dict:
        """基础数据验证"""
        logger.info("执行基础数据验证...")
        
        results = {}
        
        # 1. 数据形状和基本信息
        results['shape'] = self.data.shape
        results['columns_count'] = len(self.data.columns)
        results['memory_usage_mb'] = self.data.memory_usage(deep=True).sum() / (1024**2)
        
        # 2. 时间范围验证
        results['time_range'] = {
            'start': self.data['Timestamp'].min(),
            'end': self.data['Timestamp'].max(),
            'span_days': (self.data['Timestamp'].max() - self.data['Timestamp'].min()).days
        }
        
        # 3. 缺失值统计
        missing_stats = self.data.isnull().sum()
        results['missing_values'] = {
            'total_missing': missing_stats.sum(),
            'columns_with_missing': (missing_stats > 0).sum(),
            'worst_columns': missing_stats.nlargest(10).to_dict()
        }
        
        # 4. 数据类型统计
        results['data_types'] = self.data.dtypes.value_counts().to_dict()
        
        # 5. 特征分类统计
        load_cols = [col for col in self.data.columns if any(keyword in col for keyword in 
                    ['Load', 'Energy', 'Power', 'Factor'])]
        weather_cols = [col for col in self.data.columns if any(keyword in col for keyword in 
                       ['Temperature', 'Humidity', 'Pressure', 'Precipitation', 'WindSpeed'])]
        aq_cols = [col for col in self.data.columns if any(keyword in col for keyword in 
                  ['AQI', 'PM2_5', 'PM10', 'SO2', 'NO2', 'O3', 'CO'])]
        time_cols = [col for col in self.data.columns if col in 
                    ['Year', 'Month', 'Day', 'Hour', 'DayOfWeek', 'IsWeekend', 'Season']]
        
        results['feature_categories'] = {
            'load_features': len(load_cols),
            'weather_features': len(weather_cols),
            'air_quality_features': len(aq_cols),
            'time_features': len(time_cols),
            'other_features': len(self.data.columns) - len(load_cols) - len(weather_cols) - len(aq_cols) - len(time_cols)
        }
        
        self.validation_results['basic'] = results
        logger.info("基础数据验证完成")
        return results
    
    def data_quality_check(self) -> dict:
        """数据质量检查"""
        logger.info("执行数据质量检查...")
        
        results = {}
        
        # 1. 负荷数据质量检查
        load_cols = ['HourlyAvgLoad', 'DailyMaxLoad', 'DailyAvgLoad']
        available_load_cols = [col for col in load_cols if col in self.data.columns]
        
        if available_load_cols:
            load_stats = {}
            for col in available_load_cols:
                if self.data[col].notna().sum() > 0:
                    load_stats[col] = {
                        'mean': self.data[col].mean(),
                        'std': self.data[col].std(),
                        'min': self.data[col].min(),
                        'max': self.data[col].max(),
                        'negative_count': (self.data[col] < 0).sum(),
                        'zero_count': (self.data[col] == 0).sum()
                    }
            results['load_quality'] = load_stats
        
        # 2. 空气质量数据质量检查
        aq_cols = [col for col in self.data.columns if 'AQI_成都' in col or 'PM2_5_成都' in col]
        if aq_cols:
            aq_stats = {}
            for col in aq_cols[:2]:  # 只检查前两个
                if self.data[col].notna().sum() > 0:
                    aq_stats[col] = {
                        'mean': self.data[col].mean(),
                        'std': self.data[col].std(),
                        'min': self.data[col].min(),
                        'max': self.data[col].max(),
                        'coverage': self.data[col].notna().sum() / len(self.data)
                    }
            results['air_quality'] = aq_stats
        
        # 3. 时间连续性检查
        if 'Timestamp' in self.data.columns:
            time_diffs = self.data['Timestamp'].diff().dropna()
            most_common_diff = time_diffs.mode().iloc[0] if len(time_diffs.mode()) > 0 else None
            
            results['time_continuity'] = {
                'most_common_interval': str(most_common_diff),
                'unique_intervals': len(time_diffs.unique()),
                'gaps_count': (time_diffs > pd.Timedelta(hours=2)).sum()
            }
        
        self.validation_results['quality'] = results
        logger.info("数据质量检查完成")
        return results
    
    def create_summary_plots(self) -> None:
        """创建摘要图表"""
        logger.info("创建摘要图表...")
        
        # 设置图表样式
        plt.style.use('default')
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('大宽表数据质量摘要', fontsize=16, fontweight='bold')
        
        # 1. 缺失值热图
        ax1 = axes[0, 0]
        missing_data = self.data.isnull().sum()
        top_missing = missing_data.nlargest(20)
        
        if len(top_missing) > 0:
            top_missing.plot(kind='barh', ax=ax1)
            ax1.set_title('缺失值最多的20个特征')
            ax1.set_xlabel('缺失值数量')
        else:
            ax1.text(0.5, 0.5, '无缺失值', ha='center', va='center', transform=ax1.transAxes)
            ax1.set_title('缺失值统计')
        
        # 2. 负荷数据分布
        ax2 = axes[0, 1]
        if 'HourlyAvgLoad' in self.data.columns and self.data['HourlyAvgLoad'].notna().sum() > 0:
            self.data['HourlyAvgLoad'].hist(bins=50, ax=ax2, alpha=0.7)
            ax2.set_title('小时平均负荷分布')
            ax2.set_xlabel('负荷值')
            ax2.set_ylabel('频次')
        else:
            ax2.text(0.5, 0.5, '无负荷数据', ha='center', va='center', transform=ax2.transAxes)
            ax2.set_title('负荷数据分布')
        
        # 3. 时间分布
        ax3 = axes[1, 0]
        if 'Hour' in self.data.columns:
            hour_counts = self.data['Hour'].value_counts().sort_index()
            hour_counts.plot(kind='bar', ax=ax3)
            ax3.set_title('小时分布')
            ax3.set_xlabel('小时')
            ax3.set_ylabel('记录数')
            ax3.tick_params(axis='x', rotation=45)
        
        # 4. 区域分布
        ax4 = axes[1, 1]
        if 'Region' in self.data.columns:
            region_counts = self.data['Region'].value_counts()
            if len(region_counts) > 0:
                region_counts.plot(kind='pie', ax=ax4, autopct='%1.1f%%')
                ax4.set_title('区域分布')
                ax4.set_ylabel('')
            else:
                ax4.text(0.5, 0.5, '无区域数据', ha='center', va='center', transform=ax4.transAxes)
                ax4.set_title('区域分布')
        
        plt.tight_layout()
        
        # 保存图表
        plot_file = 'figures/wide_table_summary.png'
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        logger.info(f"摘要图表已保存至: {plot_file}")
        plt.close()
    
    def generate_validation_report(self) -> str:
        """生成验证报告"""
        logger.info("生成验证报告...")
        
        report = []
        report.append("=" * 80)
        report.append("大宽表数据验证报告")
        report.append("=" * 80)
        report.append(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"数据文件: {self.file_path}")
        report.append("")
        
        # 基础信息
        if 'basic' in self.validation_results:
            basic = self.validation_results['basic']
            report.append("1. 基础信息")
            report.append("-" * 40)
            report.append(f"数据形状: {basic['shape']}")
            report.append(f"特征数量: {basic['columns_count']}")
            report.append(f"内存使用: {basic['memory_usage_mb']:.2f} MB")
            report.append(f"时间范围: {basic['time_range']['start']} 到 {basic['time_range']['end']}")
            report.append(f"时间跨度: {basic['time_range']['span_days']} 天")
            report.append("")
            
            # 特征分类
            report.append("2. 特征分类统计")
            report.append("-" * 40)
            for category, count in basic['feature_categories'].items():
                report.append(f"{category}: {count}")
            report.append("")
            
            # 缺失值情况
            report.append("3. 数据完整性")
            report.append("-" * 40)
            missing = basic['missing_values']
            report.append(f"总缺失值: {missing['total_missing']}")
            report.append(f"有缺失值的列: {missing['columns_with_missing']}")
            report.append("缺失值最多的列:")
            for col, count in list(missing['worst_columns'].items())[:5]:
                pct = (count / basic['shape'][0]) * 100
                report.append(f"  {col}: {count} ({pct:.1f}%)")
            report.append("")
        
        # 数据质量
        if 'quality' in self.validation_results:
            quality = self.validation_results['quality']
            report.append("4. 数据质量评估")
            report.append("-" * 40)
            
            if 'load_quality' in quality:
                report.append("负荷数据质量:")
                for col, stats in quality['load_quality'].items():
                    report.append(f"  {col}:")
                    report.append(f"    均值: {stats['mean']:.2f}")
                    report.append(f"    范围: {stats['min']:.2f} - {stats['max']:.2f}")
                    report.append(f"    负值数量: {stats['negative_count']}")
                    report.append(f"    零值数量: {stats['zero_count']}")
                report.append("")
            
            if 'time_continuity' in quality:
                tc = quality['time_continuity']
                report.append("时间连续性:")
                report.append(f"  主要时间间隔: {tc['most_common_interval']}")
                report.append(f"  时间间隔种类: {tc['unique_intervals']}")
                report.append(f"  时间间隙数量: {tc['gaps_count']}")
                report.append("")
        
        # 建议
        report.append("5. 建议")
        report.append("-" * 40)
        report.append("✓ 数据已成功整合，包含负荷、气象和空气质量数据")
        report.append("✓ 可以开始进行特征工程和相关性分析")
        report.append("✓ 建议重点关注成都地区的数据，覆盖率较高")
        report.append("✓ 可以构建负荷与空气质量的关联模型")
        report.append("")
        
        report_text = "\n".join(report)
        
        # 保存报告
        report_file = 'results/wide_table_validation_report.txt'
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_text)
        
        logger.info(f"验证报告已保存至: {report_file}")
        return report_text

def main():
    """主函数"""
    print("空调负荷柔性调控能力分析系统 - 大宽表数据验证")
    print("=" * 60)
    
    # 查找最新的综合大宽表文件
    processed_dir = Path('data/processed')
    wide_table_files = list(processed_dir.glob('comprehensive_wide_table_*.csv'))
    
    if not wide_table_files:
        print("错误: 未找到综合大宽表文件")
        return 1
    
    # 选择最新的文件
    latest_file = max(wide_table_files, key=lambda x: x.stat().st_mtime)
    print(f"验证文件: {latest_file}")
    
    try:
        # 创建验证器
        validator = WideTableValidator(str(latest_file))
        
        # 1. 加载数据
        print("1. 加载数据...")
        validator.load_data()
        
        # 2. 基础验证
        print("2. 执行基础验证...")
        basic_results = validator.basic_validation()
        
        # 3. 数据质量检查
        print("3. 数据质量检查...")
        quality_results = validator.data_quality_check()
        
        # 4. 创建图表
        print("4. 生成可视化图表...")
        validator.create_summary_plots()
        
        # 5. 生成报告
        print("5. 生成验证报告...")
        report = validator.generate_validation_report()
        
        print("\n" + "=" * 60)
        print("数据验证完成！")
        print(f"数据形状: {basic_results['shape']}")
        print(f"特征总数: {basic_results['columns_count']}")
        print(f"负荷特征: {basic_results['feature_categories']['load_features']}")
        print(f"空气质量特征: {basic_results['feature_categories']['air_quality_features']}")
        print("\n查看详细结果:")
        print("- 验证报告: results/wide_table_validation_report.txt")
        print("- 可视化图表: figures/wide_table_summary.png")
        
    except Exception as e:
        logger.error(f"数据验证失败: {e}")
        print(f"错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
