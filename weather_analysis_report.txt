天气文件详细分析报告
================================================================================

文件名: 新津23.xls
文件大小: 2.36 MB
解析区域: 新津
解析年份: 2023
读取状态: 成功
数据形状: (8760, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2023-01-01 00:00:00': 1, '2023-09-01 10:00:00': 1, '2023-09-01 04:00:00': 1, '2023-09-01 05:00:00': 1, '2023-09-01 06:00:00': 1, '2023-09-01 07:00:00': 1, '2023-09-01 08:00:00': 1, '2023-09-01 09:00:00': 1, '2023-09-01 11:00:00': 1, '2023-09-03 05:00:00': 1}, '北京时(UTC+8)': {'2023-01-01 08:00:00': 1, '2023-09-01 18:00:00': 1, '2023-09-01 12:00:00': 1, '2023-09-01 13:00:00': 1, '2023-09-01 14:00:00': 1, '2023-09-01 15:00:00': 1, '2023-09-01 16:00:00': 1, '2023-09-01 17:00:00': 1, '2023-09-01 19:00:00': 1, '2023-09-03 13:00:00': 1}, '备注': {'新津区': 8760}}
样本数据:
  行1: {'经度(lon)': 103.8, '纬度(lat)': 30.4, '地面气压(hPa)': 967.5, '气温(℃)': 3.69, '降水量(mm)': 0.001, '露点温度(℃)': 3.61, '经向风速(V,m/s)': -0.53, '纬向风速(U,m/s)': 0.51, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2023-01-01 00:00:00', '北京时(UTC+8)': '2023-01-01 08:00:00', '备注': '新津区'}
  行2: {'经度(lon)': 103.8, '纬度(lat)': 30.4, '地面气压(hPa)': 968.22, '气温(℃)': 3.88, '降水量(mm)': 0.002, '露点温度(℃)': 3.66, '经向风速(V,m/s)': -0.58, '纬向风速(U,m/s)': 0.68, '太阳辐射净强度(net,J/m2)': 74710.75, '太阳辐射总强度(down,J/m2)': 85791.25, '世界时(UTC)': '2023-01-01 01:00:00', '北京时(UTC+8)': '2023-01-01 09:00:00', '备注': '新津区'}
  行3: {'经度(lon)': 103.8, '纬度(lat)': 30.4, '地面气压(hPa)': 968.75, '气温(℃)': 4.84, '降水量(mm)': 0.001, '露点温度(℃)': 3.82, '经向风速(V,m/s)': -0.64, '纬向风速(U,m/s)': 1.03, '太阳辐射净强度(net,J/m2)': 314814.75, '太阳辐射总强度(down,J/m2)': 361539.25, '世界时(UTC)': '2023-01-01 02:00:00', '北京时(UTC+8)': '2023-01-01 10:00:00', '备注': '新津区'}

--------------------------------------------------------------------------------

文件名: 邛崃23.xls
文件大小: 5.49 MB
解析区域: 邛崃
解析年份: 2023
读取状态: 成功
数据形状: (8760, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('O'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'邛崃': 8760}, '世界时(UTC)': {'2023-01-01 00:00:00': 1, '2023-09-01 10:00:00': 1, '2023-09-01 04:00:00': 1, '2023-09-01 05:00:00': 1, '2023-09-01 06:00:00': 1, '2023-09-01 07:00:00': 1, '2023-09-01 08:00:00': 1, '2023-09-01 09:00:00': 1, '2023-09-01 11:00:00': 1, '2023-09-03 05:00:00': 1}, '北京时(UTC+8)': {'2023-01-01 08:00:00': 1, '2023-09-01 18:00:00': 1, '2023-09-01 12:00:00': 1, '2023-09-01 13:00:00': 1, '2023-09-01 14:00:00': 1, '2023-09-01 15:00:00': 1, '2023-09-01 16:00:00': 1, '2023-09-01 17:00:00': 1, '2023-09-01 19:00:00': 1, '2023-09-03 13:00:00': 1}, '雪层温度(℃)': {'*': 6995, '0.01': 539, '0.0': 115, '-0.01': 67, '-0.02': 59, '-0.05': 46, '-0.03': 38, '-0.07': 33, '-0.06': 33, '-0.04': 25}, '相对湿度(%)': {'78.03': 7, '79.97': 7, '91.58': 7, '67.13': 7, '81.62': 7, '88.14': 7, '90.14': 7, '88.53': 7, '78.43': 7, '76.77': 7}}
样本数据:
  行1: {'参考地名': '邛崃', '经度(lon)': 103.5, '纬度(lat)': 30.5, '世界时(UTC)': '2023-01-01 00:00:00', '北京时(UTC+8)': '2023-01-01 08:00:00', '海平面气压(hPa)': 1029.92, '地面气压(hPa)': 944.22, '气温2m(℃)': 4.01, '降水量(mm)': 0.005, '降雪量(mm)': 0.002, '积雪深度(mm of water equivalent)': 0.69, '积雪密度(kg/m3)': 101.7, '雪层温度(℃)': '-0.07', '地表温度(℃)': 3.17, '露点温度(℃)': 3.67, '相对湿度(%)': '97.61', '蒸发量(mm)': -0.0003, '潜在蒸发量(mm)': 0.0, '经向风速(V,m/s)': -0.05, '纬向风速(U,m/s)': 0.64, '最大阵风(上一小时内，m/s)': 1.19, '云底高度(m)': '45.17', '低层云量(lcc)': 0.75, '中层云量(mcc)': 0.3, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.76, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.0296, '地表径流(mm)': 0.0005, '地下径流(mm)': 0.0291, '雷暴概率(TT，K)': 25.18, 'K指数(K)': 2.64, '对流可用位能(J/kg)': 0.0}
  行2: {'参考地名': '邛崃', '经度(lon)': 103.5, '纬度(lat)': 30.5, '世界时(UTC)': '2023-01-01 01:00:00', '北京时(UTC+8)': '2023-01-01 09:00:00', '海平面气压(hPa)': 1030.94, '地面气压(hPa)': 945.09, '气温2m(℃)': 3.76, '降水量(mm)': 0.005, '降雪量(mm)': 0.001, '积雪深度(mm of water equivalent)': 0.69, '积雪密度(kg/m3)': 101.7, '雪层温度(℃)': '-0.08', '地表温度(℃)': 4.52, '露点温度(℃)': 3.55, '相对湿度(%)': '98.57', '蒸发量(mm)': -0.0014, '潜在蒸发量(mm)': -0.0011, '经向风速(V,m/s)': -0.13, '纬向风速(U,m/s)': 0.68, '最大阵风(上一小时内，m/s)': 1.84, '云底高度(m)': '58.46', '低层云量(lcc)': 0.82, '中层云量(mcc)': 0.22, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.83, '总太阳辐射度(down,J/m2)': 90816.0, '净太阳辐射度(net,J/m2)': 76864.0, '直接辐射(J/m2)': 26048.0, '紫外强度(J/m2)': 14904.0, '径流(mm)': 0.0296, '地表径流(mm)': 0.0005, '地下径流(mm)': 0.0291, '雷暴概率(TT，K)': 24.93, 'K指数(K)': 2.32, '对流可用位能(J/kg)': 0.12}
  行3: {'参考地名': '邛崃', '经度(lon)': 103.5, '纬度(lat)': 30.5, '世界时(UTC)': '2023-01-01 02:00:00', '北京时(UTC+8)': '2023-01-01 10:00:00', '海平面气压(hPa)': 1031.19, '地面气压(hPa)': 945.35, '气温2m(℃)': 5.13, '降水量(mm)': 0.004, '降雪量(mm)': 0.001, '积雪深度(mm of water equivalent)': 0.69, '积雪密度(kg/m3)': 101.71, '雪层温度(℃)': '-0.05', '地表温度(℃)': 6.17, '露点温度(℃)': 3.88, '相对湿度(%)': '91.6', '蒸发量(mm)': -0.0344, '潜在蒸发量(mm)': -0.0278, '经向风速(V,m/s)': 0.21, '纬向风速(U,m/s)': 0.64, '最大阵风(上一小时内，m/s)': 2.92, '云底高度(m)': '154.04', '低层云量(lcc)': 0.45, '中层云量(mcc)': 0.25, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.53, '总太阳辐射度(down,J/m2)': 431680.0, '净太阳辐射度(net,J/m2)': 368704.0, '直接辐射(J/m2)': 169088.0, '紫外强度(J/m2)': 57296.0, '径流(mm)': 0.0291, '地表径流(mm)': 0.0002, '地下径流(mm)': 0.0288, '雷暴概率(TT，K)': 24.74, 'K指数(K)': 2.04, '对流可用位能(J/kg)': 1.06}

--------------------------------------------------------------------------------

文件名: 崇州23.xls
文件大小: 5.48 MB
解析区域: 崇州
解析年份: 2023
读取状态: 成功
数据形状: (8760, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('O'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'崇州': 8760}, '世界时(UTC)': {'2023-01-01 00:00:00': 1, '2023-09-01 10:00:00': 1, '2023-09-01 04:00:00': 1, '2023-09-01 05:00:00': 1, '2023-09-01 06:00:00': 1, '2023-09-01 07:00:00': 1, '2023-09-01 08:00:00': 1, '2023-09-01 09:00:00': 1, '2023-09-01 11:00:00': 1, '2023-09-03 05:00:00': 1}, '北京时(UTC+8)': {'2023-01-01 08:00:00': 1, '2023-09-01 18:00:00': 1, '2023-09-01 12:00:00': 1, '2023-09-01 13:00:00': 1, '2023-09-01 14:00:00': 1, '2023-09-01 15:00:00': 1, '2023-09-01 16:00:00': 1, '2023-09-01 17:00:00': 1, '2023-09-01 19:00:00': 1, '2023-09-03 13:00:00': 1}, '雪层温度(℃)': {'*': 8743, '-0.82': 1, '-0.37': 1, '-1.33': 1, '-1.84': 1, '-4.9': 1, '-4.62': 1, '-4.14': 1, '-3.7': 1, '-0.51': 1}, '相对湿度(%)': {'86.56': 9, '86.37': 8, '79.83': 8, '80.7': 8, '75.83': 7, '87.38': 7, '87.8': 7, '85.43': 7, '86.61': 7, '86.85': 7}}
样本数据:
  行1: {'参考地名': '崇州', '经度(lon)': 103.75, '纬度(lat)': 30.75, '世界时(UTC)': '2023-01-01 00:00:00', '北京时(UTC+8)': '2023-01-01 08:00:00', '海平面气压(hPa)': 1029.99, '地面气压(hPa)': 970.25, '气温2m(℃)': 5.27, '降水量(mm)': 0.002, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 3.88, '露点温度(℃)': 4.66, '相对湿度(%)': '95.87', '蒸发量(mm)': -0.0001, '潜在蒸发量(mm)': 0.0, '经向风速(V,m/s)': 0.11, '纬向风速(U,m/s)': 0.88, '最大阵风(上一小时内，m/s)': 1.42, '云底高度(m)': '31.67', '低层云量(lcc)': 0.48, '中层云量(mcc)': 0.3, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.56, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.0024, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0024, '雷暴概率(TT，K)': 25.71, 'K指数(K)': 4.36, '对流可用位能(J/kg)': 0.0}
  行2: {'参考地名': '崇州', '经度(lon)': 103.75, '纬度(lat)': 30.75, '世界时(UTC)': '2023-01-01 01:00:00', '北京时(UTC+8)': '2023-01-01 09:00:00', '海平面气压(hPa)': 1030.88, '地面气压(hPa)': 971.04, '气温2m(℃)': 5.27, '降水量(mm)': 0.001, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 5.51, '露点温度(℃)': 4.66, '相对湿度(%)': '95.84', '蒸发量(mm)': -0.0027, '潜在蒸发量(mm)': -0.0019, '经向风速(V,m/s)': -0.23, '纬向风速(U,m/s)': 0.39, '最大阵风(上一小时内，m/s)': 1.94, '云底高度(m)': '49.21', '低层云量(lcc)': 0.44, '中层云量(mcc)': 0.26, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.56, '总太阳辐射度(down,J/m2)': 79616.0, '净太阳辐射度(net,J/m2)': 68544.0, '直接辐射(J/m2)': 9984.0, '紫外强度(J/m2)': 12592.0, '径流(mm)': 0.0024, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0024, '雷暴概率(TT，K)': 25.63, 'K指数(K)': 4.03, '对流可用位能(J/kg)': 0.0}
  行3: {'参考地名': '崇州', '经度(lon)': 103.75, '纬度(lat)': 30.75, '世界时(UTC)': '2023-01-01 02:00:00', '北京时(UTC+8)': '2023-01-01 10:00:00', '海平面气压(hPa)': 1031.13, '地面气压(hPa)': 971.31, '气温2m(℃)': 6.48, '降水量(mm)': 0.001, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 7.31, '露点温度(℃)': 5.44, '相对湿度(%)': '93.07', '蒸发量(mm)': -0.0299, '潜在蒸发量(mm)': -0.024, '经向风速(V,m/s)': -0.29, '纬向风速(U,m/s)': 0.49, '最大阵风(上一小时内，m/s)': 2.54, '云底高度(m)': '148.29', '低层云量(lcc)': 0.21, '中层云量(mcc)': 0.28, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.4, '总太阳辐射度(down,J/m2)': 365568.0, '净太阳辐射度(net,J/m2)': 316928.0, '直接辐射(J/m2)': 95424.0, '紫外强度(J/m2)': 48096.0, '径流(mm)': 0.0026, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0024, '雷暴概率(TT，K)': 25.43, 'K指数(K)': 3.7, '对流可用位能(J/kg)': 0.0}

--------------------------------------------------------------------------------

文件名: 金牛23.xls
文件大小: 2.36 MB
解析区域: 金牛
解析年份: 2023
读取状态: 成功
数据形状: (8760, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2023-01-01 00:00:00': 1, '2023-09-01 10:00:00': 1, '2023-09-01 04:00:00': 1, '2023-09-01 05:00:00': 1, '2023-09-01 06:00:00': 1, '2023-09-01 07:00:00': 1, '2023-09-01 08:00:00': 1, '2023-09-01 09:00:00': 1, '2023-09-01 11:00:00': 1, '2023-09-03 05:00:00': 1}, '北京时(UTC+8)': {'2023-01-01 08:00:00': 1, '2023-09-01 18:00:00': 1, '2023-09-01 12:00:00': 1, '2023-09-01 13:00:00': 1, '2023-09-01 14:00:00': 1, '2023-09-01 15:00:00': 1, '2023-09-01 16:00:00': 1, '2023-09-01 17:00:00': 1, '2023-09-01 19:00:00': 1, '2023-09-03 13:00:00': 1}, '备注': {'金牛区': 8760}}
样本数据:
  行1: {'经度(lon)': 104.0, '纬度(lat)': 30.7, '地面气压(hPa)': 969.87, '气温(℃)': 3.3, '降水量(mm)': 0.001, '露点温度(℃)': 3.16, '经向风速(V,m/s)': 0.09, '纬向风速(U,m/s)': 0.51, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2023-01-01 00:00:00', '北京时(UTC+8)': '2023-01-01 08:00:00', '备注': '金牛区'}
  行2: {'经度(lon)': 104.0, '纬度(lat)': 30.7, '地面气压(hPa)': 970.58, '气温(℃)': 3.73, '降水量(mm)': 0.001, '露点温度(℃)': 3.53, '经向风速(V,m/s)': 0.27, '纬向风速(U,m/s)': 0.56, '太阳辐射净强度(net,J/m2)': 72555.0, '太阳辐射总强度(down,J/m2)': 83993.0, '世界时(UTC)': '2023-01-01 01:00:00', '北京时(UTC+8)': '2023-01-01 09:00:00', '备注': '金牛区'}
  行3: {'经度(lon)': 104.0, '纬度(lat)': 30.7, '地面气压(hPa)': 971.09, '气温(℃)': 4.66, '降水量(mm)': 0.0, '露点温度(℃)': 3.88, '经向风速(V,m/s)': 0.24, '纬向风速(U,m/s)': 0.87, '太阳辐射净强度(net,J/m2)': 295106.5, '太阳辐射总强度(down,J/m2)': 341636.47, '世界时(UTC)': '2023-01-01 02:00:00', '北京时(UTC+8)': '2023-01-01 10:00:00', '备注': '金牛区'}

--------------------------------------------------------------------------------

文件名: 大邑25.xls
文件大小: 2.39 MB
解析区域: 大邑
解析年份: 2025
读取状态: 成功
数据形状: (3648, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('float64'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'大邑县': 3648}, '世界时(UTC)': {'2025-01-01 00:00:00': 1, '2025-04-11 23:00:00': 1, '2025-04-12 01:00:00': 1, '2025-04-12 02:00:00': 1, '2025-04-12 03:00:00': 1, '2025-04-12 04:00:00': 1, '2025-04-12 05:00:00': 1, '2025-04-12 06:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 08:00:00': 1}, '北京时(UTC+8)': {'2025-01-01 08:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 09:00:00': 1, '2025-04-12 10:00:00': 1, '2025-04-12 11:00:00': 1, '2025-04-12 12:00:00': 1, '2025-04-12 13:00:00': 1, '2025-04-12 14:00:00': 1, '2025-04-12 15:00:00': 1, '2025-04-12 16:00:00': 1}, '雪层温度(℃)': {'*': 1606, '0.01': 857, '0.0': 258, '-0.01': 164, '-0.02': 92, '-0.03': 56, '-0.04': 37, '-0.05': 35, '-0.08': 33, '-0.06': 31}, '云底高度(m)': {'*': 122, '30.35': 4, '42.4': 4, '30.36': 3, '30.03': 3, '30.31': 3, '541.88': 2, '1450.47': 2, '29.78': 2, '695.18': 2}}
样本数据:
  行1: {'参考地名': '大邑县', '经度(lon)': 103.5, '纬度(lat)': 30.5, '世界时(UTC)': '2025-01-01 00:00:00', '北京时(UTC+8)': '2025-01-01 08:00:00', '海平面气压(hPa)': 1022.11, '地面气压(hPa)': 937.61, '气温2m(℃)': 2.89, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 1.41, '积雪密度(kg/m3)': 103.59, '雪层温度(℃)': '-0.56', '地表温度(℃)': -0.54, '露点温度(℃)': -0.68, '相对湿度(%)': 77.37, '蒸发量(mm)': 0.0005, '潜在蒸发量(mm)': 0.0009, '经向风速(V,m/s)': -0.59, '纬向风速(U,m/s)': -0.2, '最大阵风(上一小时内，m/s)': 2.23, '云底高度(m)': '933.62', '低层云量(lcc)': 0.31, '中层云量(mcc)': 0.27, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.37, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.0312, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0312, '雷暴概率(TT，K)': 35.59, 'K指数(K)': 1.23, '对流可用位能(J/kg)': 0.0}
  行2: {'参考地名': '大邑县', '经度(lon)': 103.5, '纬度(lat)': 30.5, '世界时(UTC)': '2025-01-01 01:00:00', '北京时(UTC+8)': '2025-01-01 09:00:00', '海平面气压(hPa)': 1023.15, '地面气压(hPa)': 938.49, '气温2m(℃)': 1.99, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 1.41, '积雪密度(kg/m3)': 103.59, '雪层温度(℃)': '-1.72', '地表温度(℃)': 3.1, '露点温度(℃)': -0.08, '相对湿度(%)': 86.15, '蒸发量(mm)': -0.0029, '潜在蒸发量(mm)': -0.0041, '经向风速(V,m/s)': -0.41, '纬向风速(U,m/s)': -0.05, '最大阵风(上一小时内，m/s)': 3.02, '云底高度(m)': '845.45', '低层云量(lcc)': 0.22, '中层云量(mcc)': 0.19, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.26, '总太阳辐射度(down,J/m2)': 154048.0, '净太阳辐射度(net,J/m2)': 130944.0, '直接辐射(J/m2)': 80256.0, '紫外强度(J/m2)': 17152.0, '径流(mm)': 0.0312, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0312, '雷暴概率(TT，K)': 35.94, 'K指数(K)': 0.88, '对流可用位能(J/kg)': 0.0}
  行3: {'参考地名': '大邑县', '经度(lon)': 103.5, '纬度(lat)': 30.5, '世界时(UTC)': '2025-01-01 02:00:00', '北京时(UTC+8)': '2025-01-01 10:00:00', '海平面气压(hPa)': 1024.06, '地面气压(hPa)': 939.33, '气温2m(℃)': 6.99, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 1.41, '积雪密度(kg/m3)': 103.59, '雪层温度(℃)': '-0.18', '地表温度(℃)': 7.96, '露点温度(℃)': 1.34, '相对湿度(%)': 67.25, '蒸发量(mm)': -0.0335, '潜在蒸发量(mm)': -0.0484, '经向风速(V,m/s)': -0.46, '纬向风速(U,m/s)': 0.12, '最大阵风(上一小时内，m/s)': 3.89, '云底高度(m)': '804.97', '低层云量(lcc)': 0.24, '中层云量(mcc)': 0.21, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.27, '总太阳辐射度(down,J/m2)': 630912.0, '净太阳辐射度(net,J/m2)': 540416.0, '直接辐射(J/m2)': 399744.0, '紫外强度(J/m2)': 70136.0, '径流(mm)': 0.0312, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0312, '雷暴概率(TT，K)': 36.15, 'K指数(K)': 1.0, '对流可用位能(J/kg)': 0.0}

--------------------------------------------------------------------------------

文件名: 锦江24.xls
文件大小: 2.37 MB
解析区域: 锦江
解析年份: 2024
读取状态: 成功
数据形状: (8784, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2024-01-01 00:00:00': 1, '2024-09-01 03:00:00': 1, '2024-08-31 21:00:00': 1, '2024-08-31 22:00:00': 1, '2024-08-31 23:00:00': 1, '2024-09-01 00:00:00': 1, '2024-09-01 01:00:00': 1, '2024-09-01 02:00:00': 1, '2024-09-01 04:00:00': 1, '2024-08-31 02:00:00': 1}, '北京时(UTC+8)': {'2024-01-01 08:00:00': 1, '2024-09-01 11:00:00': 1, '2024-09-01 05:00:00': 1, '2024-09-01 06:00:00': 1, '2024-09-01 07:00:00': 1, '2024-09-01 08:00:00': 1, '2024-09-01 09:00:00': 1, '2024-09-01 10:00:00': 1, '2024-09-01 12:00:00': 1, '2024-08-31 10:00:00': 1}, '备注': {'锦江区': 8784}}
样本数据:
  行1: {'经度(lon)': 104.1, '纬度(lat)': 30.6, '地面气压(hPa)': 971.08, '气温(℃)': 6.91, '降水量(mm)': 0.001, '露点温度(℃)': 6.38, '经向风速(V,m/s)': -0.44, '纬向风速(U,m/s)': 0.87, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2024-01-01 00:00:00', '北京时(UTC+8)': '2024-01-01 08:00:00', '备注': '锦江区'}
  行2: {'经度(lon)': 104.1, '纬度(lat)': 30.6, '地面气压(hPa)': 971.79, '气温(℃)': 7.4, '降水量(mm)': 0.003, '露点温度(℃)': 6.83, '经向风速(V,m/s)': -0.28, '纬向风速(U,m/s)': 0.82, '太阳辐射净强度(net,J/m2)': 63333.5, '太阳辐射总强度(down,J/m2)': 73513.0, '世界时(UTC)': '2024-01-01 01:00:00', '北京时(UTC+8)': '2024-01-01 09:00:00', '备注': '锦江区'}
  行3: {'经度(lon)': 104.1, '纬度(lat)': 30.6, '地面气压(hPa)': 972.49, '气温(℃)': 8.9, '降水量(mm)': 0.006, '露点温度(℃)': 7.13, '经向风速(V,m/s)': -0.21, '纬向风速(U,m/s)': 0.85, '太阳辐射净强度(net,J/m2)': 194352.0, '太阳辐射总强度(down,J/m2)': 225591.0, '世界时(UTC)': '2024-01-01 02:00:00', '北京时(UTC+8)': '2024-01-01 10:00:00', '备注': '锦江区'}

--------------------------------------------------------------------------------

文件名: 温江23.xls
文件大小: 2.35 MB
解析区域: 温江
解析年份: 2023
读取状态: 成功
数据形状: (8760, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2023-01-01 00:00:00': 1, '2023-09-01 10:00:00': 1, '2023-09-01 04:00:00': 1, '2023-09-01 05:00:00': 1, '2023-09-01 06:00:00': 1, '2023-09-01 07:00:00': 1, '2023-09-01 08:00:00': 1, '2023-09-01 09:00:00': 1, '2023-09-01 11:00:00': 1, '2023-09-03 05:00:00': 1}, '北京时(UTC+8)': {'2023-01-01 08:00:00': 1, '2023-09-01 18:00:00': 1, '2023-09-01 12:00:00': 1, '2023-09-01 13:00:00': 1, '2023-09-01 14:00:00': 1, '2023-09-01 15:00:00': 1, '2023-09-01 16:00:00': 1, '2023-09-01 17:00:00': 1, '2023-09-01 19:00:00': 1, '2023-09-03 13:00:00': 1}, '备注': {'温江区': 8760}}
样本数据:
  行1: {'经度(lon)': 103.8, '纬度(lat)': 30.7, '地面气压(hPa)': 956.33, '气温(℃)': 3.23, '降水量(mm)': 0.002, '露点温度(℃)': 2.95, '经向风速(V,m/s)': -0.33, '纬向风速(U,m/s)': 0.2, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2023-01-01 00:00:00', '北京时(UTC+8)': '2023-01-01 08:00:00', '备注': '温江区'}
  行2: {'经度(lon)': 103.8, '纬度(lat)': 30.7, '地面气压(hPa)': 957.05, '气温(℃)': 3.38, '降水量(mm)': 0.001, '露点温度(℃)': 3.13, '经向风速(V,m/s)': -0.11, '纬向风速(U,m/s)': 0.27, '太阳辐射净强度(net,J/m2)': 69972.0, '太阳辐射总强度(down,J/m2)': 81699.0, '世界时(UTC)': '2023-01-01 01:00:00', '北京时(UTC+8)': '2023-01-01 09:00:00', '备注': '温江区'}
  行3: {'经度(lon)': 103.8, '纬度(lat)': 30.7, '地面气压(hPa)': 957.59, '气温(℃)': 4.39, '降水量(mm)': 0.0, '露点温度(℃)': 3.37, '经向风速(V,m/s)': -0.02, '纬向风速(U,m/s)': 0.56, '太阳辐射净强度(net,J/m2)': 313098.5, '太阳辐射总强度(down,J/m2)': 365571.0, '世界时(UTC)': '2023-01-01 02:00:00', '北京时(UTC+8)': '2023-01-01 10:00:00', '备注': '温江区'}

--------------------------------------------------------------------------------

文件名: 甘孜23.xls
文件大小: 3.16 MB
解析区域: 甘孜
解析年份: 2023
读取状态: 成功
数据形状: (8760, 18)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '相对湿度(%)', '地表温度(℃)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '相对湿度(%)': dtype('O'), '地表温度(℃)': dtype('float64'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64')}
唯一值统计: {'参考地名': {'甘孜': 8760}, '世界时(UTC)': {'2023-01-01 00:00:00': 1, '2023-09-01 10:00:00': 1, '2023-09-01 04:00:00': 1, '2023-09-01 05:00:00': 1, '2023-09-01 06:00:00': 1, '2023-09-01 07:00:00': 1, '2023-09-01 08:00:00': 1, '2023-09-01 09:00:00': 1, '2023-09-01 11:00:00': 1, '2023-09-03 05:00:00': 1}, '北京时(UTC+8)': {'2023-01-01 08:00:00': 1, '2023-09-01 18:00:00': 1, '2023-09-01 12:00:00': 1, '2023-09-01 13:00:00': 1, '2023-09-01 14:00:00': 1, '2023-09-01 15:00:00': 1, '2023-09-01 16:00:00': 1, '2023-09-01 17:00:00': 1, '2023-09-01 19:00:00': 1, '2023-09-03 13:00:00': 1}, '相对湿度(%)': {'81.04': 7, '87.12': 6, '64.88': 6, '78.62': 6, '80.52': 6, '86.02': 6, '89.57': 6, '*': 6, '92.29': 6, '88.58': 5}}
样本数据:
  行1: {'参考地名': '甘孜', '经度(lon)': 100.0, '纬度(lat)': 31.5, '世界时(UTC)': '2023-01-01 00:00:00', '北京时(UTC+8)': '2023-01-01 08:00:00', '海平面气压(hPa)': 1016.22, '地面气压(hPa)': 602.16, '气温2m(℃)': -22.3, '降水量(mm)': 0.001, '相对湿度(%)': '84.09', '地表温度(℃)': -23.02, '蒸发量(mm)': 0.001, '潜在蒸发量(mm)': 0.001, '经向风速(V,m/s)': 1.52, '纬向风速(U,m/s)': 1.03, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0}
  行2: {'参考地名': '甘孜', '经度(lon)': 100.0, '纬度(lat)': 31.5, '世界时(UTC)': '2023-01-01 01:00:00', '北京时(UTC+8)': '2023-01-01 09:00:00', '海平面气压(hPa)': 1017.82, '地面气压(hPa)': 602.85, '气温2m(℃)': -22.66, '降水量(mm)': 0.001, '相对湿度(%)': '78.79', '地表温度(℃)': -20.99, '蒸发量(mm)': 0.0009, '潜在蒸发量(mm)': 0.0006, '经向风速(V,m/s)': 1.67, '纬向风速(U,m/s)': 1.3, '总太阳辐射度(down,J/m2)': 79232.0, '净太阳辐射度(net,J/m2)': 50368.0, '直接辐射(J/m2)': 37056.0}
  行3: {'参考地名': '甘孜', '经度(lon)': 100.0, '纬度(lat)': 31.5, '世界时(UTC)': '2023-01-01 02:00:00', '北京时(UTC+8)': '2023-01-01 10:00:00', '海平面气压(hPa)': 1019.19, '地面气压(hPa)': 603.4, '气温2m(℃)': -16.79, '降水量(mm)': 0.002, '相对湿度(%)': '75.29', '地表温度(℃)': -13.48, '蒸发量(mm)': -0.0008, '潜在蒸发量(mm)': -0.0143, '经向风速(V,m/s)': 1.64, '纬向风速(U,m/s)': 1.26, '总太阳辐射度(down,J/m2)': 581184.0, '净太阳辐射度(net,J/m2)': 370048.0, '直接辐射(J/m2)': 323008.0}

--------------------------------------------------------------------------------

文件名: 锦江25.xls
文件大小: 1.02 MB
解析区域: 锦江
解析年份: 2025
读取状态: 成功
数据形状: (3648, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2025-01-01 00:00:00': 1, '2025-04-11 23:00:00': 1, '2025-04-12 01:00:00': 1, '2025-04-12 02:00:00': 1, '2025-04-12 03:00:00': 1, '2025-04-12 04:00:00': 1, '2025-04-12 05:00:00': 1, '2025-04-12 06:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 08:00:00': 1}, '北京时(UTC+8)': {'2025-01-01 08:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 09:00:00': 1, '2025-04-12 10:00:00': 1, '2025-04-12 11:00:00': 1, '2025-04-12 12:00:00': 1, '2025-04-12 13:00:00': 1, '2025-04-12 14:00:00': 1, '2025-04-12 15:00:00': 1, '2025-04-12 16:00:00': 1}, '备注': {'锦江区': 3648}}
样本数据:
  行1: {'经度(lon)': 104.1, '纬度(lat)': 30.6, '地面气压(hPa)': 969.59, '气温(℃)': 1.97, '降水量(mm)': 0.0, '露点温度(℃)': 1.16, '经向风速(V,m/s)': -1.8, '纬向风速(U,m/s)': -0.74, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2025-01-01 00:00:00', '北京时(UTC+8)': '2025-01-01 08:00:00', '备注': '锦江区'}
  行2: {'经度(lon)': 104.1, '纬度(lat)': 30.6, '地面气压(hPa)': 970.51, '气温(℃)': 3.5, '降水量(mm)': 0.0, '露点温度(℃)': 2.28, '经向风速(V,m/s)': -1.72, '纬向风速(U,m/s)': -0.58, '太阳辐射净强度(net,J/m2)': 128431.5, '太阳辐射总强度(down,J/m2)': 149075.5, '世界时(UTC)': '2025-01-01 01:00:00', '北京时(UTC+8)': '2025-01-01 09:00:00', '备注': '锦江区'}
  行3: {'经度(lon)': 104.1, '纬度(lat)': 30.6, '地面气压(hPa)': 971.25, '气温(℃)': 6.79, '降水量(mm)': 0.0, '露点温度(℃)': 3.37, '经向风速(V,m/s)': -1.96, '纬向风速(U,m/s)': -0.64, '太阳辐射净强度(net,J/m2)': 472263.0, '太阳辐射总强度(down,J/m2)': 548171.5, '世界时(UTC)': '2025-01-01 02:00:00', '北京时(UTC+8)': '2025-01-01 10:00:00', '备注': '锦江区'}

--------------------------------------------------------------------------------

文件名: 大邑24.xls
文件大小: 5.52 MB
解析区域: 大邑
解析年份: 2024
读取状态: 成功
数据形状: (8808, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('O'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'大邑县': 8808}, '世界时(UTC)': {'2024-09-01 12:00:00': 2, '2024-09-01 02:00:00': 2, '2024-09-01 22:00:00': 2, '2024-09-01 21:00:00': 2, '2024-09-01 20:00:00': 2, '2024-09-01 19:00:00': 2, '2024-09-01 18:00:00': 2, '2024-09-01 17:00:00': 2, '2024-09-01 16:00:00': 2, '2024-09-01 15:00:00': 2}, '北京时(UTC+8)': {'2024-09-01 20:00:00': 2, '2024-09-01 10:00:00': 2, '2024-09-02 06:00:00': 2, '2024-09-02 05:00:00': 2, '2024-09-02 04:00:00': 2, '2024-09-02 03:00:00': 2, '2024-09-02 02:00:00': 2, '2024-09-02 01:00:00': 2, '2024-09-02 00:00:00': 2, '2024-09-01 23:00:00': 2}, '雪层温度(℃)': {'*': 6710, '0.01': 590, '0.0': 213, '-0.01': 127, '-0.02': 118, '-0.03': 76, '-0.04': 56, '-0.06': 46, '-0.1': 42, '-0.07': 40}, '相对湿度(%)': {'88.0': 8, '82.09': 8, '72.2': 8, '78.2': 8, '79.18': 8, '85.7': 8, '85.62': 8, '89.67': 8, '82.25': 8, '89.41': 8}}
样本数据:
  行1: {'参考地名': '大邑县', '经度(lon)': 103.5, '纬度(lat)': 30.5, '世界时(UTC)': '2024-01-01 00:00:00', '北京时(UTC+8)': '2024-01-01 08:00:00', '海平面气压(hPa)': 1023.38, '地面气压(hPa)': 939.3, '气温2m(℃)': 6.68, '降水量(mm)': 0.024, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.02, '积雪密度(kg/m3)': 101.56, '雪层温度(℃)': '-0.03', '地表温度(℃)': 6.55, '露点温度(℃)': 6.04, '相对湿度(%)': '95.72', '蒸发量(mm)': -0.0021, '潜在蒸发量(mm)': -0.0009, '经向风速(V,m/s)': -0.18, '纬向风速(U,m/s)': 0.49, '最大阵风(上一小时内，m/s)': 1.82, '云底高度(m)': '41.47', '低层云量(lcc)': 0.88, '中层云量(mcc)': 0.79, '高层云量(hcc)': 0.76, '总云量(tcc)': 0.98, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.0248, '地表径流(mm)': 0.0021, '地下径流(mm)': 0.0226, '雷暴概率(TT，K)': 35.88, 'K指数(K)': 15.74, '对流可用位能(J/kg)': 0.0}
  行2: {'参考地名': '大邑县', '经度(lon)': 103.5, '纬度(lat)': 30.5, '世界时(UTC)': '2024-01-01 01:00:00', '北京时(UTC+8)': '2024-01-01 09:00:00', '海平面气压(hPa)': 1023.73, '地面气压(hPa)': 939.68, '气温2m(℃)': 6.75, '降水量(mm)': 0.059, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.02, '积雪密度(kg/m3)': 101.56, '雪层温度(℃)': '-0.47', '地表温度(℃)': 8.08, '露点温度(℃)': 5.87, '相对湿度(%)': '94.07', '蒸发量(mm)': -0.0036, '潜在蒸发量(mm)': -0.0026, '经向风速(V,m/s)': -0.12, '纬向风速(U,m/s)': 0.43, '最大阵风(上一小时内，m/s)': 2.15, '云底高度(m)': '66.2', '低层云量(lcc)': 0.76, '中层云量(mcc)': 0.75, '高层云量(hcc)': 0.76, '总云量(tcc)': 0.96, '总太阳辐射度(down,J/m2)': 83840.0, '净太阳辐射度(net,J/m2)': 72768.0, '直接辐射(J/m2)': 19136.0, '紫外强度(J/m2)': 5296.0, '径流(mm)': 0.0305, '地表径流(mm)': 0.0079, '地下径流(mm)': 0.0225, '雷暴概率(TT，K)': 35.94, 'K指数(K)': 16.68, '对流可用位能(J/kg)': 0.0}
  行3: {'参考地名': '大邑县', '经度(lon)': 103.5, '纬度(lat)': 30.5, '世界时(UTC)': '2024-01-01 02:00:00', '北京时(UTC+8)': '2024-01-01 10:00:00', '海平面气压(hPa)': 1024.13, '地面气压(hPa)': 940.05, '气温2m(℃)': 8.75, '降水量(mm)': 0.024, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.02, '积雪密度(kg/m3)': 101.56, '雪层温度(℃)': '0.01', '地表温度(℃)': 9.02, '露点温度(℃)': 6.85, '相对湿度(%)': '87.85', '蒸发量(mm)': -0.0378, '潜在蒸发量(mm)': -0.0284, '经向风速(V,m/s)': 0.48, '纬向风速(U,m/s)': 0.43, '最大阵风(上一小时内，m/s)': 2.85, '云底高度(m)': '166.22', '低层云量(lcc)': 0.84, '中层云量(mcc)': 0.77, '高层云量(hcc)': 0.73, '总云量(tcc)': 0.95, '总太阳辐射度(down,J/m2)': 311936.0, '净太阳辐射度(net,J/m2)': 280768.0, '直接辐射(J/m2)': 128448.0, '紫外强度(J/m2)': 35808.0, '径流(mm)': 0.0248, '地表径流(mm)': 0.0021, '地下径流(mm)': 0.0225, '雷暴概率(TT，K)': 36.14, 'K指数(K)': 17.38, '对流可用位能(J/kg)': 0.0}

--------------------------------------------------------------------------------

文件名: 邛崃25.xls
文件大小: 2.39 MB
解析区域: 邛崃
解析年份: 2025
读取状态: 成功
数据形状: (3648, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('float64'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'邛崃': 3648}, '世界时(UTC)': {'2025-01-01 00:00:00': 1, '2025-04-11 23:00:00': 1, '2025-04-12 01:00:00': 1, '2025-04-12 02:00:00': 1, '2025-04-12 03:00:00': 1, '2025-04-12 04:00:00': 1, '2025-04-12 05:00:00': 1, '2025-04-12 06:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 08:00:00': 1}, '北京时(UTC+8)': {'2025-01-01 08:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 09:00:00': 1, '2025-04-12 10:00:00': 1, '2025-04-12 11:00:00': 1, '2025-04-12 12:00:00': 1, '2025-04-12 13:00:00': 1, '2025-04-12 14:00:00': 1, '2025-04-12 15:00:00': 1, '2025-04-12 16:00:00': 1}, '雪层温度(℃)': {'*': 1606, '0.01': 857, '0.0': 258, '-0.01': 164, '-0.02': 92, '-0.03': 56, '-0.04': 37, '-0.05': 35, '-0.08': 33, '-0.06': 31}, '云底高度(m)': {'*': 122, '30.35': 4, '42.4': 4, '30.36': 3, '30.03': 3, '30.31': 3, '541.88': 2, '1450.47': 2, '29.78': 2, '695.18': 2}}
样本数据:
  行1: {'参考地名': '邛崃', '经度(lon)': 103.5, '纬度(lat)': 30.5, '世界时(UTC)': '2025-01-01 00:00:00', '北京时(UTC+8)': '2025-01-01 08:00:00', '海平面气压(hPa)': 1022.11, '地面气压(hPa)': 937.61, '气温2m(℃)': 2.89, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 1.41, '积雪密度(kg/m3)': 103.59, '雪层温度(℃)': '-0.56', '地表温度(℃)': -0.54, '露点温度(℃)': -0.68, '相对湿度(%)': 77.37, '蒸发量(mm)': 0.0005, '潜在蒸发量(mm)': 0.0009, '经向风速(V,m/s)': -0.59, '纬向风速(U,m/s)': -0.2, '最大阵风(上一小时内，m/s)': 2.23, '云底高度(m)': '933.62', '低层云量(lcc)': 0.31, '中层云量(mcc)': 0.27, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.37, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.0312, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0312, '雷暴概率(TT，K)': 35.59, 'K指数(K)': 1.23, '对流可用位能(J/kg)': 0.0}
  行2: {'参考地名': '邛崃', '经度(lon)': 103.5, '纬度(lat)': 30.5, '世界时(UTC)': '2025-01-01 01:00:00', '北京时(UTC+8)': '2025-01-01 09:00:00', '海平面气压(hPa)': 1023.15, '地面气压(hPa)': 938.49, '气温2m(℃)': 1.99, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 1.41, '积雪密度(kg/m3)': 103.59, '雪层温度(℃)': '-1.72', '地表温度(℃)': 3.1, '露点温度(℃)': -0.08, '相对湿度(%)': 86.15, '蒸发量(mm)': -0.0029, '潜在蒸发量(mm)': -0.0041, '经向风速(V,m/s)': -0.41, '纬向风速(U,m/s)': -0.05, '最大阵风(上一小时内，m/s)': 3.02, '云底高度(m)': '845.45', '低层云量(lcc)': 0.22, '中层云量(mcc)': 0.19, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.26, '总太阳辐射度(down,J/m2)': 154048.0, '净太阳辐射度(net,J/m2)': 130944.0, '直接辐射(J/m2)': 80256.0, '紫外强度(J/m2)': 17152.0, '径流(mm)': 0.0312, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0312, '雷暴概率(TT，K)': 35.94, 'K指数(K)': 0.88, '对流可用位能(J/kg)': 0.0}
  行3: {'参考地名': '邛崃', '经度(lon)': 103.5, '纬度(lat)': 30.5, '世界时(UTC)': '2025-01-01 02:00:00', '北京时(UTC+8)': '2025-01-01 10:00:00', '海平面气压(hPa)': 1024.06, '地面气压(hPa)': 939.33, '气温2m(℃)': 6.99, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 1.41, '积雪密度(kg/m3)': 103.59, '雪层温度(℃)': '-0.18', '地表温度(℃)': 7.96, '露点温度(℃)': 1.34, '相对湿度(%)': 67.25, '蒸发量(mm)': -0.0335, '潜在蒸发量(mm)': -0.0484, '经向风速(V,m/s)': -0.46, '纬向风速(U,m/s)': 0.12, '最大阵风(上一小时内，m/s)': 3.89, '云底高度(m)': '804.97', '低层云量(lcc)': 0.24, '中层云量(mcc)': 0.21, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.27, '总太阳辐射度(down,J/m2)': 630912.0, '净太阳辐射度(net,J/m2)': 540416.0, '直接辐射(J/m2)': 399744.0, '紫外强度(J/m2)': 70136.0, '径流(mm)': 0.0312, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0312, '雷暴概率(TT，K)': 36.15, 'K指数(K)': 1.0, '对流可用位能(J/kg)': 0.0}

--------------------------------------------------------------------------------

文件名: 金牛24.xls
文件大小: 2.36 MB
解析区域: 金牛
解析年份: 2024
读取状态: 成功
数据形状: (8784, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2024-01-01 00:00:00': 1, '2024-09-01 03:00:00': 1, '2024-08-31 21:00:00': 1, '2024-08-31 22:00:00': 1, '2024-08-31 23:00:00': 1, '2024-09-01 00:00:00': 1, '2024-09-01 01:00:00': 1, '2024-09-01 02:00:00': 1, '2024-09-01 04:00:00': 1, '2024-08-31 02:00:00': 1}, '北京时(UTC+8)': {'2024-01-01 08:00:00': 1, '2024-09-01 11:00:00': 1, '2024-09-01 05:00:00': 1, '2024-09-01 06:00:00': 1, '2024-09-01 07:00:00': 1, '2024-09-01 08:00:00': 1, '2024-09-01 09:00:00': 1, '2024-09-01 10:00:00': 1, '2024-09-01 12:00:00': 1, '2024-08-31 10:00:00': 1}, '备注': {'金牛区': 8784}}
样本数据:
  行1: {'经度(lon)': 104.0, '纬度(lat)': 30.7, '地面气压(hPa)': 964.25, '气温(℃)': 6.71, '降水量(mm)': 0.001, '露点温度(℃)': 6.16, '经向风速(V,m/s)': -0.39, '纬向风速(U,m/s)': 0.87, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2024-01-01 00:00:00', '北京时(UTC+8)': '2024-01-01 08:00:00', '备注': '金牛区'}
  行2: {'经度(lon)': 104.0, '纬度(lat)': 30.7, '地面气压(hPa)': 964.94, '气温(℃)': 7.19, '降水量(mm)': 0.001, '露点温度(℃)': 6.57, '经向风速(V,m/s)': -0.16, '纬向风速(U,m/s)': 0.81, '太阳辐射净强度(net,J/m2)': 65999.25, '太阳辐射总强度(down,J/m2)': 76405.25, '世界时(UTC)': '2024-01-01 01:00:00', '北京时(UTC+8)': '2024-01-01 09:00:00', '备注': '金牛区'}
  行3: {'经度(lon)': 104.0, '纬度(lat)': 30.7, '地面气压(hPa)': 965.65, '气温(℃)': 8.76, '降水量(mm)': 0.002, '露点温度(℃)': 6.78, '经向风速(V,m/s)': -0.03, '纬向风速(U,m/s)': 0.83, '太阳辐射净强度(net,J/m2)': 199878.77, '太阳辐射总强度(down,J/m2)': 231393.77, '世界时(UTC)': '2024-01-01 02:00:00', '北京时(UTC+8)': '2024-01-01 10:00:00', '备注': '金牛区'}

--------------------------------------------------------------------------------

文件名: 新津25.xls
文件大小: 1.02 MB
解析区域: 新津
解析年份: 2025
读取状态: 成功
数据形状: (3648, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2025-01-01 00:00:00': 1, '2025-04-11 23:00:00': 1, '2025-04-12 01:00:00': 1, '2025-04-12 02:00:00': 1, '2025-04-12 03:00:00': 1, '2025-04-12 04:00:00': 1, '2025-04-12 05:00:00': 1, '2025-04-12 06:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 08:00:00': 1}, '北京时(UTC+8)': {'2025-01-01 08:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 09:00:00': 1, '2025-04-12 10:00:00': 1, '2025-04-12 11:00:00': 1, '2025-04-12 12:00:00': 1, '2025-04-12 13:00:00': 1, '2025-04-12 14:00:00': 1, '2025-04-12 15:00:00': 1, '2025-04-12 16:00:00': 1}, '备注': {'新津区': 3648}}
样本数据:
  行1: {'经度(lon)': 103.8, '纬度(lat)': 30.4, '地面气压(hPa)': 960.19, '气温(℃)': 1.4, '降水量(mm)': 0.0, '露点温度(℃)': 0.73, '经向风速(V,m/s)': -1.65, '纬向风速(U,m/s)': -0.45, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2025-01-01 00:00:00', '北京时(UTC+8)': '2025-01-01 08:00:00', '备注': '新津区'}
  行2: {'经度(lon)': 103.8, '纬度(lat)': 30.4, '地面气压(hPa)': 961.18, '气温(℃)': 2.78, '降水量(mm)': 0.0, '露点温度(℃)': 1.8, '经向风速(V,m/s)': -1.51, '纬向风速(U,m/s)': -0.24, '太阳辐射净强度(net,J/m2)': 137424.0, '太阳辐射总强度(down,J/m2)': 157813.75, '世界时(UTC)': '2025-01-01 01:00:00', '北京时(UTC+8)': '2025-01-01 09:00:00', '备注': '新津区'}
  行3: {'经度(lon)': 103.8, '纬度(lat)': 30.4, '地面气压(hPa)': 962.02, '气温(℃)': 6.09, '降水量(mm)': 0.0, '露点温度(℃)': 2.92, '经向风速(V,m/s)': -1.84, '纬向风速(U,m/s)': -0.27, '太阳辐射净强度(net,J/m2)': 528229.0, '太阳辐射总强度(down,J/m2)': 606623.25, '世界时(UTC)': '2025-01-01 02:00:00', '北京时(UTC+8)': '2025-01-01 10:00:00', '备注': '新津区'}

--------------------------------------------------------------------------------

文件名: 崇州24.xls
文件大小: 5.52 MB
解析区域: 崇州
解析年份: 2024
读取状态: 成功
数据形状: (8808, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('float64'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'崇州': 8808}, '世界时(UTC)': {'2024-09-01 12:00:00': 2, '2024-09-01 02:00:00': 2, '2024-09-01 22:00:00': 2, '2024-09-01 21:00:00': 2, '2024-09-01 20:00:00': 2, '2024-09-01 19:00:00': 2, '2024-09-01 18:00:00': 2, '2024-09-01 17:00:00': 2, '2024-09-01 16:00:00': 2, '2024-09-01 15:00:00': 2}, '北京时(UTC+8)': {'2024-09-01 20:00:00': 2, '2024-09-01 10:00:00': 2, '2024-09-02 06:00:00': 2, '2024-09-02 05:00:00': 2, '2024-09-02 04:00:00': 2, '2024-09-02 03:00:00': 2, '2024-09-02 02:00:00': 2, '2024-09-02 01:00:00': 2, '2024-09-02 00:00:00': 2, '2024-09-01 23:00:00': 2}, '雪层温度(℃)': {'*': 8805, '-0.89': 1, '-0.29': 1, '-0.47': 1}, '云底高度(m)': {'*': 219, '30.43': 4, '799.29': 3, '480.61': 3, '30.46': 2, '788.48': 2, '976.7': 2, '979.14': 2, '4091.35': 2, '786.22': 2}}
样本数据:
  行1: {'参考地名': '崇州', '经度(lon)': 103.75, '纬度(lat)': 30.75, '世界时(UTC)': '2024-01-01 00:00:00', '北京时(UTC+8)': '2024-01-01 08:00:00', '海平面气压(hPa)': 1023.34, '地面气压(hPa)': 964.71, '气温2m(℃)': 7.29, '降水量(mm)': 0.035, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 6.98, '露点温度(℃)': 5.99, '相对湿度(%)': 91.47, '蒸发量(mm)': -0.0011, '潜在蒸发量(mm)': -0.0008, '经向风速(V,m/s)': -0.09, '纬向风速(U,m/s)': 0.69, '最大阵风(上一小时内，m/s)': 2.12, '云底高度(m)': '47.97', '低层云量(lcc)': 0.69, '中层云量(mcc)': 0.93, '高层云量(hcc)': 0.86, '总云量(tcc)': 1.0, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.0036, '地表径流(mm)': 0.0024, '地下径流(mm)': 0.001, '雷暴概率(TT，K)': 34.86, 'K指数(K)': 15.97, '对流可用位能(J/kg)': 0.0}
  行2: {'参考地名': '崇州', '经度(lon)': 103.75, '纬度(lat)': 30.75, '世界时(UTC)': '2024-01-01 01:00:00', '北京时(UTC+8)': '2024-01-01 09:00:00', '海平面气压(hPa)': 1023.63, '地面气压(hPa)': 965.02, '气温2m(℃)': 7.57, '降水量(mm)': 0.022, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 8.33, '露点温度(℃)': 6.21, '相对湿度(%)': 91.12, '蒸发量(mm)': -0.0016, '潜在蒸发量(mm)': -0.0018, '经向风速(V,m/s)': -0.26, '纬向风速(U,m/s)': 0.46, '最大阵风(上一小时内，m/s)': 1.94, '云底高度(m)': '70.2', '低层云量(lcc)': 0.52, '中层云量(mcc)': 0.91, '高层云量(hcc)': 0.85, '总云量(tcc)': 0.97, '总太阳辐射度(down,J/m2)': 78464.0, '净太阳辐射度(net,J/m2)': 66560.0, '直接辐射(J/m2)': 12416.0, '紫外强度(J/m2)': 7112.0, '径流(mm)': 0.0026, '地表径流(mm)': 0.0017, '地下径流(mm)': 0.0011, '雷暴概率(TT，K)': 35.21, 'K指数(K)': 17.06, '对流可用位能(J/kg)': 0.0}
  行3: {'参考地名': '崇州', '经度(lon)': 103.75, '纬度(lat)': 30.75, '世界时(UTC)': '2024-01-01 02:00:00', '北京时(UTC+8)': '2024-01-01 10:00:00', '海平面气压(hPa)': 1023.9, '地面气压(hPa)': 965.32, '气温2m(℃)': 10.04, '降水量(mm)': 0.021, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 9.46, '露点温度(℃)': 6.97, '相对湿度(%)': 81.22, '蒸发量(mm)': -0.022, '潜在蒸发量(mm)': -0.0207, '经向风速(V,m/s)': 0.33, '纬向风速(U,m/s)': 0.36, '最大阵风(上一小时内，m/s)': 2.21, '云底高度(m)': '292.97', '低层云量(lcc)': 0.72, '中层云量(mcc)': 0.91, '高层云量(hcc)': 0.93, '总云量(tcc)': 0.99, '总太阳辐射度(down,J/m2)': 255616.0, '净太阳辐射度(net,J/m2)': 225984.0, '直接辐射(J/m2)': 74560.0, '紫外强度(J/m2)': 23552.0, '径流(mm)': 0.0019, '地表径流(mm)': 0.0007, '地下径流(mm)': 0.0011, '雷暴概率(TT，K)': 35.55, 'K指数(K)': 17.97, '对流可用位能(J/kg)': 0.0}

--------------------------------------------------------------------------------

文件名: 崇州25.xls
文件大小: 2.39 MB
解析区域: 崇州
解析年份: 2025
读取状态: 成功
数据形状: (3648, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('float64'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'崇州': 3648}, '世界时(UTC)': {'2025-01-01 00:00:00': 1, '2025-04-11 23:00:00': 1, '2025-04-12 01:00:00': 1, '2025-04-12 02:00:00': 1, '2025-04-12 03:00:00': 1, '2025-04-12 04:00:00': 1, '2025-04-12 05:00:00': 1, '2025-04-12 06:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 08:00:00': 1}, '北京时(UTC+8)': {'2025-01-01 08:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 09:00:00': 1, '2025-04-12 10:00:00': 1, '2025-04-12 11:00:00': 1, '2025-04-12 12:00:00': 1, '2025-04-12 13:00:00': 1, '2025-04-12 14:00:00': 1, '2025-04-12 15:00:00': 1, '2025-04-12 16:00:00': 1}, '雪层温度(℃)': {'*': 3640, '0.01': 8}, '云底高度(m)': {'*': 186, '30.31': 4, '30.28': 3, '1921.3': 2, '976.49': 2, '361.73': 2, '471.61': 2, '176.21': 2, '987.74': 2, '1125.1': 2}}
样本数据:
  行1: {'参考地名': '崇州', '经度(lon)': 103.75, '纬度(lat)': 30.75, '世界时(UTC)': '2025-01-01 00:00:00', '北京时(UTC+8)': '2025-01-01 08:00:00', '海平面气压(hPa)': 1022.08, '地面气压(hPa)': 963.1, '气温2m(℃)': 1.66, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 0.15, '露点温度(℃)': 0.59, '相对湿度(%)': 92.58, '蒸发量(mm)': 0.0017, '潜在蒸发量(mm)': 0.002, '经向风速(V,m/s)': -0.63, '纬向风速(U,m/s)': -1.89, '最大阵风(上一小时内，m/s)': 3.13, '云底高度(m)': '1008.12', '低层云量(lcc)': 0.27, '中层云量(mcc)': 0.27, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.31, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.0017, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0017, '雷暴概率(TT，K)': 33.95, 'K指数(K)': 0.26, '对流可用位能(J/kg)': 0.0}
  行2: {'参考地名': '崇州', '经度(lon)': 103.75, '纬度(lat)': 30.75, '世界时(UTC)': '2025-01-01 01:00:00', '北京时(UTC+8)': '2025-01-01 09:00:00', '海平面气压(hPa)': 1023.21, '地面气压(hPa)': 964.09, '气温2m(℃)': 2.73, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 3.86, '露点温度(℃)': 1.03, '相对湿度(%)': 88.53, '蒸发量(mm)': -0.0036, '潜在蒸发量(mm)': -0.0049, '经向风速(V,m/s)': -0.64, '纬向风速(U,m/s)': -1.09, '最大阵风(上一小时内，m/s)': 3.46, '云底高度(m)': '650.95', '低层云量(lcc)': 0.16, '中层云量(mcc)': 0.13, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.18, '总太阳辐射度(down,J/m2)': 145152.0, '净太阳辐射度(net,J/m2)': 124672.0, '直接辐射(J/m2)': 77056.0, '紫外强度(J/m2)': 15680.0, '径流(mm)': 0.0017, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0018, '雷暴概率(TT，K)': 34.15, 'K指数(K)': 0.87, '对流可用位能(J/kg)': 0.0}
  行3: {'参考地名': '崇州', '经度(lon)': 103.75, '纬度(lat)': 30.75, '世界时(UTC)': '2025-01-01 02:00:00', '北京时(UTC+8)': '2025-01-01 10:00:00', '海平面气压(hPa)': 1024.16, '地面气压(hPa)': 964.94, '气温2m(℃)': 7.96, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 8.28, '露点温度(℃)': 3.17, '相对湿度(%)': 71.72, '蒸发量(mm)': -0.0322, '潜在蒸发量(mm)': -0.0448, '经向风速(V,m/s)': -0.63, '纬向风速(U,m/s)': -1.04, '最大阵风(上一小时内，m/s)': 4.41, '云底高度(m)': '573.72', '低层云量(lcc)': 0.34, '中层云量(mcc)': 0.2, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.39, '总太阳辐射度(down,J/m2)': 583104.0, '净太阳辐射度(net,J/m2)': 505088.0, '直接辐射(J/m2)': 355904.0, '紫外强度(J/m2)': 67168.0, '径流(mm)': 0.0017, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0018, '雷暴概率(TT，K)': 34.44, 'K指数(K)': 1.75, '对流可用位能(J/kg)': 0.0}

--------------------------------------------------------------------------------

文件名: 金牛25.xls
文件大小: 1.02 MB
解析区域: 金牛
解析年份: 2025
读取状态: 成功
数据形状: (3648, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2025-01-01 00:00:00': 1, '2025-04-11 23:00:00': 1, '2025-04-12 01:00:00': 1, '2025-04-12 02:00:00': 1, '2025-04-12 03:00:00': 1, '2025-04-12 04:00:00': 1, '2025-04-12 05:00:00': 1, '2025-04-12 06:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 08:00:00': 1}, '北京时(UTC+8)': {'2025-01-01 08:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 09:00:00': 1, '2025-04-12 10:00:00': 1, '2025-04-12 11:00:00': 1, '2025-04-12 12:00:00': 1, '2025-04-12 13:00:00': 1, '2025-04-12 14:00:00': 1, '2025-04-12 15:00:00': 1, '2025-04-12 16:00:00': 1}, '备注': {'金牛区': 3648}}
样本数据:
  行1: {'经度(lon)': 104.0, '纬度(lat)': 30.7, '地面气压(hPa)': 962.76, '气温(℃)': 1.89, '降水量(mm)': 0.0, '露点温度(℃)': 0.86, '经向风速(V,m/s)': -1.88, '纬向风速(U,m/s)': -0.88, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2025-01-01 00:00:00', '北京时(UTC+8)': '2025-01-01 08:00:00', '备注': '金牛区'}
  行2: {'经度(lon)': 104.0, '纬度(lat)': 30.7, '地面气压(hPa)': 963.71, '气温(℃)': 3.32, '降水量(mm)': 0.0, '露点温度(℃)': 1.87, '经向风速(V,m/s)': -1.78, '纬向风速(U,m/s)': -0.78, '太阳辐射净强度(net,J/m2)': 124036.25, '太阳辐射总强度(down,J/m2)': 143591.25, '世界时(UTC)': '2025-01-01 01:00:00', '北京时(UTC+8)': '2025-01-01 09:00:00', '备注': '金牛区'}
  行3: {'经度(lon)': 104.0, '纬度(lat)': 30.7, '地面气压(hPa)': 964.46, '气温(℃)': 6.26, '降水量(mm)': 0.0, '露点温度(℃)': 2.83, '经向风速(V,m/s)': -1.96, '纬向风速(U,m/s)': -0.8, '太阳辐射净强度(net,J/m2)': 463240.75, '太阳辐射总强度(down,J/m2)': 536278.25, '世界时(UTC)': '2025-01-01 02:00:00', '北京时(UTC+8)': '2025-01-01 10:00:00', '备注': '金牛区'}

--------------------------------------------------------------------------------

文件名: 新津24.xls
文件大小: 2.36 MB
解析区域: 新津
解析年份: 2024
读取状态: 成功
数据形状: (8784, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2024-01-01 00:00:00': 1, '2024-09-01 03:00:00': 1, '2024-08-31 21:00:00': 1, '2024-08-31 22:00:00': 1, '2024-08-31 23:00:00': 1, '2024-09-01 00:00:00': 1, '2024-09-01 01:00:00': 1, '2024-09-01 02:00:00': 1, '2024-09-01 04:00:00': 1, '2024-08-31 02:00:00': 1}, '北京时(UTC+8)': {'2024-01-01 08:00:00': 1, '2024-09-01 11:00:00': 1, '2024-09-01 05:00:00': 1, '2024-09-01 06:00:00': 1, '2024-09-01 07:00:00': 1, '2024-09-01 08:00:00': 1, '2024-09-01 09:00:00': 1, '2024-09-01 10:00:00': 1, '2024-09-01 12:00:00': 1, '2024-08-31 10:00:00': 1}, '备注': {'新津区': 8784}}
样本数据:
  行1: {'经度(lon)': 103.8, '纬度(lat)': 30.4, '地面气压(hPa)': 961.97, '气温(℃)': 7.28, '降水量(mm)': 0.017, '露点温度(℃)': 6.67, '经向风速(V,m/s)': -0.61, '纬向风速(U,m/s)': 0.65, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2024-01-01 00:00:00', '北京时(UTC+8)': '2024-01-01 08:00:00', '备注': '新津区'}
  行2: {'经度(lon)': 103.8, '纬度(lat)': 30.4, '地面气压(hPa)': 962.69, '气温(℃)': 7.59, '降水量(mm)': 0.018, '露点温度(℃)': 6.85, '经向风速(V,m/s)': -0.46, '纬向风速(U,m/s)': 0.75, '太阳辐射净强度(net,J/m2)': 66637.5, '太阳辐射总强度(down,J/m2)': 76524.75, '世界时(UTC)': '2024-01-01 01:00:00', '北京时(UTC+8)': '2024-01-01 09:00:00', '备注': '新津区'}
  行3: {'经度(lon)': 103.8, '纬度(lat)': 30.4, '地面气压(hPa)': 963.42, '气温(℃)': 8.81, '降水量(mm)': 0.025, '露点温度(℃)': 7.17, '经向风速(V,m/s)': -0.5, '纬向风速(U,m/s)': 0.94, '太阳辐射净强度(net,J/m2)': 232117.5, '太阳辐射总强度(down,J/m2)': 266573.25, '世界时(UTC)': '2024-01-01 02:00:00', '北京时(UTC+8)': '2024-01-01 10:00:00', '备注': '新津区'}

--------------------------------------------------------------------------------

文件名: 邛崃24.xls
文件大小: 5.52 MB
解析区域: 邛崃
解析年份: 2024
读取状态: 成功
数据形状: (8808, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('O'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'邛崃': 8808}, '世界时(UTC)': {'2024-09-01 12:00:00': 2, '2024-09-01 02:00:00': 2, '2024-09-01 22:00:00': 2, '2024-09-01 21:00:00': 2, '2024-09-01 20:00:00': 2, '2024-09-01 19:00:00': 2, '2024-09-01 18:00:00': 2, '2024-09-01 17:00:00': 2, '2024-09-01 16:00:00': 2, '2024-09-01 15:00:00': 2}, '北京时(UTC+8)': {'2024-09-01 20:00:00': 2, '2024-09-01 10:00:00': 2, '2024-09-02 06:00:00': 2, '2024-09-02 05:00:00': 2, '2024-09-02 04:00:00': 2, '2024-09-02 03:00:00': 2, '2024-09-02 02:00:00': 2, '2024-09-02 01:00:00': 2, '2024-09-02 00:00:00': 2, '2024-09-01 23:00:00': 2}, '雪层温度(℃)': {'*': 6710, '0.01': 590, '0.0': 213, '-0.01': 127, '-0.02': 118, '-0.03': 76, '-0.04': 56, '-0.06': 46, '-0.1': 42, '-0.07': 40}, '相对湿度(%)': {'88.0': 8, '82.09': 8, '72.2': 8, '78.2': 8, '79.18': 8, '85.7': 8, '85.62': 8, '89.67': 8, '82.25': 8, '89.41': 8}}
样本数据:
  行1: {'参考地名': '邛崃', '经度(lon)': 103.5, '纬度(lat)': 30.5, '世界时(UTC)': '2024-01-01 00:00:00', '北京时(UTC+8)': '2024-01-01 08:00:00', '海平面气压(hPa)': 1023.38, '地面气压(hPa)': 939.3, '气温2m(℃)': 6.68, '降水量(mm)': 0.024, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.02, '积雪密度(kg/m3)': 101.56, '雪层温度(℃)': '-0.03', '地表温度(℃)': 6.55, '露点温度(℃)': 6.04, '相对湿度(%)': '95.72', '蒸发量(mm)': -0.0021, '潜在蒸发量(mm)': -0.0009, '经向风速(V,m/s)': -0.18, '纬向风速(U,m/s)': 0.49, '最大阵风(上一小时内，m/s)': 1.82, '云底高度(m)': '41.47', '低层云量(lcc)': 0.88, '中层云量(mcc)': 0.79, '高层云量(hcc)': 0.76, '总云量(tcc)': 0.98, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.0248, '地表径流(mm)': 0.0021, '地下径流(mm)': 0.0226, '雷暴概率(TT，K)': 35.88, 'K指数(K)': 15.74, '对流可用位能(J/kg)': 0.0}
  行2: {'参考地名': '邛崃', '经度(lon)': 103.5, '纬度(lat)': 30.5, '世界时(UTC)': '2024-01-01 01:00:00', '北京时(UTC+8)': '2024-01-01 09:00:00', '海平面气压(hPa)': 1023.73, '地面气压(hPa)': 939.68, '气温2m(℃)': 6.75, '降水量(mm)': 0.059, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.02, '积雪密度(kg/m3)': 101.56, '雪层温度(℃)': '-0.47', '地表温度(℃)': 8.08, '露点温度(℃)': 5.87, '相对湿度(%)': '94.07', '蒸发量(mm)': -0.0036, '潜在蒸发量(mm)': -0.0026, '经向风速(V,m/s)': -0.12, '纬向风速(U,m/s)': 0.43, '最大阵风(上一小时内，m/s)': 2.15, '云底高度(m)': '66.2', '低层云量(lcc)': 0.76, '中层云量(mcc)': 0.75, '高层云量(hcc)': 0.76, '总云量(tcc)': 0.96, '总太阳辐射度(down,J/m2)': 83840.0, '净太阳辐射度(net,J/m2)': 72768.0, '直接辐射(J/m2)': 19136.0, '紫外强度(J/m2)': 5296.0, '径流(mm)': 0.0305, '地表径流(mm)': 0.0079, '地下径流(mm)': 0.0225, '雷暴概率(TT，K)': 35.94, 'K指数(K)': 16.68, '对流可用位能(J/kg)': 0.0}
  行3: {'参考地名': '邛崃', '经度(lon)': 103.5, '纬度(lat)': 30.5, '世界时(UTC)': '2024-01-01 02:00:00', '北京时(UTC+8)': '2024-01-01 10:00:00', '海平面气压(hPa)': 1024.13, '地面气压(hPa)': 940.05, '气温2m(℃)': 8.75, '降水量(mm)': 0.024, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.02, '积雪密度(kg/m3)': 101.56, '雪层温度(℃)': '0.01', '地表温度(℃)': 9.02, '露点温度(℃)': 6.85, '相对湿度(%)': '87.85', '蒸发量(mm)': -0.0378, '潜在蒸发量(mm)': -0.0284, '经向风速(V,m/s)': 0.48, '纬向风速(U,m/s)': 0.43, '最大阵风(上一小时内，m/s)': 2.85, '云底高度(m)': '166.22', '低层云量(lcc)': 0.84, '中层云量(mcc)': 0.77, '高层云量(hcc)': 0.73, '总云量(tcc)': 0.95, '总太阳辐射度(down,J/m2)': 311936.0, '净太阳辐射度(net,J/m2)': 280768.0, '直接辐射(J/m2)': 128448.0, '紫外强度(J/m2)': 35808.0, '径流(mm)': 0.0248, '地表径流(mm)': 0.0021, '地下径流(mm)': 0.0225, '雷暴概率(TT，K)': 36.14, 'K指数(K)': 17.38, '对流可用位能(J/kg)': 0.0}

--------------------------------------------------------------------------------

文件名: 大邑23.xls
文件大小: 5.49 MB
解析区域: 大邑
解析年份: 2023
读取状态: 成功
数据形状: (8760, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('O'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'大邑县': 8760}, '世界时(UTC)': {'2023-01-01 00:00:00': 1, '2023-09-01 10:00:00': 1, '2023-09-01 04:00:00': 1, '2023-09-01 05:00:00': 1, '2023-09-01 06:00:00': 1, '2023-09-01 07:00:00': 1, '2023-09-01 08:00:00': 1, '2023-09-01 09:00:00': 1, '2023-09-01 11:00:00': 1, '2023-09-03 05:00:00': 1}, '北京时(UTC+8)': {'2023-01-01 08:00:00': 1, '2023-09-01 18:00:00': 1, '2023-09-01 12:00:00': 1, '2023-09-01 13:00:00': 1, '2023-09-01 14:00:00': 1, '2023-09-01 15:00:00': 1, '2023-09-01 16:00:00': 1, '2023-09-01 17:00:00': 1, '2023-09-01 19:00:00': 1, '2023-09-03 13:00:00': 1}, '雪层温度(℃)': {'*': 6995, '0.01': 539, '0.0': 115, '-0.01': 67, '-0.02': 59, '-0.05': 46, '-0.03': 38, '-0.07': 33, '-0.06': 33, '-0.04': 25}, '相对湿度(%)': {'78.03': 7, '79.97': 7, '91.58': 7, '67.13': 7, '81.62': 7, '88.14': 7, '90.14': 7, '88.53': 7, '78.43': 7, '76.77': 7}}
样本数据:
  行1: {'参考地名': '大邑县', '经度(lon)': 103.5, '纬度(lat)': 30.5, '世界时(UTC)': '2023-01-01 00:00:00', '北京时(UTC+8)': '2023-01-01 08:00:00', '海平面气压(hPa)': 1029.92, '地面气压(hPa)': 944.22, '气温2m(℃)': 4.01, '降水量(mm)': 0.005, '降雪量(mm)': 0.002, '积雪深度(mm of water equivalent)': 0.69, '积雪密度(kg/m3)': 101.7, '雪层温度(℃)': '-0.07', '地表温度(℃)': 3.17, '露点温度(℃)': 3.67, '相对湿度(%)': '97.61', '蒸发量(mm)': -0.0003, '潜在蒸发量(mm)': 0.0, '经向风速(V,m/s)': -0.05, '纬向风速(U,m/s)': 0.64, '最大阵风(上一小时内，m/s)': 1.19, '云底高度(m)': '45.17', '低层云量(lcc)': 0.75, '中层云量(mcc)': 0.3, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.76, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.0296, '地表径流(mm)': 0.0005, '地下径流(mm)': 0.0291, '雷暴概率(TT，K)': 25.18, 'K指数(K)': 2.64, '对流可用位能(J/kg)': 0.0}
  行2: {'参考地名': '大邑县', '经度(lon)': 103.5, '纬度(lat)': 30.5, '世界时(UTC)': '2023-01-01 01:00:00', '北京时(UTC+8)': '2023-01-01 09:00:00', '海平面气压(hPa)': 1030.94, '地面气压(hPa)': 945.09, '气温2m(℃)': 3.76, '降水量(mm)': 0.005, '降雪量(mm)': 0.001, '积雪深度(mm of water equivalent)': 0.69, '积雪密度(kg/m3)': 101.7, '雪层温度(℃)': '-0.08', '地表温度(℃)': 4.52, '露点温度(℃)': 3.55, '相对湿度(%)': '98.57', '蒸发量(mm)': -0.0014, '潜在蒸发量(mm)': -0.0011, '经向风速(V,m/s)': -0.13, '纬向风速(U,m/s)': 0.68, '最大阵风(上一小时内，m/s)': 1.84, '云底高度(m)': '58.46', '低层云量(lcc)': 0.82, '中层云量(mcc)': 0.22, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.83, '总太阳辐射度(down,J/m2)': 90816.0, '净太阳辐射度(net,J/m2)': 76864.0, '直接辐射(J/m2)': 26048.0, '紫外强度(J/m2)': 14904.0, '径流(mm)': 0.0296, '地表径流(mm)': 0.0005, '地下径流(mm)': 0.0291, '雷暴概率(TT，K)': 24.93, 'K指数(K)': 2.32, '对流可用位能(J/kg)': 0.12}
  行3: {'参考地名': '大邑县', '经度(lon)': 103.5, '纬度(lat)': 30.5, '世界时(UTC)': '2023-01-01 02:00:00', '北京时(UTC+8)': '2023-01-01 10:00:00', '海平面气压(hPa)': 1031.19, '地面气压(hPa)': 945.35, '气温2m(℃)': 5.13, '降水量(mm)': 0.004, '降雪量(mm)': 0.001, '积雪深度(mm of water equivalent)': 0.69, '积雪密度(kg/m3)': 101.71, '雪层温度(℃)': '-0.05', '地表温度(℃)': 6.17, '露点温度(℃)': 3.88, '相对湿度(%)': '91.6', '蒸发量(mm)': -0.0344, '潜在蒸发量(mm)': -0.0278, '经向风速(V,m/s)': 0.21, '纬向风速(U,m/s)': 0.64, '最大阵风(上一小时内，m/s)': 2.92, '云底高度(m)': '154.04', '低层云量(lcc)': 0.45, '中层云量(mcc)': 0.25, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.53, '总太阳辐射度(down,J/m2)': 431680.0, '净太阳辐射度(net,J/m2)': 368704.0, '直接辐射(J/m2)': 169088.0, '紫外强度(J/m2)': 57296.0, '径流(mm)': 0.0291, '地表径流(mm)': 0.0002, '地下径流(mm)': 0.0288, '雷暴概率(TT，K)': 24.74, 'K指数(K)': 2.04, '对流可用位能(J/kg)': 1.06}

--------------------------------------------------------------------------------

文件名: 甘孜24.xls
文件大小: 3.17 MB
解析区域: 甘孜
解析年份: 2024
读取状态: 成功
数据形状: (8808, 18)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '相对湿度(%)', '地表温度(℃)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '相对湿度(%)': dtype('O'), '地表温度(℃)': dtype('float64'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64')}
唯一值统计: {'参考地名': {'甘孜': 8808}, '世界时(UTC)': {'2024-09-01 12:00:00': 2, '2024-09-01 02:00:00': 2, '2024-09-01 22:00:00': 2, '2024-09-01 21:00:00': 2, '2024-09-01 20:00:00': 2, '2024-09-01 19:00:00': 2, '2024-09-01 18:00:00': 2, '2024-09-01 17:00:00': 2, '2024-09-01 16:00:00': 2, '2024-09-01 15:00:00': 2}, '北京时(UTC+8)': {'2024-09-01 20:00:00': 2, '2024-09-01 10:00:00': 2, '2024-09-02 06:00:00': 2, '2024-09-02 05:00:00': 2, '2024-09-02 04:00:00': 2, '2024-09-02 03:00:00': 2, '2024-09-02 02:00:00': 2, '2024-09-02 01:00:00': 2, '2024-09-02 00:00:00': 2, '2024-09-01 23:00:00': 2}, '相对湿度(%)': {'88.36': 9, '93.33': 8, '94.33': 7, '75.01': 7, '83.97': 7, '69.75': 7, '68.67': 7, '56.96': 6, '93.74': 6, '86.62': 6}}
样本数据:
  行1: {'参考地名': '甘孜', '经度(lon)': 100.0, '纬度(lat)': 31.5, '世界时(UTC)': '2024-01-01 00:00:00', '北京时(UTC+8)': '2024-01-01 08:00:00', '海平面气压(hPa)': 1018.16, '地面气压(hPa)': 604.38, '气温2m(℃)': -18.27, '降水量(mm)': 0.0, '相对湿度(%)': '58.02', '地表温度(℃)': -23.87, '蒸发量(mm)': 0.0003, '潜在蒸发量(mm)': -0.001, '经向风速(V,m/s)': 1.28, '纬向风速(U,m/s)': 1.56, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0}
  行2: {'参考地名': '甘孜', '经度(lon)': 100.0, '纬度(lat)': 31.5, '世界时(UTC)': '2024-01-01 01:00:00', '北京时(UTC+8)': '2024-01-01 09:00:00', '海平面气压(hPa)': 1019.41, '地面气压(hPa)': 604.82, '气温2m(℃)': -18.18, '降水量(mm)': 0.0, '相对湿度(%)': '56.12', '地表温度(℃)': -21.02, '蒸发量(mm)': 0.0002, '潜在蒸发量(mm)': -0.0031, '经向风速(V,m/s)': 1.09, '纬向风速(U,m/s)': 1.67, '总太阳辐射度(down,J/m2)': 83200.0, '净太阳辐射度(net,J/m2)': 54400.0, '直接辐射(J/m2)': 39680.0}
  行3: {'参考地名': '甘孜', '经度(lon)': 100.0, '纬度(lat)': 31.5, '世界时(UTC)': '2024-01-01 02:00:00', '北京时(UTC+8)': '2024-01-01 10:00:00', '海平面气压(hPa)': 1020.82, '地面气压(hPa)': 605.32, '气温2m(℃)': -10.75, '降水量(mm)': 0.0, '相对湿度(%)': '37.63', '地表温度(℃)': -11.99, '蒸发量(mm)': -0.0013, '潜在蒸发量(mm)': -0.0269, '经向风速(V,m/s)': 0.58, '纬向风速(U,m/s)': 1.25, '总太阳辐射度(down,J/m2)': 667328.0, '净太阳辐射度(net,J/m2)': 436992.0, '直接辐射(J/m2)': 462080.0}

--------------------------------------------------------------------------------

文件名: 温江25.xls
文件大小: 1.02 MB
解析区域: 温江
解析年份: 2025
读取状态: 成功
数据形状: (3648, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2025-01-01 00:00:00': 1, '2025-04-11 23:00:00': 1, '2025-04-12 01:00:00': 1, '2025-04-12 02:00:00': 1, '2025-04-12 03:00:00': 1, '2025-04-12 04:00:00': 1, '2025-04-12 05:00:00': 1, '2025-04-12 06:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 08:00:00': 1}, '北京时(UTC+8)': {'2025-01-01 08:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 09:00:00': 1, '2025-04-12 10:00:00': 1, '2025-04-12 11:00:00': 1, '2025-04-12 12:00:00': 1, '2025-04-12 13:00:00': 1, '2025-04-12 14:00:00': 1, '2025-04-12 15:00:00': 1, '2025-04-12 16:00:00': 1}, '备注': {'温江区': 3648}}
样本数据:
  行1: {'经度(lon)': 103.8, '纬度(lat)': 30.7, '地面气压(hPa)': 949.38, '气温(℃)': 0.98, '降水量(mm)': 0.0, '露点温度(℃)': -0.19, '经向风速(V,m/s)': -1.64, '纬向风速(U,m/s)': -0.96, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2025-01-01 00:00:00', '北京时(UTC+8)': '2025-01-01 08:00:00', '备注': '温江区'}
  行2: {'经度(lon)': 103.8, '纬度(lat)': 30.7, '地面气压(hPa)': 950.36, '气温(℃)': 2.24, '降水量(mm)': 0.0, '露点温度(℃)': 0.95, '经向风速(V,m/s)': -1.53, '纬向风速(U,m/s)': -0.83, '太阳辐射净强度(net,J/m2)': 124902.75, '太阳辐射总强度(down,J/m2)': 145835.5, '世界时(UTC)': '2025-01-01 01:00:00', '北京时(UTC+8)': '2025-01-01 09:00:00', '备注': '温江区'}
  行3: {'经度(lon)': 103.8, '纬度(lat)': 30.7, '地面气压(hPa)': 951.23, '气温(℃)': 5.73, '降水量(mm)': 0.0, '露点温度(℃)': 2.37, '经向风速(V,m/s)': -1.55, '纬向风速(U,m/s)': -0.81, '太阳辐射净强度(net,J/m2)': 490317.25, '太阳辐射总强度(down,J/m2)': 572489.0, '世界时(UTC)': '2025-01-01 02:00:00', '北京时(UTC+8)': '2025-01-01 10:00:00', '备注': '温江区'}

--------------------------------------------------------------------------------

文件名: 温江24.xls
文件大小: 2.36 MB
解析区域: 温江
解析年份: 2024
读取状态: 成功
数据形状: (8784, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2024-01-01 00:00:00': 1, '2024-09-01 03:00:00': 1, '2024-08-31 21:00:00': 1, '2024-08-31 22:00:00': 1, '2024-08-31 23:00:00': 1, '2024-09-01 00:00:00': 1, '2024-09-01 01:00:00': 1, '2024-09-01 02:00:00': 1, '2024-09-01 04:00:00': 1, '2024-08-31 02:00:00': 1}, '北京时(UTC+8)': {'2024-01-01 08:00:00': 1, '2024-09-01 11:00:00': 1, '2024-09-01 05:00:00': 1, '2024-09-01 06:00:00': 1, '2024-09-01 07:00:00': 1, '2024-09-01 08:00:00': 1, '2024-09-01 09:00:00': 1, '2024-09-01 10:00:00': 1, '2024-09-01 12:00:00': 1, '2024-08-31 10:00:00': 1}, '备注': {'温江区': 8784}}
样本数据:
  行1: {'经度(lon)': 103.8, '纬度(lat)': 30.7, '地面气压(hPa)': 951.04, '气温(℃)': 6.68, '降水量(mm)': 0.024, '露点温度(℃)': 5.9, '经向风速(V,m/s)': -0.77, '纬向风速(U,m/s)': 0.62, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2024-01-01 00:00:00', '北京时(UTC+8)': '2024-01-01 08:00:00', '备注': '温江区'}
  行2: {'经度(lon)': 103.8, '纬度(lat)': 30.7, '地面气压(hPa)': 951.74, '气温(℃)': 7.1, '降水量(mm)': 0.014, '露点温度(℃)': 6.11, '经向风速(V,m/s)': -0.49, '纬向风速(U,m/s)': 0.65, '太阳辐射净强度(net,J/m2)': 67595.5, '太阳辐射总强度(down,J/m2)': 78924.0, '世界时(UTC)': '2024-01-01 01:00:00', '北京时(UTC+8)': '2024-01-01 09:00:00', '备注': '温江区'}
  行3: {'经度(lon)': 103.8, '纬度(lat)': 30.7, '地面气压(hPa)': 952.49, '气温(℃)': 8.75, '降水量(mm)': 0.012, '露点温度(℃)': 6.72, '经向风速(V,m/s)': -0.2, '纬向风速(U,m/s)': 0.65, '太阳辐射净强度(net,J/m2)': 215548.0, '太阳辐射总强度(down,J/m2)': 251671.0, '世界时(UTC)': '2024-01-01 02:00:00', '北京时(UTC+8)': '2024-01-01 10:00:00', '备注': '温江区'}

--------------------------------------------------------------------------------

文件名: 甘孜25.xls
文件大小: 1.39 MB
解析区域: 甘孜
解析年份: 2025
读取状态: 成功
数据形状: (3648, 18)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '相对湿度(%)', '地表温度(℃)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '相对湿度(%)': dtype('O'), '地表温度(℃)': dtype('float64'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64')}
唯一值统计: {'参考地名': {'甘孜': 3648}, '世界时(UTC)': {'2025-01-01 00:00:00': 1, '2025-04-11 23:00:00': 1, '2025-04-12 01:00:00': 1, '2025-04-12 02:00:00': 1, '2025-04-12 03:00:00': 1, '2025-04-12 04:00:00': 1, '2025-04-12 05:00:00': 1, '2025-04-12 06:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 08:00:00': 1}, '北京时(UTC+8)': {'2025-01-01 08:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 09:00:00': 1, '2025-04-12 10:00:00': 1, '2025-04-12 11:00:00': 1, '2025-04-12 12:00:00': 1, '2025-04-12 13:00:00': 1, '2025-04-12 14:00:00': 1, '2025-04-12 15:00:00': 1, '2025-04-12 16:00:00': 1}, '相对湿度(%)': {'92.92': 6, '86.98': 5, '91.8': 4, '*': 4, '77.0': 4, '84.93': 4, '92.46': 4, '72.09': 4, '90.07': 4, '77.49': 4}}
样本数据:
  行1: {'参考地名': '甘孜', '经度(lon)': 100.0, '纬度(lat)': 31.5, '世界时(UTC)': '2025-01-01 00:00:00', '北京时(UTC+8)': '2025-01-01 08:00:00', '海平面气压(hPa)': 1019.11, '地面气压(hPa)': 604.12, '气温2m(℃)': -20.44, '降水量(mm)': 0.0, '相对湿度(%)': '80.08', '地表温度(℃)': -25.17, '蒸发量(mm)': 0.0005, '潜在蒸发量(mm)': -0.0006, '经向风速(V,m/s)': 1.69, '纬向风速(U,m/s)': 1.46, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0}
  行2: {'参考地名': '甘孜', '经度(lon)': 100.0, '纬度(lat)': 31.5, '世界时(UTC)': '2025-01-01 01:00:00', '北京时(UTC+8)': '2025-01-01 09:00:00', '海平面气压(hPa)': 1020.08, '地面气压(hPa)': 604.62, '气温2m(℃)': -22.92, '降水量(mm)': 0.0, '相对湿度(%)': '81.1', '地表温度(℃)': -21.44, '蒸发量(mm)': 0.0004, '潜在蒸发量(mm)': -0.002, '经向风速(V,m/s)': 1.42, '纬向风速(U,m/s)': 1.39, '总太阳辐射度(down,J/m2)': 89536.0, '净太阳辐射度(net,J/m2)': 59648.0, '直接辐射(J/m2)': 45120.0}
  行3: {'参考地名': '甘孜', '经度(lon)': 100.0, '纬度(lat)': 31.5, '世界时(UTC)': '2025-01-01 02:00:00', '北京时(UTC+8)': '2025-01-01 10:00:00', '海平面气压(hPa)': 1020.54, '地面气压(hPa)': 604.86, '气温2m(℃)': -15.66, '降水量(mm)': 0.0, '相对湿度(%)': '64.04', '地表温度(℃)': -12.96, '蒸发量(mm)': -0.0009, '潜在蒸发量(mm)': -0.0224, '经向风速(V,m/s)': 1.49, '纬向风速(U,m/s)': 1.2, '总太阳辐射度(down,J/m2)': 684416.0, '净太阳辐射度(net,J/m2)': 455424.0, '直接辐射(J/m2)': 451136.0}

--------------------------------------------------------------------------------

文件名: 锦江23.xls
文件大小: 2.36 MB
解析区域: 锦江
解析年份: 2023
读取状态: 成功
数据形状: (8760, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2023-01-01 00:00:00': 1, '2023-09-01 10:00:00': 1, '2023-09-01 04:00:00': 1, '2023-09-01 05:00:00': 1, '2023-09-01 06:00:00': 1, '2023-09-01 07:00:00': 1, '2023-09-01 08:00:00': 1, '2023-09-01 09:00:00': 1, '2023-09-01 11:00:00': 1, '2023-09-03 05:00:00': 1}, '北京时(UTC+8)': {'2023-01-01 08:00:00': 1, '2023-09-01 18:00:00': 1, '2023-09-01 12:00:00': 1, '2023-09-01 13:00:00': 1, '2023-09-01 14:00:00': 1, '2023-09-01 15:00:00': 1, '2023-09-01 16:00:00': 1, '2023-09-01 17:00:00': 1, '2023-09-01 19:00:00': 1, '2023-09-03 13:00:00': 1}, '备注': {'锦江区': 8760}}
样本数据:
  行1: {'经度(lon)': 104.1, '纬度(lat)': 30.6, '地面气压(hPa)': 976.89, '气温(℃)': 3.37, '降水量(mm)': 0.002, '露点温度(℃)': 3.28, '经向风速(V,m/s)': -0.17, '纬向风速(U,m/s)': 0.61, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2023-01-01 00:00:00', '北京时(UTC+8)': '2023-01-01 08:00:00', '备注': '锦江区'}
  行2: {'经度(lon)': 104.1, '纬度(lat)': 30.6, '地面气压(hPa)': 977.6, '气温(℃)': 4.05, '降水量(mm)': 0.001, '露点温度(℃)': 3.78, '经向风速(V,m/s)': -0.06, '纬向风速(U,m/s)': 0.63, '太阳辐射净强度(net,J/m2)': 76352.25, '太阳辐射总强度(down,J/m2)': 88625.25, '世界时(UTC)': '2023-01-01 01:00:00', '北京时(UTC+8)': '2023-01-01 09:00:00', '备注': '锦江区'}
  行3: {'经度(lon)': 104.1, '纬度(lat)': 30.6, '地面气压(hPa)': 978.09, '气温(℃)': 5.05, '降水量(mm)': 0.001, '露点温度(℃)': 4.4, '经向风速(V,m/s)': -0.16, '纬向风速(U,m/s)': 0.96, '太阳辐射净强度(net,J/m2)': 287272.25, '太阳辐射总强度(down,J/m2)': 333444.75, '世界时(UTC)': '2023-01-01 02:00:00', '北京时(UTC+8)': '2023-01-01 10:00:00', '备注': '锦江区'}

--------------------------------------------------------------------------------

文件名: 郫都24.xls
文件大小: 2.36 MB
解析区域: 郫都
解析年份: 2024
读取状态: 成功
数据形状: (8784, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2024-01-01 00:00:00': 1, '2024-09-01 03:00:00': 1, '2024-08-31 21:00:00': 1, '2024-08-31 22:00:00': 1, '2024-08-31 23:00:00': 1, '2024-09-01 00:00:00': 1, '2024-09-01 01:00:00': 1, '2024-09-01 02:00:00': 1, '2024-09-01 04:00:00': 1, '2024-08-31 02:00:00': 1}, '北京时(UTC+8)': {'2024-01-01 08:00:00': 1, '2024-09-01 11:00:00': 1, '2024-09-01 05:00:00': 1, '2024-09-01 06:00:00': 1, '2024-09-01 07:00:00': 1, '2024-09-01 08:00:00': 1, '2024-09-01 09:00:00': 1, '2024-09-01 10:00:00': 1, '2024-09-01 12:00:00': 1, '2024-08-31 10:00:00': 1}, '备注': {'郫都区': 8784}}
样本数据:
  行1: {'经度(lon)': 103.9, '纬度(lat)': 30.8, '地面气压(hPa)': 945.17, '气温(℃)': 6.23, '降水量(mm)': 0.012, '露点温度(℃)': 5.33, '经向风速(V,m/s)': -0.52, '纬向风速(U,m/s)': 0.78, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2024-01-01 00:00:00', '北京时(UTC+8)': '2024-01-01 08:00:00', '备注': '郫都区'}
  行2: {'经度(lon)': 103.9, '纬度(lat)': 30.8, '地面气压(hPa)': 945.85, '气温(℃)': 6.68, '降水量(mm)': 0.012, '露点温度(℃)': 5.58, '经向风速(V,m/s)': -0.26, '纬向风速(U,m/s)': 0.74, '太阳辐射净强度(net,J/m2)': 65300.0, '太阳辐射总强度(down,J/m2)': 75665.25, '世界时(UTC)': '2024-01-01 01:00:00', '北京时(UTC+8)': '2024-01-01 09:00:00', '备注': '郫都区'}
  行3: {'经度(lon)': 103.9, '纬度(lat)': 30.8, '地面气压(hPa)': 946.59, '气温(℃)': 8.28, '降水量(mm)': 0.015, '露点温度(℃)': 6.19, '经向风速(V,m/s)': 0.0, '纬向风速(U,m/s)': 0.67, '太阳辐射净强度(net,J/m2)': 207319.5, '太阳辐射总强度(down,J/m2)': 240234.23, '世界时(UTC)': '2024-01-01 02:00:00', '北京时(UTC+8)': '2024-01-01 10:00:00', '备注': '郫都区'}

--------------------------------------------------------------------------------

文件名: 武侯25.xls
文件大小: 1.02 MB
解析区域: 武侯
解析年份: 2025
读取状态: 成功
数据形状: (3648, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2025-01-01 00:00:00': 1, '2025-04-11 23:00:00': 1, '2025-04-12 01:00:00': 1, '2025-04-12 02:00:00': 1, '2025-04-12 03:00:00': 1, '2025-04-12 04:00:00': 1, '2025-04-12 05:00:00': 1, '2025-04-12 06:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 08:00:00': 1}, '北京时(UTC+8)': {'2025-01-01 08:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 09:00:00': 1, '2025-04-12 10:00:00': 1, '2025-04-12 11:00:00': 1, '2025-04-12 12:00:00': 1, '2025-04-12 13:00:00': 1, '2025-04-12 14:00:00': 1, '2025-04-12 15:00:00': 1, '2025-04-12 16:00:00': 1}, '备注': {'武侯区': 3648}}
样本数据:
  行1: {'经度(lon)': 104.0, '纬度(lat)': 30.6, '地面气压(hPa)': 965.65, '气温(℃)': 1.97, '降水量(mm)': 0.0, '露点温度(℃)': 1.15, '经向风速(V,m/s)': -1.81, '纬向风速(U,m/s)': -0.8, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2025-01-01 00:00:00', '北京时(UTC+8)': '2025-01-01 08:00:00', '备注': '武侯区'}
  行2: {'经度(lon)': 104.0, '纬度(lat)': 30.6, '地面气压(hPa)': 966.58, '气温(℃)': 3.43, '降水量(mm)': 0.0, '露点温度(℃)': 2.17, '经向风速(V,m/s)': -1.72, '纬向风速(U,m/s)': -0.62, '太阳辐射净强度(net,J/m2)': 128029.0, '太阳辐射总强度(down,J/m2)': 147928.75, '世界时(UTC)': '2025-01-01 01:00:00', '北京时(UTC+8)': '2025-01-01 09:00:00', '备注': '武侯区'}
  行3: {'经度(lon)': 104.0, '纬度(lat)': 30.6, '地面气压(hPa)': 967.32, '气温(℃)': 6.44, '降水量(mm)': 0.0, '露点温度(℃)': 3.12, '经向风速(V,m/s)': -1.99, '纬向风速(U,m/s)': -0.64, '太阳辐射净强度(net,J/m2)': 474628.0, '太阳辐射总强度(down,J/m2)': 548394.25, '世界时(UTC)': '2025-01-01 02:00:00', '北京时(UTC+8)': '2025-01-01 10:00:00', '备注': '武侯区'}

--------------------------------------------------------------------------------

文件名: 青羊区24.xls
文件大小: 5.52 MB
解析区域: 青羊区
解析年份: 2024
读取状态: 成功
数据形状: (8808, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('O'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'青羊区': 8808}, '世界时(UTC)': {'2024-09-01 12:00:00': 2, '2024-09-01 02:00:00': 2, '2024-09-01 22:00:00': 2, '2024-09-01 21:00:00': 2, '2024-09-01 20:00:00': 2, '2024-09-01 19:00:00': 2, '2024-09-01 18:00:00': 2, '2024-09-01 17:00:00': 2, '2024-09-01 16:00:00': 2, '2024-09-01 15:00:00': 2}, '北京时(UTC+8)': {'2024-09-01 20:00:00': 2, '2024-09-01 10:00:00': 2, '2024-09-02 06:00:00': 2, '2024-09-02 05:00:00': 2, '2024-09-02 04:00:00': 2, '2024-09-02 03:00:00': 2, '2024-09-02 02:00:00': 2, '2024-09-02 01:00:00': 2, '2024-09-02 00:00:00': 2, '2024-09-01 23:00:00': 2}, '雪层温度(℃)': {'*': 8765, '0.01': 11, '-0.12': 3, '-0.15': 2, '-0.22': 2, '-6.3': 1, '-6.04': 1, '-5.26': 1, '-4.08': 1, '-3.47': 1}, '相对湿度(%)': {'92.75': 9, '90.3': 8, '86.75': 8, '91.45': 8, '91.41': 8, '91.35': 7, '95.01': 7, '79.86': 7, '86.57': 7, '89.85': 7}}
样本数据:
  行1: {'参考地名': '青羊区', '经度(lon)': 104.0, '纬度(lat)': 30.75, '世界时(UTC)': '2024-01-01 00:00:00', '北京时(UTC+8)': '2024-01-01 08:00:00', '海平面气压(hPa)': 1023.22, '地面气压(hPa)': 957.33, '气温2m(℃)': 6.71, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 7.17, '露点温度(℃)': 6.08, '相对湿度(%)': '95.76', '蒸发量(mm)': -0.0011, '潜在蒸发量(mm)': -0.0015, '经向风速(V,m/s)': 0.45, '纬向风速(U,m/s)': 1.65, '最大阵风(上一小时内，m/s)': 1.8, '云底高度(m)': '31.97', '低层云量(lcc)': 0.63, '中层云量(mcc)': 0.92, '高层云量(hcc)': 0.85, '总云量(tcc)': 0.99, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.0014, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0014, '雷暴概率(TT，K)': 36.16, 'K指数(K)': 18.75, '对流可用位能(J/kg)': 0.0}
  行2: {'参考地名': '青羊区', '经度(lon)': 104.0, '纬度(lat)': 30.75, '世界时(UTC)': '2024-01-01 01:00:00', '北京时(UTC+8)': '2024-01-01 09:00:00', '海平面气压(hPa)': 1023.63, '地面气压(hPa)': 957.73, '气温2m(℃)': 6.72, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 7.88, '露点温度(℃)': 5.93, '相对湿度(%)': '94.74', '蒸发量(mm)': -0.0025, '潜在蒸发量(mm)': -0.0042, '经向风速(V,m/s)': -0.01, '纬向风速(U,m/s)': 1.61, '最大阵风(上一小时内，m/s)': 2.48, '云底高度(m)': '54.7', '低层云量(lcc)': 0.46, '中层云量(mcc)': 0.94, '高层云量(hcc)': 0.83, '总云量(tcc)': 0.96, '总太阳辐射度(down,J/m2)': 76416.0, '净太阳辐射度(net,J/m2)': 64192.0, '直接辐射(J/m2)': 9792.0, '紫外强度(J/m2)': 8168.0, '径流(mm)': 0.0014, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0014, '雷暴概率(TT，K)': 36.72, 'K指数(K)': 19.59, '对流可用位能(J/kg)': 0.0}
  行3: {'参考地名': '青羊区', '经度(lon)': 104.0, '纬度(lat)': 30.75, '世界时(UTC)': '2024-01-01 02:00:00', '北京时(UTC+8)': '2024-01-01 10:00:00', '海平面气压(hPa)': 1023.86, '地面气压(hPa)': 957.99, '气温2m(℃)': 8.52, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 8.98, '露点温度(℃)': 6.59, '相对湿度(%)': '87.61', '蒸发量(mm)': -0.0123, '潜在蒸发量(mm)': -0.0224, '经向风速(V,m/s)': 0.45, '纬向风速(U,m/s)': 1.1, '最大阵风(上一小时内，m/s)': 2.8, '云底高度(m)': '866.97', '低层云量(lcc)': 1.0, '中层云量(mcc)': 0.9, '高层云量(hcc)': 0.89, '总云量(tcc)': 1.0, '总太阳辐射度(down,J/m2)': 229760.0, '净太阳辐射度(net,J/m2)': 200320.0, '直接辐射(J/m2)': 67904.0, '紫外强度(J/m2)': 16672.0, '径流(mm)': 0.0014, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0014, '雷暴概率(TT，K)': 37.14, 'K指数(K)': 20.09, '对流可用位能(J/kg)': 0.12}

--------------------------------------------------------------------------------

文件名: 青羊区25.xls
文件大小: 2.38 MB
解析区域: 青羊区
解析年份: 2025
读取状态: 成功
数据形状: (3648, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('float64'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'青羊区': 3648}, '世界时(UTC)': {'2025-01-01 00:00:00': 1, '2025-04-11 23:00:00': 1, '2025-04-12 01:00:00': 1, '2025-04-12 02:00:00': 1, '2025-04-12 03:00:00': 1, '2025-04-12 04:00:00': 1, '2025-04-12 05:00:00': 1, '2025-04-12 06:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 08:00:00': 1}, '北京时(UTC+8)': {'2025-01-01 08:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 09:00:00': 1, '2025-04-12 10:00:00': 1, '2025-04-12 11:00:00': 1, '2025-04-12 12:00:00': 1, '2025-04-12 13:00:00': 1, '2025-04-12 14:00:00': 1, '2025-04-12 15:00:00': 1, '2025-04-12 16:00:00': 1}, '雪层温度(℃)': {'*': 3584, '0.01': 45, '0.0': 3, '-0.17': 2, '-0.1': 2, '-0.02': 1, '-0.13': 1, '-0.11': 1, '-0.08': 1, '-0.03': 1}, '云底高度(m)': {'*': 229, '30.24': 3, '29.9': 3, '30.05': 3, '52.32': 3, '30.6': 3, '30.47': 3, '30.21': 3, '30.31': 3, '30.01': 3}}
样本数据:
  行1: {'参考地名': '青羊区', '经度(lon)': 104.0, '纬度(lat)': 30.75, '世界时(UTC)': '2025-01-01 00:00:00', '北京时(UTC+8)': '2025-01-01 08:00:00', '海平面气压(hPa)': 1022.13, '地面气压(hPa)': 955.95, '气温2m(℃)': 2.23, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 1.13, '露点温度(℃)': 1.32, '相对湿度(%)': 93.65, '蒸发量(mm)': -0.0005, '潜在蒸发量(mm)': -0.0001, '经向风速(V,m/s)': -1.06, '纬向风速(U,m/s)': -1.25, '最大阵风(上一小时内，m/s)': 3.27, '云底高度(m)': '1143.12', '低层云量(lcc)': 0.24, '中层云量(mcc)': 0.22, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.26, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.0057, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0057, '雷暴概率(TT，K)': 33.46, 'K指数(K)': 0.82, '对流可用位能(J/kg)': 0.0}
  行2: {'参考地名': '青羊区', '经度(lon)': 104.0, '纬度(lat)': 30.75, '世界时(UTC)': '2025-01-01 01:00:00', '北京时(UTC+8)': '2025-01-01 09:00:00', '海平面气压(hPa)': 1023.14, '地面气压(hPa)': 956.82, '气温2m(℃)': 2.33, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 4.15, '露点温度(℃)': 0.87, '相对湿度(%)': 90.11, '蒸发量(mm)': -0.0067, '潜在蒸发量(mm)': -0.0106, '经向风速(V,m/s)': -1.82, '纬向风速(U,m/s)': -1.42, '最大阵风(上一小时内，m/s)': 4.94, '云底高度(m)': '1043.95', '低层云量(lcc)': 0.15, '中层云量(mcc)': 0.18, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.19, '总太阳辐射度(down,J/m2)': 141120.0, '净太阳辐射度(net,J/m2)': 120640.0, '直接辐射(J/m2)': 72000.0, '紫外强度(J/m2)': 13200.0, '径流(mm)': 0.0057, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0056, '雷暴概率(TT，K)': 33.34, 'K指数(K)': 1.22, '对流可用位能(J/kg)': 0.0}
  行3: {'参考地名': '青羊区', '经度(lon)': 104.0, '纬度(lat)': 30.75, '世界时(UTC)': '2025-01-01 02:00:00', '北京时(UTC+8)': '2025-01-01 10:00:00', '海平面气压(hPa)': 1024.06, '地面气压(hPa)': 957.57, '气温2m(℃)': 7.14, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 8.15, '露点温度(℃)': 2.87, '相对湿度(%)': 74.28, '蒸发量(mm)': -0.0301, '潜在蒸发量(mm)': -0.0485, '经向风速(V,m/s)': -1.58, '纬向风速(U,m/s)': -0.84, '最大阵风(上一小时内，m/s)': 5.36, '云底高度(m)': '1036.47', '低层云量(lcc)': 0.17, '中层云量(mcc)': 0.21, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.24, '总太阳辐射度(down,J/m2)': 527680.0, '净太阳辐射度(net,J/m2)': 454592.0, '直接辐射(J/m2)': 263936.0, '紫外强度(J/m2)': 60152.0, '径流(mm)': 0.0055, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0056, '雷暴概率(TT，K)': 33.84, 'K指数(K)': 2.22, '对流可用位能(J/kg)': 0.0}

--------------------------------------------------------------------------------

文件名: 武侯24.xls
文件大小: 2.37 MB
解析区域: 武侯
解析年份: 2024
读取状态: 成功
数据形状: (8784, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2024-01-01 00:00:00': 1, '2024-09-01 03:00:00': 1, '2024-08-31 21:00:00': 1, '2024-08-31 22:00:00': 1, '2024-08-31 23:00:00': 1, '2024-09-01 00:00:00': 1, '2024-09-01 01:00:00': 1, '2024-09-01 02:00:00': 1, '2024-09-01 04:00:00': 1, '2024-08-31 02:00:00': 1}, '北京时(UTC+8)': {'2024-01-01 08:00:00': 1, '2024-09-01 11:00:00': 1, '2024-09-01 05:00:00': 1, '2024-09-01 06:00:00': 1, '2024-09-01 07:00:00': 1, '2024-09-01 08:00:00': 1, '2024-09-01 09:00:00': 1, '2024-09-01 10:00:00': 1, '2024-09-01 12:00:00': 1, '2024-08-31 10:00:00': 1}, '备注': {'武侯区': 8784}}
样本数据:
  行1: {'经度(lon)': 104.0, '纬度(lat)': 30.6, '地面气压(hPa)': 967.2, '气温(℃)': 6.89, '降水量(mm)': 0.001, '露点温度(℃)': 6.31, '经向风速(V,m/s)': -0.45, '纬向风速(U,m/s)': 0.79, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2024-01-01 00:00:00', '北京时(UTC+8)': '2024-01-01 08:00:00', '备注': '武侯区'}
  行2: {'经度(lon)': 104.0, '纬度(lat)': 30.6, '地面气压(hPa)': 967.89, '气温(℃)': 7.36, '降水量(mm)': 0.0, '露点温度(℃)': 6.74, '经向风速(V,m/s)': -0.27, '纬向风速(U,m/s)': 0.76, '太阳辐射净强度(net,J/m2)': 66718.75, '太阳辐射总强度(down,J/m2)': 77083.5, '世界时(UTC)': '2024-01-01 01:00:00', '北京时(UTC+8)': '2024-01-01 09:00:00', '备注': '武侯区'}
  行3: {'经度(lon)': 104.0, '纬度(lat)': 30.6, '地面气压(hPa)': 968.59, '气温(℃)': 8.76, '降水量(mm)': 0.001, '露点温度(℃)': 6.95, '经向风速(V,m/s)': -0.2, '纬向风速(U,m/s)': 0.84, '太阳辐射净强度(net,J/m2)': 202891.77, '太阳辐射总强度(down,J/m2)': 234414.5, '世界时(UTC)': '2024-01-01 02:00:00', '北京时(UTC+8)': '2024-01-01 10:00:00', '备注': '武侯区'}

--------------------------------------------------------------------------------

文件名: 郫都25.xls
文件大小: 1.02 MB
解析区域: 郫都
解析年份: 2025
读取状态: 成功
数据形状: (3648, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2025-01-01 00:00:00': 1, '2025-04-11 23:00:00': 1, '2025-04-12 01:00:00': 1, '2025-04-12 02:00:00': 1, '2025-04-12 03:00:00': 1, '2025-04-12 04:00:00': 1, '2025-04-12 05:00:00': 1, '2025-04-12 06:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 08:00:00': 1}, '北京时(UTC+8)': {'2025-01-01 08:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 09:00:00': 1, '2025-04-12 10:00:00': 1, '2025-04-12 11:00:00': 1, '2025-04-12 12:00:00': 1, '2025-04-12 13:00:00': 1, '2025-04-12 14:00:00': 1, '2025-04-12 15:00:00': 1, '2025-04-12 16:00:00': 1}, '备注': {'郫都区': 3648}}
样本数据:
  行1: {'经度(lon)': 103.9, '纬度(lat)': 30.8, '地面气压(hPa)': 943.53, '气温(℃)': 0.9, '降水量(mm)': 0.0, '露点温度(℃)': -0.43, '经向风速(V,m/s)': -1.75, '纬向风速(U,m/s)': -0.93, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2025-01-01 00:00:00', '北京时(UTC+8)': '2025-01-01 08:00:00', '备注': '郫都区'}
  行2: {'经度(lon)': 103.9, '纬度(lat)': 30.8, '地面气压(hPa)': 944.49, '气温(℃)': 2.15, '降水量(mm)': 0.0, '露点温度(℃)': 0.69, '经向风速(V,m/s)': -1.65, '纬向风速(U,m/s)': -0.87, '太阳辐射净强度(net,J/m2)': 121022.25, '太阳辐射总强度(down,J/m2)': 140499.75, '世界时(UTC)': '2025-01-01 01:00:00', '北京时(UTC+8)': '2025-01-01 09:00:00', '备注': '郫都区'}
  行3: {'经度(lon)': 103.9, '纬度(lat)': 30.8, '地面气压(hPa)': 945.31, '气温(℃)': 5.4, '降水量(mm)': 0.0, '露点温度(℃)': 2.06, '经向风速(V,m/s)': -1.61, '纬向风速(U,m/s)': -0.89, '太阳辐射净强度(net,J/m2)': 471777.25, '太阳辐射总强度(down,J/m2)': 547733.25, '世界时(UTC)': '2025-01-01 02:00:00', '北京时(UTC+8)': '2025-01-01 10:00:00', '备注': '郫都区'}

--------------------------------------------------------------------------------

文件名: 龙泉驿23.xls
文件大小: 2.36 MB
解析区域: 龙泉驿
解析年份: 2023
读取状态: 成功
数据形状: (8760, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2023-01-01 00:00:00': 1, '2023-09-01 10:00:00': 1, '2023-09-01 04:00:00': 1, '2023-09-01 05:00:00': 1, '2023-09-01 06:00:00': 1, '2023-09-01 07:00:00': 1, '2023-09-01 08:00:00': 1, '2023-09-01 09:00:00': 1, '2023-09-01 11:00:00': 1, '2023-09-03 05:00:00': 1}, '北京时(UTC+8)': {'2023-01-01 08:00:00': 1, '2023-09-01 18:00:00': 1, '2023-09-01 12:00:00': 1, '2023-09-01 13:00:00': 1, '2023-09-01 14:00:00': 1, '2023-09-01 15:00:00': 1, '2023-09-01 16:00:00': 1, '2023-09-01 17:00:00': 1, '2023-09-01 19:00:00': 1, '2023-09-03 13:00:00': 1}, '备注': {'龙泉驿': 8760}}
样本数据:
  行1: {'经度(lon)': 104.3, '纬度(lat)': 30.6, '地面气压(hPa)': 963.19, '气温(℃)': 2.53, '降水量(mm)': 0.003, '露点温度(℃)': 2.34, '经向风速(V,m/s)': -0.19, '纬向风速(U,m/s)': 0.57, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2023-01-01 00:00:00', '北京时(UTC+8)': '2023-01-01 08:00:00', '备注': '龙泉驿'}
  行2: {'经度(lon)': 104.3, '纬度(lat)': 30.6, '地面气压(hPa)': 963.93, '气温(℃)': 3.12, '降水量(mm)': 0.002, '露点温度(℃)': 2.81, '经向风速(V,m/s)': -0.13, '纬向风速(U,m/s)': 0.52, '太阳辐射净强度(net,J/m2)': 81360.0, '太阳辐射总强度(down,J/m2)': 92449.75, '世界时(UTC)': '2023-01-01 01:00:00', '北京时(UTC+8)': '2023-01-01 09:00:00', '备注': '龙泉驿'}
  行3: {'经度(lon)': 104.3, '纬度(lat)': 30.6, '地面气压(hPa)': 964.45, '气温(℃)': 4.23, '降水量(mm)': 0.002, '露点温度(℃)': 3.84, '经向风速(V,m/s)': -0.28, '纬向风速(U,m/s)': 0.85, '太阳辐射净强度(net,J/m2)': 283753.5, '太阳辐射总强度(down,J/m2)': 322442.75, '世界时(UTC)': '2023-01-01 02:00:00', '北京时(UTC+8)': '2023-01-01 10:00:00', '备注': '龙泉驿'}

--------------------------------------------------------------------------------

文件名: 都江堰24.xls
文件大小: 5.52 MB
解析区域: 都江堰
解析年份: 2024
读取状态: 成功
数据形状: (8808, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('O'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'都江堰': 8808}, '世界时(UTC)': {'2024-09-01 12:00:00': 2, '2024-09-01 02:00:00': 2, '2024-09-01 22:00:00': 2, '2024-09-01 21:00:00': 2, '2024-09-01 20:00:00': 2, '2024-09-01 19:00:00': 2, '2024-09-01 18:00:00': 2, '2024-09-01 17:00:00': 2, '2024-09-01 16:00:00': 2, '2024-09-01 15:00:00': 2}, '北京时(UTC+8)': {'2024-09-01 20:00:00': 2, '2024-09-01 10:00:00': 2, '2024-09-02 06:00:00': 2, '2024-09-02 05:00:00': 2, '2024-09-02 04:00:00': 2, '2024-09-02 03:00:00': 2, '2024-09-02 02:00:00': 2, '2024-09-02 01:00:00': 2, '2024-09-02 00:00:00': 2, '2024-09-01 23:00:00': 2}, '雪层温度(℃)': {'*': 8195, '0.01': 49, '0.0': 14, '-0.03': 11, '-0.01': 11, '-0.02': 9, '-0.08': 7, '-0.39': 7, '-0.04': 6, '-0.18': 5}, '相对湿度(%)': {'90.43': 9, '95.52': 9, '85.91': 8, '90.14': 8, '89.65': 8, '84.39': 8, '84.48': 8, '77.49': 8, '85.56': 7, '75.87': 7}}
样本数据:
  行1: {'参考地名': '都江堰', '经度(lon)': 103.75, '纬度(lat)': 31.0, '世界时(UTC)': '2024-01-01 00:00:00', '北京时(UTC+8)': '2024-01-01 08:00:00', '海平面气压(hPa)': 1023.4, '地面气压(hPa)': 910.28, '气温2m(℃)': 5.18, '降水量(mm)': 0.023, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 4.73, '露点温度(℃)': 2.99, '相对湿度(%)': '85.76', '蒸发量(mm)': -0.0008, '潜在蒸发量(mm)': -0.0007, '经向风速(V,m/s)': -0.22, '纬向风速(U,m/s)': 0.51, '最大阵风(上一小时内，m/s)': 2.21, '云底高度(m)': '88.47', '低层云量(lcc)': 0.91, '中层云量(mcc)': 0.74, '高层云量(hcc)': 0.84, '总云量(tcc)': 1.0, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.0501, '地表径流(mm)': 0.0048, '地下径流(mm)': 0.0453, '雷暴概率(TT，K)': 35.53, 'K指数(K)': 9.31, '对流可用位能(J/kg)': 0.0}
  行2: {'参考地名': '都江堰', '经度(lon)': 103.75, '纬度(lat)': 31.0, '世界时(UTC)': '2024-01-01 01:00:00', '北京时(UTC+8)': '2024-01-01 09:00:00', '海平面气压(hPa)': 1023.68, '地面气压(hPa)': 910.59, '气温2m(℃)': 4.98, '降水量(mm)': 0.039, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 5.72, '露点温度(℃)': 3.32, '相对湿度(%)': '88.97', '蒸发量(mm)': -0.0012, '潜在蒸发量(mm)': -0.0015, '经向风速(V,m/s)': -0.49, '纬向风速(U,m/s)': 0.42, '最大阵风(上一小时内，m/s)': 2.04, '云底高度(m)': '115.45', '低层云量(lcc)': 0.77, '中层云量(mcc)': 0.76, '高层云量(hcc)': 0.89, '总云量(tcc)': 0.98, '总太阳辐射度(down,J/m2)': 68352.0, '净太阳辐射度(net,J/m2)': 59456.0, '直接辐射(J/m2)': 3968.0, '紫外强度(J/m2)': 9184.0, '径流(mm)': 0.0532, '地表径流(mm)': 0.0081, '地下径流(mm)': 0.0452, '雷暴概率(TT，K)': 35.77, 'K指数(K)': 10.71, '对流可用位能(J/kg)': 0.0}
  行3: {'参考地名': '都江堰', '经度(lon)': 103.75, '纬度(lat)': 31.0, '世界时(UTC)': '2024-01-01 02:00:00', '北京时(UTC+8)': '2024-01-01 10:00:00', '海平面气压(hPa)': 1023.84, '地面气压(hPa)': 910.87, '气温2m(℃)': 7.2, '降水量(mm)': 0.066, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 7.49, '露点温度(℃)': 4.4, '相对湿度(%)': '82.33', '蒸发量(mm)': -0.0212, '潜在蒸发量(mm)': -0.018, '经向风速(V,m/s)': -0.15, '纬向风速(U,m/s)': 0.11, '最大阵风(上一小时内，m/s)': 1.8, '云底高度(m)': '267.97', '低层云量(lcc)': 0.86, '中层云量(mcc)': 0.85, '高层云量(hcc)': 0.89, '总云量(tcc)': 0.98, '总太阳辐射度(down,J/m2)': 259264.0, '净太阳辐射度(net,J/m2)': 232192.0, '直接辐射(J/m2)': 56960.0, '紫外强度(J/m2)': 19936.0, '径流(mm)': 0.0553, '地表径流(mm)': 0.0098, '地下径流(mm)': 0.0452, '雷暴概率(TT，K)': 35.97, 'K指数(K)': 11.93, '对流可用位能(J/kg)': 0.0}

--------------------------------------------------------------------------------

文件名: 双流23.xls
文件大小: 2.36 MB
解析区域: 双流
解析年份: 2023
读取状态: 成功
数据形状: (8760, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2023-01-01 00:00:00': 1, '2023-09-01 10:00:00': 1, '2023-09-01 04:00:00': 1, '2023-09-01 05:00:00': 1, '2023-09-01 06:00:00': 1, '2023-09-01 07:00:00': 1, '2023-09-01 08:00:00': 1, '2023-09-01 09:00:00': 1, '2023-09-01 11:00:00': 1, '2023-09-03 05:00:00': 1}, '北京时(UTC+8)': {'2023-01-01 08:00:00': 1, '2023-09-01 18:00:00': 1, '2023-09-01 12:00:00': 1, '2023-09-01 13:00:00': 1, '2023-09-01 14:00:00': 1, '2023-09-01 15:00:00': 1, '2023-09-01 16:00:00': 1, '2023-09-01 17:00:00': 1, '2023-09-01 19:00:00': 1, '2023-09-03 13:00:00': 1}, '备注': {'双流区': 8760}}
样本数据:
  行1: {'经度(lon)': 103.9, '纬度(lat)': 30.6, '地面气压(hPa)': 970.58, '气温(℃)': 3.5, '降水量(mm)': 0.001, '露点温度(℃)': 3.41, '经向风速(V,m/s)': -0.13, '纬向风速(U,m/s)': 0.56, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2023-01-01 00:00:00', '北京时(UTC+8)': '2023-01-01 08:00:00', '备注': '双流区'}
  行2: {'经度(lon)': 103.9, '纬度(lat)': 30.6, '地面气压(hPa)': 971.31, '气温(℃)': 3.88, '降水量(mm)': 0.002, '露点温度(℃)': 3.68, '经向风速(V,m/s)': -0.02, '纬向风速(U,m/s)': 0.64, '太阳辐射净强度(net,J/m2)': 74781.75, '太阳辐射总强度(down,J/m2)': 86082.75, '世界时(UTC)': '2023-01-01 01:00:00', '北京时(UTC+8)': '2023-01-01 09:00:00', '备注': '双流区'}
  行3: {'经度(lon)': 103.9, '纬度(lat)': 30.6, '地面气压(hPa)': 971.82, '气温(℃)': 4.9, '降水量(mm)': 0.001, '露点温度(℃)': 3.92, '经向风速(V,m/s)': -0.09, '纬向风速(U,m/s)': 0.99, '太阳辐射净强度(net,J/m2)': 312362.75, '太阳辐射总强度(down,J/m2)': 359525.25, '世界时(UTC)': '2023-01-01 02:00:00', '北京时(UTC+8)': '2023-01-01 10:00:00', '备注': '双流区'}

--------------------------------------------------------------------------------

文件名: 都江堰25.xls
文件大小: 2.39 MB
解析区域: 都江堰
解析年份: 2025
读取状态: 成功
数据形状: (3648, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('O'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'都江堰': 3648}, '世界时(UTC)': {'2025-01-01 00:00:00': 1, '2025-04-11 23:00:00': 1, '2025-04-12 01:00:00': 1, '2025-04-12 02:00:00': 1, '2025-04-12 03:00:00': 1, '2025-04-12 04:00:00': 1, '2025-04-12 05:00:00': 1, '2025-04-12 06:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 08:00:00': 1}, '北京时(UTC+8)': {'2025-01-01 08:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 09:00:00': 1, '2025-04-12 10:00:00': 1, '2025-04-12 11:00:00': 1, '2025-04-12 12:00:00': 1, '2025-04-12 13:00:00': 1, '2025-04-12 14:00:00': 1, '2025-04-12 15:00:00': 1, '2025-04-12 16:00:00': 1}, '雪层温度(℃)': {'*': 3478, '0.01': 20, '-0.01': 3, '-0.02': 3, '-0.72': 3, '-0.3': 3, '-0.1': 3, '-0.06': 3, '-0.67': 2, '-0.17': 2}, '相对湿度(%)': {'95.51': 6, '96.83': 5, '88.35': 5, '97.39': 5, '71.97': 5, '85.59': 5, '95.86': 5, '83.82': 5, '67.65': 5, '78.26': 5}}
样本数据:
  行1: {'参考地名': '都江堰', '经度(lon)': 103.75, '纬度(lat)': 31.0, '世界时(UTC)': '2025-01-01 00:00:00', '北京时(UTC+8)': '2025-01-01 08:00:00', '海平面气压(hPa)': 1021.8, '地面气压(hPa)': 908.3, '气温2m(℃)': 1.14, '降水量(mm)': 0.002, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': -2.74, '露点温度(℃)': -1.44, '相对湿度(%)': '82.89', '蒸发量(mm)': 0.0012, '潜在蒸发量(mm)': 0.0014, '经向风速(V,m/s)': -1.63, '纬向风速(U,m/s)': -0.74, '最大阵风(上一小时内，m/s)': 3.42, '云底高度(m)': '604.87', '低层云量(lcc)': 0.29, '中层云量(mcc)': 0.06, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.29, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.0744, '地表径流(mm)': 0.0002, '地下径流(mm)': 0.0744, '雷暴概率(TT，K)': 37.47, 'K指数(K)': -0.38, '对流可用位能(J/kg)': 0.0}
  行2: {'参考地名': '都江堰', '经度(lon)': 103.75, '纬度(lat)': 31.0, '世界时(UTC)': '2025-01-01 01:00:00', '北京时(UTC+8)': '2025-01-01 09:00:00', '海平面气压(hPa)': 1022.98, '地面气压(hPa)': 909.16, '气温2m(℃)': 0.28, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 1.26, '露点温度(℃)': -1.17, '相对湿度(%)': '89.98', '蒸发量(mm)': -0.0012, '潜在蒸发量(mm)': -0.0018, '经向风速(V,m/s)': -1.41, '纬向风速(U,m/s)': -0.78, '最大阵风(上一小时内，m/s)': 3.47, '云底高度(m)': '543.2', '低层云量(lcc)': 0.16, '中层云量(mcc)': 0.06, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.17, '总太阳辐射度(down,J/m2)': 134464.0, '净太阳辐射度(net,J/m2)': 118656.0, '直接辐射(J/m2)': 80704.0, '紫外强度(J/m2)': 16200.0, '径流(mm)': 0.0744, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0743, '雷暴概率(TT，K)': 37.91, 'K指数(K)': -0.65, '对流可用位能(J/kg)': 0.0}
  行3: {'参考地名': '都江堰', '经度(lon)': 103.75, '纬度(lat)': 31.0, '世界时(UTC)': '2025-01-01 02:00:00', '北京时(UTC+8)': '2025-01-01 10:00:00', '海平面气压(hPa)': 1024.04, '地面气压(hPa)': 909.96, '气温2m(℃)': 5.23, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 6.54, '露点温度(℃)': 1.24, '相对湿度(%)': '75.44', '蒸发量(mm)': -0.0246, '潜在蒸发量(mm)': -0.0306, '经向风速(V,m/s)': -0.74, '纬向风速(U,m/s)': -1.12, '最大阵风(上一小时内，m/s)': 3.96, '云底高度(m)': '541.22', '低层云量(lcc)': 0.35, '中层云量(mcc)': 0.1, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.37, '总太阳辐射度(down,J/m2)': 580480.0, '净太阳辐射度(net,J/m2)': 515584.0, '直接辐射(J/m2)': 415168.0, '紫外强度(J/m2)': 71680.0, '径流(mm)': 0.0744, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0743, '雷暴概率(TT，K)': 38.34, 'K指数(K)': -0.66, '对流可用位能(J/kg)': 0.0}

--------------------------------------------------------------------------------

文件名: 彭州23.xls
文件大小: 5.48 MB
解析区域: 彭州
解析年份: 2023
读取状态: 成功
数据形状: (8760, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('O'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'彭州': 8760}, '世界时(UTC)': {'2023-01-01 00:00:00': 1, '2023-09-01 10:00:00': 1, '2023-09-01 04:00:00': 1, '2023-09-01 05:00:00': 1, '2023-09-01 06:00:00': 1, '2023-09-01 07:00:00': 1, '2023-09-01 08:00:00': 1, '2023-09-01 09:00:00': 1, '2023-09-01 11:00:00': 1, '2023-09-03 05:00:00': 1}, '北京时(UTC+8)': {'2023-01-01 08:00:00': 1, '2023-09-01 18:00:00': 1, '2023-09-01 12:00:00': 1, '2023-09-01 13:00:00': 1, '2023-09-01 14:00:00': 1, '2023-09-01 15:00:00': 1, '2023-09-01 16:00:00': 1, '2023-09-01 17:00:00': 1, '2023-09-01 19:00:00': 1, '2023-09-03 13:00:00': 1}, '雪层温度(℃)': {'*': 8751, '-0.67': 1, '-0.98': 1, '-0.4': 1, '-0.13': 1, '-0.53': 1, '-0.04': 1, '0.01': 1, '-4.66': 1, '-5.01': 1}, '相对湿度(%)': {'87.9': 9, '67.77': 8, '82.52': 7, '89.08': 7, '76.59': 7, '91.77': 7, '90.08': 7, '84.75': 7, '86.16': 7, '85.96': 7}}
样本数据:
  行1: {'参考地名': '彭州', '经度(lon)': 104.0, '纬度(lat)': 31.0, '世界时(UTC)': '2023-01-01 00:00:00', '北京时(UTC+8)': '2023-01-01 08:00:00', '海平面气压(hPa)': 1030.26, '地面气压(hPa)': 960.77, '气温2m(℃)': 4.53, '降水量(mm)': 0.016, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 3.17, '露点温度(℃)': 4.45, '相对湿度(%)': '99.44', '蒸发量(mm)': 0.0003, '潜在蒸发量(mm)': 0.0003, '经向风速(V,m/s)': 0.25, '纬向风速(U,m/s)': 1.25, '最大阵风(上一小时内，m/s)': 1.14, '云底高度(m)': '29.92', '低层云量(lcc)': 1.0, '中层云量(mcc)': 0.56, '高层云量(hcc)': 0.0, '总云量(tcc)': 1.0, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.0033, '地表径流(mm)': 0.001, '地下径流(mm)': 0.0021, '雷暴概率(TT，K)': 27.68, 'K指数(K)': 6.77, '对流可用位能(J/kg)': 0.0}
  行2: {'参考地名': '彭州', '经度(lon)': 104.0, '纬度(lat)': 31.0, '世界时(UTC)': '2023-01-01 01:00:00', '北京时(UTC+8)': '2023-01-01 09:00:00', '海平面气压(hPa)': 1031.04, '地面气压(hPa)': 961.47, '气温2m(℃)': 4.59, '降水量(mm)': 0.01, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 4.62, '露点温度(℃)': 4.58, '相对湿度(%)': '99.99', '蒸发量(mm)': -0.0026, '潜在蒸发量(mm)': -0.0016, '经向风速(V,m/s)': 0.19, '纬向风速(U,m/s)': 0.65, '最大阵风(上一小时内，m/s)': 2.04, '云底高度(m)': '29.71', '低层云量(lcc)': 1.0, '中层云量(mcc)': 0.59, '高层云量(hcc)': 0.0, '总云量(tcc)': 1.0, '总太阳辐射度(down,J/m2)': 72576.0, '净太阳辐射度(net,J/m2)': 61568.0, '直接辐射(J/m2)': 10880.0, '紫外强度(J/m2)': 9824.0, '径流(mm)': 0.0029, '地表径流(mm)': 0.0005, '地下径流(mm)': 0.0021, '雷暴概率(TT，K)': 27.82, 'K指数(K)': 6.46, '对流可用位能(J/kg)': 0.0}
  行3: {'参考地名': '彭州', '经度(lon)': 104.0, '纬度(lat)': 31.0, '世界时(UTC)': '2023-01-01 02:00:00', '北京时(UTC+8)': '2023-01-01 10:00:00', '海平面气压(hPa)': 1031.34, '地面气压(hPa)': 961.75, '气温2m(℃)': 5.72, '降水量(mm)': 0.013, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 6.75, '露点温度(℃)': 5.02, '相对湿度(%)': '95.21', '蒸发量(mm)': -0.0277, '潜在蒸发量(mm)': -0.02, '经向风速(V,m/s)': 0.08, '纬向风速(U,m/s)': 0.47, '最大阵风(上一小时内，m/s)': 3.06, '云底高度(m)': '83.79', '低层云量(lcc)': 0.57, '中层云量(mcc)': 0.59, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.78, '总太阳辐射度(down,J/m2)': 331008.0, '净太阳辐射度(net,J/m2)': 279424.0, '直接辐射(J/m2)': 113280.0, '紫外强度(J/m2)': 37280.0, '径流(mm)': 0.0029, '地表径流(mm)': 0.0005, '地下径流(mm)': 0.0021, '雷暴概率(TT，K)': 27.57, 'K指数(K)': 6.04, '对流可用位能(J/kg)': 0.0}

--------------------------------------------------------------------------------

文件名: 武侯23.xls
文件大小: 2.36 MB
解析区域: 武侯
解析年份: 2023
读取状态: 成功
数据形状: (8760, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2023-01-01 00:00:00': 1, '2023-09-01 10:00:00': 1, '2023-09-01 04:00:00': 1, '2023-09-01 05:00:00': 1, '2023-09-01 06:00:00': 1, '2023-09-01 07:00:00': 1, '2023-09-01 08:00:00': 1, '2023-09-01 09:00:00': 1, '2023-09-01 11:00:00': 1, '2023-09-03 05:00:00': 1}, '北京时(UTC+8)': {'2023-01-01 08:00:00': 1, '2023-09-01 18:00:00': 1, '2023-09-01 12:00:00': 1, '2023-09-01 13:00:00': 1, '2023-09-01 14:00:00': 1, '2023-09-01 15:00:00': 1, '2023-09-01 16:00:00': 1, '2023-09-01 17:00:00': 1, '2023-09-01 19:00:00': 1, '2023-09-03 13:00:00': 1}, '备注': {'武侯区': 8760}}
样本数据:
  行1: {'经度(lon)': 104.0, '纬度(lat)': 30.6, '地面气压(hPa)': 972.9, '气温(℃)': 3.43, '降水量(mm)': 0.002, '露点温度(℃)': 3.31, '经向风速(V,m/s)': -0.11, '纬向风速(U,m/s)': 0.63, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2023-01-01 00:00:00', '北京时(UTC+8)': '2023-01-01 08:00:00', '备注': '武侯区'}
  行2: {'经度(lon)': 104.0, '纬度(lat)': 30.6, '地面气压(hPa)': 973.6, '气温(℃)': 3.9, '降水量(mm)': 0.002, '露点温度(℃)': 3.62, '经向风速(V,m/s)': -0.01, '纬向风速(U,m/s)': 0.72, '太阳辐射净强度(net,J/m2)': 75604.0, '太阳辐射总强度(down,J/m2)': 87356.5, '世界时(UTC)': '2023-01-01 01:00:00', '北京时(UTC+8)': '2023-01-01 09:00:00', '备注': '武侯区'}
  行3: {'经度(lon)': 104.0, '纬度(lat)': 30.6, '地面气压(hPa)': 974.1, '气温(℃)': 4.83, '降水量(mm)': 0.001, '露点温度(℃)': 4.03, '经向风速(V,m/s)': -0.09, '纬向风速(U,m/s)': 1.1, '太阳辐射净强度(net,J/m2)': 297961.0, '太阳辐射总强度(down,J/m2)': 344254.47, '世界时(UTC)': '2023-01-01 02:00:00', '北京时(UTC+8)': '2023-01-01 10:00:00', '备注': '武侯区'}

--------------------------------------------------------------------------------

文件名: 青羊区23.xls
文件大小: 5.49 MB
解析区域: 青羊区
解析年份: 2023
读取状态: 成功
数据形状: (8760, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('O'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'青羊区': 8760}, '世界时(UTC)': {'2023-01-01 00:00:00': 1, '2023-09-01 10:00:00': 1, '2023-09-01 04:00:00': 1, '2023-09-01 05:00:00': 1, '2023-09-01 06:00:00': 1, '2023-09-01 07:00:00': 1, '2023-09-01 08:00:00': 1, '2023-09-01 09:00:00': 1, '2023-09-01 11:00:00': 1, '2023-09-03 05:00:00': 1}, '北京时(UTC+8)': {'2023-01-01 08:00:00': 1, '2023-09-01 18:00:00': 1, '2023-09-01 12:00:00': 1, '2023-09-01 13:00:00': 1, '2023-09-01 14:00:00': 1, '2023-09-01 15:00:00': 1, '2023-09-01 16:00:00': 1, '2023-09-01 17:00:00': 1, '2023-09-01 19:00:00': 1, '2023-09-03 13:00:00': 1}, '雪层温度(℃)': {'*': 8760}, '相对湿度(%)': {'*': 25, '88.54': 8, '82.03': 7, '81.85': 7, '87.65': 7, '82.11': 7, '99.99': 7, '78.96': 7, '85.03': 7, '66.68': 7}}
样本数据:
  行1: {'参考地名': '青羊区', '经度(lon)': 104.0, '纬度(lat)': 30.75, '世界时(UTC)': '2023-01-01 00:00:00', '北京时(UTC+8)': '2023-01-01 08:00:00', '海平面气压(hPa)': 1029.93, '地面气压(hPa)': 962.79, '气温2m(℃)': 4.26, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 3.97, '露点温度(℃)': 4.26, '相对湿度(%)': '*', '蒸发量(mm)': 0.0011, '潜在蒸发量(mm)': 0.0005, '经向风速(V,m/s)': 0.28, '纬向风速(U,m/s)': 1.56, '最大阵风(上一小时内，m/s)': 2.16, '云底高度(m)': '29.67', '低层云量(lcc)': 1.0, '中层云量(mcc)': 0.17, '高层云量(hcc)': 0.0, '总云量(tcc)': 1.0, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.001, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0012, '雷暴概率(TT，K)': 26.08, 'K指数(K)': 5.86, '对流可用位能(J/kg)': 0.0}
  行2: {'参考地名': '青羊区', '经度(lon)': 104.0, '纬度(lat)': 30.75, '世界时(UTC)': '2023-01-01 01:00:00', '北京时(UTC+8)': '2023-01-01 09:00:00', '海平面气压(hPa)': 1030.76, '地面气压(hPa)': 963.49, '气温2m(℃)': 4.16, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 5.13, '露点温度(℃)': 4.16, '相对湿度(%)': '*', '蒸发量(mm)': -0.0023, '潜在蒸发量(mm)': -0.0019, '经向风速(V,m/s)': 0.08, '纬向风速(U,m/s)': 1.33, '最大阵风(上一小时内，m/s)': 2.72, '云底高度(m)': '29.71', '低层云量(lcc)': 1.0, '中层云量(mcc)': 0.17, '高层云量(hcc)': 0.0, '总云量(tcc)': 1.0, '总太阳辐射度(down,J/m2)': 82240.0, '净太阳辐射度(net,J/m2)': 70592.0, '直接辐射(J/m2)': 7552.0, '紫外强度(J/m2)': 11048.0, '径流(mm)': 0.0012, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0012, '雷暴概率(TT，K)': 26.31, 'K指数(K)': 6.04, '对流可用位能(J/kg)': 0.0}
  行3: {'参考地名': '青羊区', '经度(lon)': 104.0, '纬度(lat)': 30.75, '世界时(UTC)': '2023-01-01 02:00:00', '北京时(UTC+8)': '2023-01-01 10:00:00', '海平面气压(hPa)': 1031.02, '地面气压(hPa)': 963.77, '气温2m(℃)': 5.25, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 7.31, '露点温度(℃)': 5.01, '相对湿度(%)': '98.37', '蒸发量(mm)': -0.0215, '潜在蒸发量(mm)': -0.0209, '经向风速(V,m/s)': -0.3, '纬向风速(U,m/s)': 1.53, '最大阵风(上一小时内，m/s)': 3.36, '云底高度(m)': '109.04', '低层云量(lcc)': 0.58, '中层云量(mcc)': 0.19, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.64, '总太阳辐射度(down,J/m2)': 340352.0, '净太阳辐射度(net,J/m2)': 292416.0, '直接辐射(J/m2)': 77824.0, '紫外强度(J/m2)': 42176.0, '径流(mm)': 0.0012, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0012, '雷暴概率(TT，K)': 26.33, 'K指数(K)': 5.98, '对流可用位能(J/kg)': 0.81}

--------------------------------------------------------------------------------

文件名: 郫都23.xls
文件大小: 2.35 MB
解析区域: 郫都
解析年份: 2023
读取状态: 成功
数据形状: (8760, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2023-01-01 00:00:00': 1, '2023-09-01 10:00:00': 1, '2023-09-01 04:00:00': 1, '2023-09-01 05:00:00': 1, '2023-09-01 06:00:00': 1, '2023-09-01 07:00:00': 1, '2023-09-01 08:00:00': 1, '2023-09-01 09:00:00': 1, '2023-09-01 11:00:00': 1, '2023-09-03 05:00:00': 1}, '北京时(UTC+8)': {'2023-01-01 08:00:00': 1, '2023-09-01 18:00:00': 1, '2023-09-01 12:00:00': 1, '2023-09-01 13:00:00': 1, '2023-09-01 14:00:00': 1, '2023-09-01 15:00:00': 1, '2023-09-01 16:00:00': 1, '2023-09-01 17:00:00': 1, '2023-09-01 19:00:00': 1, '2023-09-03 13:00:00': 1}, '备注': {'郫都区': 8760}}
样本数据:
  行1: {'经度(lon)': 103.9, '纬度(lat)': 30.8, '地面气压(hPa)': 950.34, '气温(℃)': 2.74, '降水量(mm)': 0.031, '露点温度(℃)': 2.5, '经向风速(V,m/s)': -0.04, '纬向风速(U,m/s)': 0.23, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2023-01-01 00:00:00', '北京时(UTC+8)': '2023-01-01 08:00:00', '备注': '郫都区'}
  行2: {'经度(lon)': 103.9, '纬度(lat)': 30.8, '地面气压(hPa)': 951.05, '气温(℃)': 3.0, '降水量(mm)': 0.023, '露点温度(℃)': 2.78, '经向风速(V,m/s)': 0.2, '纬向风速(U,m/s)': 0.27, '太阳辐射净强度(net,J/m2)': 67964.5, '太阳辐射总强度(down,J/m2)': 79022.75, '世界时(UTC)': '2023-01-01 01:00:00', '北京时(UTC+8)': '2023-01-01 09:00:00', '备注': '郫都区'}
  行3: {'经度(lon)': 103.9, '纬度(lat)': 30.8, '地面气压(hPa)': 951.57, '气温(℃)': 3.83, '降水量(mm)': 0.027, '露点温度(℃)': 3.05, '经向风速(V,m/s)': 0.25, '纬向风速(U,m/s)': 0.45, '太阳辐射净强度(net,J/m2)': 295408.5, '太阳辐射总强度(down,J/m2)': 343486.25, '世界时(UTC)': '2023-01-01 02:00:00', '北京时(UTC+8)': '2023-01-01 10:00:00', '备注': '郫都区'}

--------------------------------------------------------------------------------

文件名: 彭州24.xls
文件大小: 5.51 MB
解析区域: 彭州
解析年份: 2024
读取状态: 成功
数据形状: (8808, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('O'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'彭州': 8808}, '世界时(UTC)': {'2024-09-01 12:00:00': 2, '2024-09-01 02:00:00': 2, '2024-09-01 22:00:00': 2, '2024-09-01 21:00:00': 2, '2024-09-01 20:00:00': 2, '2024-09-01 19:00:00': 2, '2024-09-01 18:00:00': 2, '2024-09-01 17:00:00': 2, '2024-09-01 16:00:00': 2, '2024-09-01 15:00:00': 2}, '北京时(UTC+8)': {'2024-09-01 20:00:00': 2, '2024-09-01 10:00:00': 2, '2024-09-02 06:00:00': 2, '2024-09-02 05:00:00': 2, '2024-09-02 04:00:00': 2, '2024-09-02 03:00:00': 2, '2024-09-02 02:00:00': 2, '2024-09-02 01:00:00': 2, '2024-09-02 00:00:00': 2, '2024-09-01 23:00:00': 2}, '雪层温度(℃)': {'*': 8790, '-0.14': 2, '0.01': 2, '-0.69': 2, '-0.21': 1, '-0.13': 1, '-0.08': 1, '-0.16': 1, '-0.57': 1, '-0.58': 1}, '相对湿度(%)': {'87.53': 9, '83.0': 9, '81.99': 9, '85.68': 8, '85.96': 8, '85.99': 8, '74.76': 7, '91.1': 7, '86.19': 7, '77.31': 7}}
样本数据:
  行1: {'参考地名': '彭州', '经度(lon)': 104.0, '纬度(lat)': 31.0, '世界时(UTC)': '2024-01-01 00:00:00', '北京时(UTC+8)': '2024-01-01 08:00:00', '海平面气压(hPa)': 1023.28, '地面气压(hPa)': 955.21, '气温2m(℃)': 7.14, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 6.67, '露点温度(℃)': 5.4, '相对湿度(%)': '88.73', '蒸发量(mm)': -0.0005, '潜在蒸发量(mm)': -0.0005, '经向风速(V,m/s)': 0.26, '纬向风速(U,m/s)': 0.98, '最大阵风(上一小时内，m/s)': 1.12, '云底高度(m)': '89.97', '低层云量(lcc)': 0.63, '中层云量(mcc)': 0.97, '高层云量(hcc)': 0.84, '总云量(tcc)': 1.0, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.0007, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0006, '雷暴概率(TT，K)': 35.79, 'K指数(K)': 16.49, '对流可用位能(J/kg)': 0.0}
  行2: {'参考地名': '彭州', '经度(lon)': 104.0, '纬度(lat)': 31.0, '世界时(UTC)': '2024-01-01 01:00:00', '北京时(UTC+8)': '2024-01-01 09:00:00', '海平面气压(hPa)': 1023.67, '地面气压(hPa)': 955.6, '气温2m(℃)': 6.99, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 7.54, '露点温度(℃)': 5.7, '相对湿度(%)': '91.51', '蒸发量(mm)': -0.001, '潜在蒸发量(mm)': -0.0013, '经向风速(V,m/s)': 0.09, '纬向风速(U,m/s)': 0.78, '最大阵风(上一小时内，m/s)': 1.12, '云底高度(m)': '94.45', '低层云量(lcc)': 0.36, '中层云量(mcc)': 0.99, '高层云量(hcc)': 0.86, '总云量(tcc)': 0.99, '总太阳辐射度(down,J/m2)': 71104.0, '净太阳辐射度(net,J/m2)': 58816.0, '直接辐射(J/m2)': 3136.0, '紫外强度(J/m2)': 8840.0, '径流(mm)': 0.0007, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0007, '雷暴概率(TT，K)': 36.21, 'K指数(K)': 17.94, '对流可用位能(J/kg)': 0.0}
  行3: {'参考地名': '彭州', '经度(lon)': 104.0, '纬度(lat)': 31.0, '世界时(UTC)': '2024-01-01 02:00:00', '北京时(UTC+8)': '2024-01-01 10:00:00', '海平面气压(hPa)': 1023.88, '地面气压(hPa)': 955.85, '气温2m(℃)': 9.04, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 8.62, '露点温度(℃)': 6.87, '相对湿度(%)': '86.27', '蒸发量(mm)': -0.017, '潜在蒸发量(mm)': -0.0168, '经向风速(V,m/s)': 0.51, '纬向风速(U,m/s)': 0.31, '最大阵风(上一小时内，m/s)': 1.92, '云底高度(m)': '859.22', '低层云量(lcc)': 0.64, '中层云量(mcc)': 0.98, '高层云量(hcc)': 0.95, '总云量(tcc)': 1.0, '总太阳辐射度(down,J/m2)': 236736.0, '净太阳辐射度(net,J/m2)': 202816.0, '直接辐射(J/m2)': 58688.0, '紫外强度(J/m2)': 15264.0, '径流(mm)': 0.0007, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0007, '雷暴概率(TT，K)': 36.58, 'K指数(K)': 18.86, '对流可用位能(J/kg)': 0.12}

--------------------------------------------------------------------------------

文件名: 龙泉驿25.xls
文件大小: 1.02 MB
解析区域: 龙泉驿
解析年份: 2025
读取状态: 成功
数据形状: (3648, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2025-01-01 00:00:00': 1, '2025-04-11 23:00:00': 1, '2025-04-12 01:00:00': 1, '2025-04-12 02:00:00': 1, '2025-04-12 03:00:00': 1, '2025-04-12 04:00:00': 1, '2025-04-12 05:00:00': 1, '2025-04-12 06:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 08:00:00': 1}, '北京时(UTC+8)': {'2025-01-01 08:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 09:00:00': 1, '2025-04-12 10:00:00': 1, '2025-04-12 11:00:00': 1, '2025-04-12 12:00:00': 1, '2025-04-12 13:00:00': 1, '2025-04-12 14:00:00': 1, '2025-04-12 15:00:00': 1, '2025-04-12 16:00:00': 1}, '备注': {'龙泉驿': 3648}}
样本数据:
  行1: {'经度(lon)': 104.3, '纬度(lat)': 30.6, '地面气压(hPa)': 955.91, '气温(℃)': 1.59, '降水量(mm)': 0.0, '露点温度(℃)': 0.81, '经向风速(V,m/s)': -1.74, '纬向风速(U,m/s)': -0.6, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2025-01-01 00:00:00', '北京时(UTC+8)': '2025-01-01 08:00:00', '备注': '龙泉驿'}
  行2: {'经度(lon)': 104.3, '纬度(lat)': 30.6, '地面气压(hPa)': 956.89, '气温(℃)': 3.25, '降水量(mm)': 0.0, '露点温度(℃)': 1.94, '经向风速(V,m/s)': -1.65, '纬向风速(U,m/s)': -0.41, '太阳辐射净强度(net,J/m2)': 132955.0, '太阳辐射总强度(down,J/m2)': 151083.75, '世界时(UTC)': '2025-01-01 01:00:00', '北京时(UTC+8)': '2025-01-01 09:00:00', '备注': '龙泉驿'}
  行3: {'经度(lon)': 104.3, '纬度(lat)': 30.6, '地面气压(hPa)': 957.72, '气温(℃)': 6.37, '降水量(mm)': 0.0, '露点温度(℃)': 2.74, '经向风速(V,m/s)': -1.93, '纬向风速(U,m/s)': -0.55, '太阳辐射净强度(net,J/m2)': 480588.0, '太阳辐射总强度(down,J/m2)': 546125.75, '世界时(UTC)': '2025-01-01 02:00:00', '北京时(UTC+8)': '2025-01-01 10:00:00', '备注': '龙泉驿'}

--------------------------------------------------------------------------------

文件名: 双流25.xls
文件大小: 1.02 MB
解析区域: 双流
解析年份: 2025
读取状态: 成功
数据形状: (3648, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2025-01-01 00:00:00': 1, '2025-04-11 23:00:00': 1, '2025-04-12 01:00:00': 1, '2025-04-12 02:00:00': 1, '2025-04-12 03:00:00': 1, '2025-04-12 04:00:00': 1, '2025-04-12 05:00:00': 1, '2025-04-12 06:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 08:00:00': 1}, '北京时(UTC+8)': {'2025-01-01 08:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 09:00:00': 1, '2025-04-12 10:00:00': 1, '2025-04-12 11:00:00': 1, '2025-04-12 12:00:00': 1, '2025-04-12 13:00:00': 1, '2025-04-12 14:00:00': 1, '2025-04-12 15:00:00': 1, '2025-04-12 16:00:00': 1}, '备注': {'双流区': 3648}}
样本数据:
  行1: {'经度(lon)': 103.9, '纬度(lat)': 30.6, '地面气压(hPa)': 963.39, '气温(℃)': 1.73, '降水量(mm)': 0.0, '露点温度(℃)': 0.86, '经向风速(V,m/s)': -1.78, '纬向风速(U,m/s)': -0.76, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2025-01-01 00:00:00', '北京时(UTC+8)': '2025-01-01 08:00:00', '备注': '双流区'}
  行2: {'经度(lon)': 103.9, '纬度(lat)': 30.6, '地面气压(hPa)': 964.35, '气温(℃)': 3.15, '降水量(mm)': 0.0, '露点温度(℃)': 1.93, '经向风速(V,m/s)': -1.67, '纬向风速(U,m/s)': -0.6, '太阳辐射净强度(net,J/m2)': 128920.0, '太阳辐射总强度(down,J/m2)': 148396.75, '世界时(UTC)': '2025-01-01 01:00:00', '北京时(UTC+8)': '2025-01-01 09:00:00', '备注': '双流区'}
  行3: {'经度(lon)': 103.9, '纬度(lat)': 30.6, '地面气压(hPa)': 965.14, '气温(℃)': 6.24, '降水量(mm)': 0.0, '露点温度(℃)': 2.95, '经向风速(V,m/s)': -1.91, '纬向风速(U,m/s)': -0.64, '太阳辐射净强度(net,J/m2)': 487108.47, '太阳辐射总强度(down,J/m2)': 560681.25, '世界时(UTC)': '2025-01-01 02:00:00', '北京时(UTC+8)': '2025-01-01 10:00:00', '备注': '双流区'}

--------------------------------------------------------------------------------

文件名: 双流24.xls
文件大小: 2.36 MB
解析区域: 双流
解析年份: 2024
读取状态: 成功
数据形状: (8784, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2024-01-01 00:00:00': 1, '2024-09-01 03:00:00': 1, '2024-08-31 21:00:00': 1, '2024-08-31 22:00:00': 1, '2024-08-31 23:00:00': 1, '2024-09-01 00:00:00': 1, '2024-09-01 01:00:00': 1, '2024-09-01 02:00:00': 1, '2024-09-01 04:00:00': 1, '2024-08-31 02:00:00': 1}, '北京时(UTC+8)': {'2024-01-01 08:00:00': 1, '2024-09-01 11:00:00': 1, '2024-09-01 05:00:00': 1, '2024-09-01 06:00:00': 1, '2024-09-01 07:00:00': 1, '2024-09-01 08:00:00': 1, '2024-09-01 09:00:00': 1, '2024-09-01 10:00:00': 1, '2024-09-01 12:00:00': 1, '2024-08-31 10:00:00': 1}, '备注': {'双流区': 8784}}
样本数据:
  行1: {'经度(lon)': 103.9, '纬度(lat)': 30.6, '地面气压(hPa)': 964.95, '气温(℃)': 7.03, '降水量(mm)': 0.008, '露点温度(℃)': 6.49, '经向风速(V,m/s)': -0.56, '纬向风速(U,m/s)': 0.73, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2024-01-01 00:00:00', '北京时(UTC+8)': '2024-01-01 08:00:00', '备注': '双流区'}
  行2: {'经度(lon)': 103.9, '纬度(lat)': 30.6, '地面气压(hPa)': 965.67, '气温(℃)': 7.44, '降水量(mm)': 0.003, '露点温度(℃)': 6.86, '经向风速(V,m/s)': -0.35, '纬向风速(U,m/s)': 0.73, '太阳辐射净强度(net,J/m2)': 68592.0, '太阳辐射总强度(down,J/m2)': 78950.75, '世界时(UTC)': '2024-01-01 01:00:00', '北京时(UTC+8)': '2024-01-01 09:00:00', '备注': '双流区'}
  行3: {'经度(lon)': 103.9, '纬度(lat)': 30.6, '地面气压(hPa)': 966.37, '气温(℃)': 8.89, '降水量(mm)': 0.002, '露点温度(℃)': 7.1, '经向风速(V,m/s)': -0.26, '纬向风速(U,m/s)': 0.8, '太阳辐射净强度(net,J/m2)': 212233.0, '太阳辐射总强度(down,J/m2)': 244279.77, '世界时(UTC)': '2024-01-01 02:00:00', '北京时(UTC+8)': '2024-01-01 10:00:00', '备注': '双流区'}

--------------------------------------------------------------------------------

文件名: 都江堰23.xls
文件大小: 5.49 MB
解析区域: 都江堰
解析年份: 2023
读取状态: 成功
数据形状: (8760, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('O'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'都江堰': 8760}, '世界时(UTC)': {'2023-01-01 00:00:00': 1, '2023-09-01 10:00:00': 1, '2023-09-01 04:00:00': 1, '2023-09-01 05:00:00': 1, '2023-09-01 06:00:00': 1, '2023-09-01 07:00:00': 1, '2023-09-01 08:00:00': 1, '2023-09-01 09:00:00': 1, '2023-09-01 11:00:00': 1, '2023-09-03 05:00:00': 1}, '北京时(UTC+8)': {'2023-01-01 08:00:00': 1, '2023-09-01 18:00:00': 1, '2023-09-01 12:00:00': 1, '2023-09-01 13:00:00': 1, '2023-09-01 14:00:00': 1, '2023-09-01 15:00:00': 1, '2023-09-01 16:00:00': 1, '2023-09-01 17:00:00': 1, '2023-09-01 19:00:00': 1, '2023-09-03 13:00:00': 1}, '雪层温度(℃)': {'*': 8301, '0.01': 48, '-0.17': 4, '-0.75': 4, '-0.94': 4, '-2.73': 4, '-5.84': 4, '-0.07': 3, '-0.13': 3, '-3.45': 3}, '相对湿度(%)': {'76.09': 9, '90.21': 9, '78.1': 9, '82.97': 8, '88.02': 8, '83.16': 8, '88.75': 8, '87.36': 8, '76.22': 8, '83.62': 8}}
样本数据:
  行1: {'参考地名': '都江堰', '经度(lon)': 103.75, '纬度(lat)': 31.0, '世界时(UTC)': '2023-01-01 00:00:00', '北京时(UTC+8)': '2023-01-01 08:00:00', '海平面气压(hPa)': 1030.31, '地面气压(hPa)': 914.77, '气温2m(℃)': 2.9, '降水量(mm)': 0.2, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.17, '积雪密度(kg/m3)': 136.88, '雪层温度(℃)': '-0.17', '地表温度(℃)': 1.83, '露点温度(℃)': 2.31, '相对湿度(%)': '95.89', '蒸发量(mm)': -0.0003, '潜在蒸发量(mm)': -0.0002, '经向风速(V,m/s)': -0.28, '纬向风速(U,m/s)': -0.22, '最大阵风(上一小时内，m/s)': 2.22, '云底高度(m)': '65.17', '低层云量(lcc)': 1.0, '中层云量(mcc)': 0.76, '高层云量(hcc)': 0.0, '总云量(tcc)': 1.0, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.1159, '地表径流(mm)': 0.0515, '地下径流(mm)': 0.0644, '雷暴概率(TT，K)': 26.43, 'K指数(K)': 0.13, '对流可用位能(J/kg)': 18.25}
  行2: {'参考地名': '都江堰', '经度(lon)': 103.75, '纬度(lat)': 31.0, '世界时(UTC)': '2023-01-01 01:00:00', '北京时(UTC+8)': '2023-01-01 09:00:00', '海平面气压(hPa)': 1031.19, '地面气压(hPa)': 915.5, '气温2m(℃)': 2.93, '降水量(mm)': 0.15, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.17, '积雪密度(kg/m3)': 138.01, '雪层温度(℃)': '-0.24', '地表温度(℃)': 3.07, '露点温度(℃)': 2.42, '相对湿度(%)': '96.45', '蒸发量(mm)': -0.0042, '潜在蒸发量(mm)': -0.0026, '经向风速(V,m/s)': -0.37, '纬向风速(U,m/s)': -0.21, '最大阵风(上一小时内，m/s)': 2.87, '云底高度(m)': '65.21', '低层云量(lcc)': 1.0, '中层云量(mcc)': 0.68, '高层云量(hcc)': 0.0, '总云量(tcc)': 1.0, '总太阳辐射度(down,J/m2)': 68288.0, '净太阳辐射度(net,J/m2)': 59776.0, '直接辐射(J/m2)': 9152.0, '紫外强度(J/m2)': 12096.0, '径流(mm)': 0.1023, '地表径流(mm)': 0.0379, '地下径流(mm)': 0.0644, '雷暴概率(TT，K)': 26.37, 'K指数(K)': -0.9, '对流可用位能(J/kg)': 15.25}
  行3: {'参考地名': '都江堰', '经度(lon)': 103.75, '纬度(lat)': 31.0, '世界时(UTC)': '2023-01-01 02:00:00', '北京时(UTC+8)': '2023-01-01 10:00:00', '海平面气压(hPa)': 1031.52, '地面气压(hPa)': 915.75, '气温2m(℃)': 3.39, '降水量(mm)': 0.179, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.17, '积雪密度(kg/m3)': 139.16, '雪层温度(℃)': '-0.16', '地表温度(℃)': 4.65, '露点温度(℃)': 2.85, '相对湿度(%)': '96.22', '蒸发量(mm)': -0.0261, '潜在蒸发量(mm)': -0.019, '经向风速(V,m/s)': -0.41, '纬向风速(U,m/s)': -0.18, '最大阵风(上一小时内，m/s)': 3.42, '云底高度(m)': '83.04', '低层云量(lcc)': 0.9, '中层云量(mcc)': 0.73, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.92, '总太阳辐射度(down,J/m2)': 321088.0, '净太阳辐射度(net,J/m2)': 281600.0, '直接辐射(J/m2)': 87872.0, '紫外强度(J/m2)': 45352.0, '径流(mm)': 0.1054, '地表径流(mm)': 0.041, '地下径流(mm)': 0.0644, '雷暴概率(TT，K)': 26.38, 'K指数(K)': -1.39, '对流可用位能(J/kg)': 0.88}

--------------------------------------------------------------------------------

文件名: 龙泉驿24.xls
文件大小: 2.36 MB
解析区域: 龙泉驿
解析年份: 2024
读取状态: 成功
数据形状: (8784, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2024-01-01 00:00:00': 1, '2024-09-01 03:00:00': 1, '2024-08-31 21:00:00': 1, '2024-08-31 22:00:00': 1, '2024-08-31 23:00:00': 1, '2024-09-01 00:00:00': 1, '2024-09-01 01:00:00': 1, '2024-09-01 02:00:00': 1, '2024-09-01 04:00:00': 1, '2024-08-31 02:00:00': 1}, '北京时(UTC+8)': {'2024-01-01 08:00:00': 1, '2024-09-01 11:00:00': 1, '2024-09-01 05:00:00': 1, '2024-09-01 06:00:00': 1, '2024-09-01 07:00:00': 1, '2024-09-01 08:00:00': 1, '2024-09-01 09:00:00': 1, '2024-09-01 10:00:00': 1, '2024-09-01 12:00:00': 1, '2024-08-31 10:00:00': 1}, '备注': {'龙泉驿': 8784}}
样本数据:
  行1: {'经度(lon)': 104.3, '纬度(lat)': 30.6, '地面气压(hPa)': 957.62, '气温(℃)': 6.63, '降水量(mm)': 0.002, '露点温度(℃)': 5.92, '经向风速(V,m/s)': -0.3, '纬向风速(U,m/s)': 0.94, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2024-01-01 00:00:00', '北京时(UTC+8)': '2024-01-01 08:00:00', '备注': '龙泉驿'}
  行2: {'经度(lon)': 104.3, '纬度(lat)': 30.6, '地面气压(hPa)': 958.35, '气温(℃)': 7.07, '降水量(mm)': 0.007, '露点温度(℃)': 6.16, '经向风速(V,m/s)': -0.22, '纬向风速(U,m/s)': 0.9, '太阳辐射净强度(net,J/m2)': 59648.0, '太阳辐射总强度(down,J/m2)': 67784.0, '世界时(UTC)': '2024-01-01 01:00:00', '北京时(UTC+8)': '2024-01-01 09:00:00', '备注': '龙泉驿'}
  行3: {'经度(lon)': 104.3, '纬度(lat)': 30.6, '地面气压(hPa)': 959.09, '气温(℃)': 8.23, '降水量(mm)': 0.019, '露点温度(℃)': 6.6, '经向风速(V,m/s)': -0.22, '纬向风速(U,m/s)': 0.87, '太阳辐射净强度(net,J/m2)': 184195.0, '太阳辐射总强度(down,J/m2)': 209322.0, '世界时(UTC)': '2024-01-01 02:00:00', '北京时(UTC+8)': '2024-01-01 10:00:00', '备注': '龙泉驿'}

--------------------------------------------------------------------------------

文件名: 彭州25.xls
文件大小: 2.39 MB
解析区域: 彭州
解析年份: 2025
读取状态: 成功
数据形状: (3648, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('O'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'彭州': 3648}, '世界时(UTC)': {'2025-01-01 00:00:00': 1, '2025-04-11 23:00:00': 1, '2025-04-12 01:00:00': 1, '2025-04-12 02:00:00': 1, '2025-04-12 03:00:00': 1, '2025-04-12 04:00:00': 1, '2025-04-12 05:00:00': 1, '2025-04-12 06:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 08:00:00': 1}, '北京时(UTC+8)': {'2025-01-01 08:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 09:00:00': 1, '2025-04-12 10:00:00': 1, '2025-04-12 11:00:00': 1, '2025-04-12 12:00:00': 1, '2025-04-12 13:00:00': 1, '2025-04-12 14:00:00': 1, '2025-04-12 15:00:00': 1, '2025-04-12 16:00:00': 1}, '雪层温度(℃)': {'*': 3592, '0.01': 15, '0.0': 2, '-1.14': 2, '-2.32': 1, '-0.16': 1, '-0.04': 1, '-0.32': 1, '-0.53': 1, '-0.28': 1}, '相对湿度(%)': {'66.54': 5, '81.76': 5, '85.91': 5, '68.76': 4, '70.21': 4, '85.66': 4, '64.1': 4, '90.63': 4, '84.76': 4, '71.21': 4}}
样本数据:
  行1: {'参考地名': '彭州', '经度(lon)': 104.0, '纬度(lat)': 31.0, '世界时(UTC)': '2025-01-01 00:00:00', '北京时(UTC+8)': '2025-01-01 08:00:00', '海平面气压(hPa)': 1022.33, '地面气压(hPa)': 953.82, '气温2m(℃)': 2.52, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': -0.37, '露点温度(℃)': 0.15, '相对湿度(%)': '84.35', '蒸发量(mm)': 0.0003, '潜在蒸发量(mm)': 0.0006, '经向风速(V,m/s)': -1.73, '纬向风速(U,m/s)': -1.2, '最大阵风(上一小时内，m/s)': 3.24, '云底高度(m)': '836.37', '低层云量(lcc)': 0.39, '中层云量(mcc)': 0.25, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.41, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.0007, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0007, '雷暴概率(TT，K)': 34.27, 'K指数(K)': 2.72, '对流可用位能(J/kg)': 0.0}
  行2: {'参考地名': '彭州', '经度(lon)': 104.0, '纬度(lat)': 31.0, '世界时(UTC)': '2025-01-01 01:00:00', '北京时(UTC+8)': '2025-01-01 09:00:00', '海平面气压(hPa)': 1023.35, '地面气压(hPa)': 954.7, '气温2m(℃)': 2.94, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 3.4, '露点温度(℃)': 0.44, '相对湿度(%)': '83.59', '蒸发量(mm)': -0.0032, '潜在蒸发量(mm)': -0.0042, '经向风速(V,m/s)': -1.7, '纬向风速(U,m/s)': -0.87, '最大阵风(上一小时内，m/s)': 2.86, '云底高度(m)': '885.45', '低层云量(lcc)': 0.28, '中层云量(mcc)': 0.18, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.3, '总太阳辐射度(down,J/m2)': 130752.0, '净太阳辐射度(net,J/m2)': 110144.0, '直接辐射(J/m2)': 72000.0, '紫外强度(J/m2)': 12056.0, '径流(mm)': 0.0005, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0007, '雷暴概率(TT，K)': 34.31, 'K指数(K)': 2.63, '对流可用位能(J/kg)': 0.0}
  行3: {'参考地名': '彭州', '经度(lon)': 104.0, '纬度(lat)': 31.0, '世界时(UTC)': '2025-01-01 02:00:00', '北京时(UTC+8)': '2025-01-01 10:00:00', '海平面气压(hPa)': 1024.2, '地面气压(hPa)': 955.46, '气温2m(℃)': 7.95, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 8.4, '露点温度(℃)': 2.25, '相对湿度(%)': '67.25', '蒸发量(mm)': -0.0268, '潜在蒸发量(mm)': -0.0404, '经向风速(V,m/s)': -0.59, '纬向风速(U,m/s)': -0.86, '最大阵风(上一小时内，m/s)': 3.85, '云底高度(m)': '816.47', '低层云量(lcc)': 0.45, '中层云量(mcc)': 0.25, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.48, '总太阳辐射度(down,J/m2)': 510720.0, '净太阳辐射度(net,J/m2)': 433600.0, '直接辐射(J/m2)': 298368.0, '紫外强度(J/m2)': 60080.0, '径流(mm)': 0.0007, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0007, '雷暴概率(TT，K)': 34.74, 'K指数(K)': 2.85, '对流可用位能(J/kg)': 0.0}

--------------------------------------------------------------------------------

文件名: 青白江25.xls
文件大小: 1.02 MB
解析区域: 青白江
解析年份: 2025
读取状态: 成功
数据形状: (3648, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2025-01-01 00:00:00': 1, '2025-04-11 23:00:00': 1, '2025-04-12 01:00:00': 1, '2025-04-12 02:00:00': 1, '2025-04-12 03:00:00': 1, '2025-04-12 04:00:00': 1, '2025-04-12 05:00:00': 1, '2025-04-12 06:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 08:00:00': 1}, '北京时(UTC+8)': {'2025-01-01 08:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 09:00:00': 1, '2025-04-12 10:00:00': 1, '2025-04-12 11:00:00': 1, '2025-04-12 12:00:00': 1, '2025-04-12 13:00:00': 1, '2025-04-12 14:00:00': 1, '2025-04-12 15:00:00': 1, '2025-04-12 16:00:00': 1}, '备注': {'青白江': 3648}}
样本数据:
  行1: {'经度(lon)': 104.2, '纬度(lat)': 30.9, '地面气压(hPa)': 965.59, '气温(℃)': 2.34, '降水量(mm)': 0.0, '露点温度(℃)': 1.08, '经向风速(V,m/s)': -1.76, '纬向风速(U,m/s)': -1.14, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2025-01-01 00:00:00', '北京时(UTC+8)': '2025-01-01 08:00:00', '备注': '青白江'}
  行2: {'经度(lon)': 104.2, '纬度(lat)': 30.9, '地面气压(hPa)': 966.53, '气温(℃)': 3.78, '降水量(mm)': 0.0, '露点温度(℃)': 2.05, '经向风速(V,m/s)': -1.64, '纬向风速(U,m/s)': -0.94, '太阳辐射净强度(net,J/m2)': 114149.5, '太阳辐射总强度(down,J/m2)': 135461.0, '世界时(UTC)': '2025-01-01 01:00:00', '北京时(UTC+8)': '2025-01-01 09:00:00', '备注': '青白江'}
  行3: {'经度(lon)': 104.2, '纬度(lat)': 30.9, '地面气压(hPa)': 967.25, '气温(℃)': 6.54, '降水量(mm)': 0.0, '露点温度(℃)': 2.96, '经向风速(V,m/s)': -1.77, '纬向风速(U,m/s)': -0.89, '太阳辐射净强度(net,J/m2)': 425960.47, '太阳辐射总强度(down,J/m2)': 505487.53, '世界时(UTC)': '2025-01-01 02:00:00', '北京时(UTC+8)': '2025-01-01 10:00:00', '备注': '青白江'}

--------------------------------------------------------------------------------

文件名: 青白江24.xls
文件大小: 2.36 MB
解析区域: 青白江
解析年份: 2024
读取状态: 成功
数据形状: (8784, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2024-01-01 00:00:00': 1, '2024-09-01 03:00:00': 1, '2024-08-31 21:00:00': 1, '2024-08-31 22:00:00': 1, '2024-08-31 23:00:00': 1, '2024-09-01 00:00:00': 1, '2024-09-01 01:00:00': 1, '2024-09-01 02:00:00': 1, '2024-09-01 04:00:00': 1, '2024-08-31 02:00:00': 1}, '北京时(UTC+8)': {'2024-01-01 08:00:00': 1, '2024-09-01 11:00:00': 1, '2024-09-01 05:00:00': 1, '2024-09-01 06:00:00': 1, '2024-09-01 07:00:00': 1, '2024-09-01 08:00:00': 1, '2024-09-01 09:00:00': 1, '2024-09-01 10:00:00': 1, '2024-09-01 12:00:00': 1, '2024-08-31 10:00:00': 1}, '备注': {'青白江': 8784}}
样本数据:
  行1: {'经度(lon)': 104.2, '纬度(lat)': 30.9, '地面气压(hPa)': 966.89, '气温(℃)': 6.62, '降水量(mm)': 0.0, '露点温度(℃)': 6.08, '经向风速(V,m/s)': 0.1, '纬向风速(U,m/s)': 0.86, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2024-01-01 00:00:00', '北京时(UTC+8)': '2024-01-01 08:00:00', '备注': '青白江'}
  行2: {'经度(lon)': 104.2, '纬度(lat)': 30.9, '地面气压(hPa)': 967.6, '气温(℃)': 7.18, '降水量(mm)': 0.0, '露点温度(℃)': 6.53, '经向风速(V,m/s)': 0.21, '纬向风速(U,m/s)': 0.69, '太阳辐射净强度(net,J/m2)': 60595.75, '太阳辐射总强度(down,J/m2)': 71909.75, '世界时(UTC)': '2024-01-01 01:00:00', '北京时(UTC+8)': '2024-01-01 09:00:00', '备注': '青白江'}
  行3: {'经度(lon)': 104.2, '纬度(lat)': 30.9, '地面气压(hPa)': 968.34, '气温(℃)': 8.77, '降水量(mm)': 0.006, '露点温度(℃)': 7.15, '经向风速(V,m/s)': 0.18, '纬向风速(U,m/s)': 0.49, '太阳辐射净强度(net,J/m2)': 183290.23, '太阳辐射总强度(down,J/m2)': 217512.23, '世界时(UTC)': '2024-01-01 02:00:00', '北京时(UTC+8)': '2024-01-01 10:00:00', '备注': '青白江'}

--------------------------------------------------------------------------------

文件名: 金堂县23.xls
文件大小: 5.50 MB
解析区域: 金堂县
解析年份: 2023
读取状态: 成功
数据形状: (8760, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('O'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'金堂县': 8760}, '世界时(UTC)': {'2023-01-01 00:00:00': 1, '2023-09-01 10:00:00': 1, '2023-09-01 04:00:00': 1, '2023-09-01 05:00:00': 1, '2023-09-01 06:00:00': 1, '2023-09-01 07:00:00': 1, '2023-09-01 08:00:00': 1, '2023-09-01 09:00:00': 1, '2023-09-01 11:00:00': 1, '2023-09-03 05:00:00': 1}, '北京时(UTC+8)': {'2023-01-01 08:00:00': 1, '2023-09-01 18:00:00': 1, '2023-09-01 12:00:00': 1, '2023-09-01 13:00:00': 1, '2023-09-01 14:00:00': 1, '2023-09-01 15:00:00': 1, '2023-09-01 16:00:00': 1, '2023-09-01 17:00:00': 1, '2023-09-01 19:00:00': 1, '2023-09-03 13:00:00': 1}, '雪层温度(℃)': {'*': 8759, '-0.72': 1}, '相对湿度(%)': {'*': 20, '62.83': 7, '59.17': 7, '75.13': 7, '74.23': 7, '46.88': 7, '89.57': 6, '69.88': 6, '62.81': 6, '75.29': 6}}
样本数据:
  行1: {'参考地名': '金堂县', '经度(lon)': 104.5, '纬度(lat)': 30.75, '世界时(UTC)': '2023-01-01 00:00:00', '北京时(UTC+8)': '2023-01-01 08:00:00', '海平面气压(hPa)': 1030.24, '地面气压(hPa)': 967.75, '气温2m(℃)': 4.22, '降水量(mm)': 0.001, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 3.75, '露点温度(℃)': 4.21, '相对湿度(%)': '99.96', '蒸发量(mm)': 0.0007, '潜在蒸发量(mm)': 0.0003, '经向风速(V,m/s)': -0.54, '纬向风速(U,m/s)': 0.73, '最大阵风(上一小时内，m/s)': 2.31, '云底高度(m)': '29.67', '低层云量(lcc)': 0.53, '中层云量(mcc)': 0.65, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.74, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.0014, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0017, '雷暴概率(TT，K)': 25.8, 'K指数(K)': 5.71, '对流可用位能(J/kg)': 4.44}
  行2: {'参考地名': '金堂县', '经度(lon)': 104.5, '纬度(lat)': 30.75, '世界时(UTC)': '2023-01-01 01:00:00', '北京时(UTC+8)': '2023-01-01 09:00:00', '海平面气压(hPa)': 1031.0, '地面气压(hPa)': 968.39, '气温2m(℃)': 4.43, '降水量(mm)': 0.001, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 5.15, '露点温度(℃)': 4.43, '相对湿度(%)': '99.99', '蒸发量(mm)': -0.007, '潜在蒸发量(mm)': -0.0035, '经向风速(V,m/s)': -0.72, '纬向风速(U,m/s)': 0.22, '最大阵风(上一小时内，m/s)': 2.91, '云底高度(m)': '29.71', '低层云量(lcc)': 0.63, '中层云量(mcc)': 0.66, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.88, '总太阳辐射度(down,J/m2)': 93952.0, '净太阳辐射度(net,J/m2)': 80576.0, '直接辐射(J/m2)': 19584.0, '紫外强度(J/m2)': 10688.0, '径流(mm)': 0.0017, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0017, '雷暴概率(TT，K)': 25.91, 'K指数(K)': 5.93, '对流可用位能(J/kg)': 1.44}
  行3: {'参考地名': '金堂县', '经度(lon)': 104.5, '纬度(lat)': 30.75, '世界时(UTC)': '2023-01-01 02:00:00', '北京时(UTC+8)': '2023-01-01 10:00:00', '海平面气压(hPa)': 1031.21, '地面气压(hPa)': 968.52, '气温2m(℃)': 5.81, '降水量(mm)': 0.002, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 7.96, '露点温度(℃)': 5.81, '相对湿度(%)': '*', '蒸发量(mm)': -0.0305, '潜在蒸发量(mm)': -0.0211, '经向风速(V,m/s)': -0.7, '纬向风速(U,m/s)': 0.2, '最大阵风(上一小时内，m/s)': 3.29, '云底高度(m)': '30.04', '低层云量(lcc)': 0.63, '中层云量(mcc)': 0.6, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.78, '总太阳辐射度(down,J/m2)': 356160.0, '净太阳辐射度(net,J/m2)': 304064.0, '直接辐射(J/m2)': 94080.0, '紫外强度(J/m2)': 35568.0, '径流(mm)': 0.0014, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0017, '雷暴概率(TT，K)': 25.94, 'K指数(K)': 5.97, '对流可用位能(J/kg)': 0.5}

--------------------------------------------------------------------------------

文件名: 青白江23.xls
文件大小: 2.36 MB
解析区域: 青白江
解析年份: 2023
读取状态: 成功
数据形状: (8760, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2023-01-01 00:00:00': 1, '2023-09-01 10:00:00': 1, '2023-09-01 04:00:00': 1, '2023-09-01 05:00:00': 1, '2023-09-01 06:00:00': 1, '2023-09-01 07:00:00': 1, '2023-09-01 08:00:00': 1, '2023-09-01 09:00:00': 1, '2023-09-01 11:00:00': 1, '2023-09-03 05:00:00': 1}, '北京时(UTC+8)': {'2023-01-01 08:00:00': 1, '2023-09-01 18:00:00': 1, '2023-09-01 12:00:00': 1, '2023-09-01 13:00:00': 1, '2023-09-01 14:00:00': 1, '2023-09-01 15:00:00': 1, '2023-09-01 16:00:00': 1, '2023-09-01 17:00:00': 1, '2023-09-01 19:00:00': 1, '2023-09-03 13:00:00': 1}, '备注': {'青白江': 8760}}
样本数据:
  行1: {'经度(lon)': 104.2, '纬度(lat)': 30.9, '地面气压(hPa)': 972.67, '气温(℃)': 3.31, '降水量(mm)': 0.001, '露点温度(℃)': 3.16, '经向风速(V,m/s)': 0.5, '纬向风速(U,m/s)': 0.01, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2023-01-01 00:00:00', '北京时(UTC+8)': '2023-01-01 08:00:00', '备注': '青白江'}
  行2: {'经度(lon)': 104.2, '纬度(lat)': 30.9, '地面气压(hPa)': 973.41, '气温(℃)': 3.82, '降水量(mm)': 0.001, '露点温度(℃)': 3.67, '经向风速(V,m/s)': 0.47, '纬向风速(U,m/s)': 0.11, '太阳辐射净强度(net,J/m2)': 70538.5, '太阳辐射总强度(down,J/m2)': 83707.25, '世界时(UTC)': '2023-01-01 01:00:00', '北京时(UTC+8)': '2023-01-01 09:00:00', '备注': '青白江'}
  行3: {'经度(lon)': 104.2, '纬度(lat)': 30.9, '地面气压(hPa)': 973.94, '气温(℃)': 5.01, '降水量(mm)': 0.001, '露点温度(℃)': 4.31, '经向风速(V,m/s)': 0.35, '纬向风速(U,m/s)': 0.4, '太阳辐射净强度(net,J/m2)': 294394.5, '太阳辐射总强度(down,J/m2)': 349354.75, '世界时(UTC)': '2023-01-01 02:00:00', '北京时(UTC+8)': '2023-01-01 10:00:00', '备注': '青白江'}

--------------------------------------------------------------------------------

文件名: 金堂县25.xls
文件大小: 2.39 MB
解析区域: 金堂县
解析年份: 2025
读取状态: 成功
数据形状: (3648, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('float64'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'金堂县': 3648}, '世界时(UTC)': {'2025-01-01 00:00:00': 1, '2025-04-11 23:00:00': 1, '2025-04-12 01:00:00': 1, '2025-04-12 02:00:00': 1, '2025-04-12 03:00:00': 1, '2025-04-12 04:00:00': 1, '2025-04-12 05:00:00': 1, '2025-04-12 06:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 08:00:00': 1}, '北京时(UTC+8)': {'2025-01-01 08:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 09:00:00': 1, '2025-04-12 10:00:00': 1, '2025-04-12 11:00:00': 1, '2025-04-12 12:00:00': 1, '2025-04-12 13:00:00': 1, '2025-04-12 14:00:00': 1, '2025-04-12 15:00:00': 1, '2025-04-12 16:00:00': 1}, '雪层温度(℃)': {'*': 3631, '0.01': 16, '-0.18': 1}, '云底高度(m)': {'*': 285, '828.27': 2, '932.15': 2, '487.26': 2, '1047.47': 2, '809.99': 2, '853.45': 2, '906.61': 2, '878.32': 2, '617.8': 2}}
样本数据:
  行1: {'参考地名': '金堂县', '经度(lon)': 104.5, '纬度(lat)': 30.75, '世界时(UTC)': '2025-01-01 00:00:00', '北京时(UTC+8)': '2025-01-01 08:00:00', '海平面气压(hPa)': 1022.18, '地面气压(hPa)': 960.48, '气温2m(℃)': 2.37, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 1.4, '露点温度(℃)': 1.76, '相对湿度(%)': 95.78, '蒸发量(mm)': 0.0006, '潜在蒸发量(mm)': 0.0015, '经向风速(V,m/s)': -1.79, '纬向风速(U,m/s)': -0.45, '最大阵风(上一小时内，m/s)': 3.37, '云底高度(m)': '1064.62', '低层云量(lcc)': 0.16, '中层云量(mcc)': 0.05, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.17, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.0005, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0005, '雷暴概率(TT，K)': 32.13, 'K指数(K)': 1.16, '对流可用位能(J/kg)': 0.0}
  行2: {'参考地名': '金堂县', '经度(lon)': 104.5, '纬度(lat)': 30.75, '世界时(UTC)': '2025-01-01 01:00:00', '北京时(UTC+8)': '2025-01-01 09:00:00', '海平面气压(hPa)': 1022.98, '地面气压(hPa)': 961.18, '气温2m(℃)': 2.87, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 4.7, '露点温度(℃)': 1.5, '相对湿度(%)': 90.7, '蒸发量(mm)': -0.0077, '潜在蒸发量(mm)': -0.0105, '经向风速(V,m/s)': -2.41, '纬向风速(U,m/s)': -1.0, '最大阵风(上一小时内，m/s)': 4.97, '云底高度(m)': '1064.7', '低层云量(lcc)': 0.16, '中层云量(mcc)': 0.05, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.17, '总太阳辐射度(down,J/m2)': 153536.0, '净太阳辐射度(net,J/m2)': 130752.0, '直接辐射(J/m2)': 74368.0, '紫外强度(J/m2)': 13808.0, '径流(mm)': 0.0005, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0005, '雷暴概率(TT，K)': 32.43, 'K指数(K)': 1.79, '对流可用位能(J/kg)': 0.0}
  行3: {'参考地名': '金堂县', '经度(lon)': 104.5, '纬度(lat)': 30.75, '世界时(UTC)': '2025-01-01 02:00:00', '北京时(UTC+8)': '2025-01-01 10:00:00', '海平面气压(hPa)': 1023.8, '地面气压(hPa)': 961.87, '气温2m(℃)': 7.78, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 8.85, '露点温度(℃)': 3.16, '相对湿度(%)': 72.53, '蒸发量(mm)': -0.0341, '潜在蒸发量(mm)': -0.0524, '经向风速(V,m/s)': -1.83, '纬向风速(U,m/s)': -0.81, '最大阵风(上一小时内，m/s)': 5.3, '云底高度(m)': '1065.22', '低层云量(lcc)': 0.28, '中层云量(mcc)': 0.06, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.29, '总太阳辐射度(down,J/m2)': 576384.0, '净太阳辐射度(net,J/m2)': 493888.0, '直接辐射(J/m2)': 287168.0, '紫外强度(J/m2)': 60432.0, '径流(mm)': 0.0005, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0005, '雷暴概率(TT，K)': 33.04, 'K指数(K)': 3.01, '对流可用位能(J/kg)': 0.0}

--------------------------------------------------------------------------------

文件名: 金堂县24.xls
文件大小: 5.53 MB
解析区域: 金堂县
解析年份: 2024
读取状态: 成功
数据形状: (8808, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('O'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'金堂县': 8808}, '世界时(UTC)': {'2024-09-01 12:00:00': 2, '2024-09-01 02:00:00': 2, '2024-09-01 22:00:00': 2, '2024-09-01 21:00:00': 2, '2024-09-01 20:00:00': 2, '2024-09-01 19:00:00': 2, '2024-09-01 18:00:00': 2, '2024-09-01 17:00:00': 2, '2024-09-01 16:00:00': 2, '2024-09-01 15:00:00': 2}, '北京时(UTC+8)': {'2024-09-01 20:00:00': 2, '2024-09-01 10:00:00': 2, '2024-09-02 06:00:00': 2, '2024-09-02 05:00:00': 2, '2024-09-02 04:00:00': 2, '2024-09-02 03:00:00': 2, '2024-09-02 02:00:00': 2, '2024-09-02 01:00:00': 2, '2024-09-02 00:00:00': 2, '2024-09-01 23:00:00': 2}, '雪层温度(℃)': {'*': 8654, '0.01': 40, '-0.01': 7, '-0.03': 6, '-0.05': 5, '-0.02': 4, '0.0': 4, '-0.06': 3, '-0.04': 3, '-0.17': 3}, '相对湿度(%)': {'*': 11, '88.23': 8, '84.66': 8, '87.11': 8, '84.15': 8, '91.75': 7, '90.23': 7, '88.62': 7, '85.94': 7, '71.45': 7}}
样本数据:
  行1: {'参考地名': '金堂县', '经度(lon)': 104.5, '纬度(lat)': 30.75, '世界时(UTC)': '2024-01-01 00:00:00', '北京时(UTC+8)': '2024-01-01 08:00:00', '海平面气压(hPa)': 1023.15, '地面气压(hPa)': 961.82, '气温2m(℃)': 6.96, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 7.41, '露点温度(℃)': 6.11, '相对湿度(%)': '94.33', '蒸发量(mm)': -0.0027, '潜在蒸发量(mm)': -0.0027, '经向风速(V,m/s)': 0.02, '纬向风速(U,m/s)': 1.07, '最大阵风(上一小时内，m/s)': 2.41, '云底高度(m)': '1063.97', '低层云量(lcc)': 0.97, '中层云量(mcc)': 0.99, '高层云量(hcc)': 0.88, '总云量(tcc)': 1.0, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.0005, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0005, '雷暴概率(TT，K)': 37.66, 'K指数(K)': 20.14, '对流可用位能(J/kg)': 0.0}
  行2: {'参考地名': '金堂县', '经度(lon)': 104.5, '纬度(lat)': 30.75, '世界时(UTC)': '2024-01-01 01:00:00', '北京时(UTC+8)': '2024-01-01 09:00:00', '海平面气压(hPa)': 1023.63, '地面气压(hPa)': 962.27, '气温2m(℃)': 7.01, '降水量(mm)': 0.002, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 7.81, '露点温度(℃)': 6.0, '相对湿度(%)': '93.31', '蒸发量(mm)': -0.0046, '潜在蒸发量(mm)': -0.0062, '经向风速(V,m/s)': -0.47, '纬向风速(U,m/s)': 1.11, '最大阵风(上一小时内，m/s)': 2.74, '云底高度(m)': '1034.7', '低层云量(lcc)': 0.97, '中层云量(mcc)': 0.99, '高层云量(hcc)': 0.83, '总云量(tcc)': 1.0, '总太阳辐射度(down,J/m2)': 57600.0, '净太阳辐射度(net,J/m2)': 48064.0, '直接辐射(J/m2)': 4864.0, '紫外强度(J/m2)': 8912.0, '径流(mm)': 0.0005, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0006, '雷暴概率(TT，K)': 37.73, 'K指数(K)': 20.46, '对流可用位能(J/kg)': 0.0}
  行3: {'参考地名': '金堂县', '经度(lon)': 104.5, '纬度(lat)': 30.75, '世界时(UTC)': '2024-01-01 02:00:00', '北京时(UTC+8)': '2024-01-01 10:00:00', '海平面气压(hPa)': 1023.94, '地面气压(hPa)': 962.55, '气温2m(℃)': 8.36, '降水量(mm)': 0.022, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 8.56, '露点温度(℃)': 6.4, '相对湿度(%)': '87.47', '蒸发量(mm)': -0.0141, '潜在蒸发量(mm)': -0.0203, '经向风速(V,m/s)': -0.54, '纬向风速(U,m/s)': 0.99, '最大阵风(上一小时内，m/s)': 2.89, '云底高度(m)': '1174.22', '低层云量(lcc)': 1.0, '中层云量(mcc)': 0.99, '高层云量(hcc)': 0.84, '总云量(tcc)': 1.0, '总太阳辐射度(down,J/m2)': 185472.0, '净太阳辐射度(net,J/m2)': 159232.0, '直接辐射(J/m2)': 48640.0, '紫外强度(J/m2)': 13080.0, '径流(mm)': 0.0007, '地表径流(mm)': 0.0002, '地下径流(mm)': 0.0006, '雷暴概率(TT，K)': 37.77, 'K指数(K)': 20.68, '对流可用位能(J/kg)': 0.0}

--------------------------------------------------------------------------------

文件名: 新都23.xls
文件大小: 2.36 MB
解析区域: 新都
解析年份: 2023
读取状态: 成功
数据形状: (8760, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2023-01-01 00:00:00': 1, '2023-09-01 10:00:00': 1, '2023-09-01 04:00:00': 1, '2023-09-01 05:00:00': 1, '2023-09-01 06:00:00': 1, '2023-09-01 07:00:00': 1, '2023-09-01 08:00:00': 1, '2023-09-01 09:00:00': 1, '2023-09-01 11:00:00': 1, '2023-09-03 05:00:00': 1}, '北京时(UTC+8)': {'2023-01-01 08:00:00': 1, '2023-09-01 18:00:00': 1, '2023-09-01 12:00:00': 1, '2023-09-01 13:00:00': 1, '2023-09-01 14:00:00': 1, '2023-09-01 15:00:00': 1, '2023-09-01 16:00:00': 1, '2023-09-01 17:00:00': 1, '2023-09-01 19:00:00': 1, '2023-09-03 13:00:00': 1}, '备注': {'新都区': 8760}}
样本数据:
  行1: {'经度(lon)': 104.2, '纬度(lat)': 30.8, '地面气压(hPa)': 970.35, '气温(℃)': 3.02, '降水量(mm)': 0.001, '露点温度(℃)': 2.94, '经向风速(V,m/s)': 0.38, '纬向风速(U,m/s)': 0.33, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2023-01-01 00:00:00', '北京时(UTC+8)': '2023-01-01 08:00:00', '备注': '新都区'}
  行2: {'经度(lon)': 104.2, '纬度(lat)': 30.8, '地面气压(hPa)': 971.05, '气温(℃)': 3.61, '降水量(mm)': 0.001, '露点温度(℃)': 3.43, '经向风速(V,m/s)': 0.47, '纬向风速(U,m/s)': 0.38, '太阳辐射净强度(net,J/m2)': 74365.25, '太阳辐射总强度(down,J/m2)': 86020.25, '世界时(UTC)': '2023-01-01 01:00:00', '北京时(UTC+8)': '2023-01-01 09:00:00', '备注': '新都区'}
  行3: {'经度(lon)': 104.2, '纬度(lat)': 30.8, '地面气压(hPa)': 971.56, '气温(℃)': 4.69, '降水量(mm)': 0.001, '露点温度(℃)': 4.12, '经向风速(V,m/s)': 0.38, '纬向风速(U,m/s)': 0.73, '太阳辐射净强度(net,J/m2)': 294551.75, '太阳辐射总强度(down,J/m2)': 340616.75, '世界时(UTC)': '2023-01-01 02:00:00', '北京时(UTC+8)': '2023-01-01 10:00:00', '备注': '新都区'}

--------------------------------------------------------------------------------

文件名: 攀枝花23.xls
文件大小: 3.14 MB
解析区域: 攀枝花
解析年份: 2023
读取状态: 成功
数据形状: (8760, 18)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '相对湿度(%)', '地表温度(℃)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '相对湿度(%)': dtype('O'), '地表温度(℃)': dtype('float64'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64')}
唯一值统计: {'参考地名': {'攀枝花': 8760}, '世界时(UTC)': {'2023-01-01 00:00:00': 1, '2023-09-01 10:00:00': 1, '2023-09-01 04:00:00': 1, '2023-09-01 05:00:00': 1, '2023-09-01 06:00:00': 1, '2023-09-01 07:00:00': 1, '2023-09-01 08:00:00': 1, '2023-09-01 09:00:00': 1, '2023-09-01 11:00:00': 1, '2023-09-03 05:00:00': 1}, '北京时(UTC+8)': {'2023-01-01 08:00:00': 1, '2023-09-01 18:00:00': 1, '2023-09-01 12:00:00': 1, '2023-09-01 13:00:00': 1, '2023-09-01 14:00:00': 1, '2023-09-01 15:00:00': 1, '2023-09-01 16:00:00': 1, '2023-09-01 17:00:00': 1, '2023-09-01 19:00:00': 1, '2023-09-03 13:00:00': 1}, '相对湿度(%)': {'24.47': 7, '55.7': 6, '68.02': 6, '42.95': 6, '44.04': 6, '22.25': 6, '64.1': 6, '55.84': 6, '68.01': 6, '39.61': 6}}
样本数据:
  行1: {'参考地名': '攀枝花', '经度(lon)': 101.75, '纬度(lat)': 26.5, '世界时(UTC)': '2023-01-01 00:00:00', '北京时(UTC+8)': '2023-01-01 08:00:00', '海平面气压(hPa)': 1020.13, '地面气压(hPa)': 834.17, '气温2m(℃)': 5.88, '降水量(mm)': 0.062, '相对湿度(%)': '93.2', '地表温度(℃)': 2.47, '蒸发量(mm)': 0.0015, '潜在蒸发量(mm)': 0.0014, '经向风速(V,m/s)': 1.01, '纬向风速(U,m/s)': -0.35, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0}
  行2: {'参考地名': '攀枝花', '经度(lon)': 101.75, '纬度(lat)': 26.5, '世界时(UTC)': '2023-01-01 01:00:00', '北京时(UTC+8)': '2023-01-01 09:00:00', '海平面气压(hPa)': 1020.95, '地面气压(hPa)': 834.83, '气温2m(℃)': 6.04, '降水量(mm)': 0.038, '相对湿度(%)': '94.19', '地表温度(℃)': 6.27, '蒸发量(mm)': -0.0016, '潜在蒸发量(mm)': -0.0004, '经向风速(V,m/s)': 0.67, '纬向风速(U,m/s)': -0.35, '总太阳辐射度(down,J/m2)': 121600.0, '净太阳辐射度(net,J/m2)': 108544.0, '直接辐射(J/m2)': 44352.0}
  行3: {'参考地名': '攀枝花', '经度(lon)': 101.75, '纬度(lat)': 26.5, '世界时(UTC)': '2023-01-01 02:00:00', '北京时(UTC+8)': '2023-01-01 10:00:00', '海平面气压(hPa)': 1021.77, '地面气压(hPa)': 835.52, '气温2m(℃)': 10.32, '降水量(mm)': 0.113, '相对湿度(%)': '87.16', '地表温度(℃)': 9.58, '蒸发量(mm)': -0.0613, '潜在蒸发量(mm)': -0.0378, '经向风速(V,m/s)': 0.73, '纬向风速(U,m/s)': -0.17, '总太阳辐射度(down,J/m2)': 654400.0, '净太阳辐射度(net,J/m2)': 576896.0, '直接辐射(J/m2)': 425280.0}

--------------------------------------------------------------------------------

文件名: 成华24.xls
文件大小: 2.36 MB
解析区域: 成华
解析年份: 2024
读取状态: 成功
数据形状: (8784, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2024-01-01 00:00:00': 1, '2024-09-01 03:00:00': 1, '2024-08-31 21:00:00': 1, '2024-08-31 22:00:00': 1, '2024-08-31 23:00:00': 1, '2024-09-01 00:00:00': 1, '2024-09-01 01:00:00': 1, '2024-09-01 02:00:00': 1, '2024-09-01 04:00:00': 1, '2024-08-31 02:00:00': 1}, '北京时(UTC+8)': {'2024-01-01 08:00:00': 1, '2024-09-01 11:00:00': 1, '2024-09-01 05:00:00': 1, '2024-09-01 06:00:00': 1, '2024-09-01 07:00:00': 1, '2024-09-01 08:00:00': 1, '2024-09-01 09:00:00': 1, '2024-09-01 10:00:00': 1, '2024-09-01 12:00:00': 1, '2024-08-31 10:00:00': 1}, '备注': {'成华区': 8784}}
样本数据:
  行1: {'经度(lon)': 104.1, '纬度(lat)': 30.7, '地面气压(hPa)': 968.9, '气温(℃)': 6.77, '降水量(mm)': 0.001, '露点温度(℃)': 6.26, '经向风速(V,m/s)': -0.34, '纬向风速(U,m/s)': 0.94, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2024-01-01 00:00:00', '北京时(UTC+8)': '2024-01-01 08:00:00', '备注': '成华区'}
  行2: {'经度(lon)': 104.1, '纬度(lat)': 30.7, '地面气压(hPa)': 969.58, '气温(℃)': 7.27, '降水量(mm)': 0.003, '露点温度(℃)': 6.68, '经向风速(V,m/s)': -0.14, '纬向风速(U,m/s)': 0.87, '太阳辐射净强度(net,J/m2)': 63250.75, '太阳辐射总强度(down,J/m2)': 73283.75, '世界时(UTC)': '2024-01-01 01:00:00', '北京时(UTC+8)': '2024-01-01 09:00:00', '备注': '成华区'}
  行3: {'经度(lon)': 104.1, '纬度(lat)': 30.7, '地面气压(hPa)': 970.29, '气温(℃)': 8.89, '降水量(mm)': 0.006, '露点温度(℃)': 6.98, '经向风速(V,m/s)': -0.04, '纬向风速(U,m/s)': 0.85, '太阳辐射净强度(net,J/m2)': 192269.77, '太阳辐射总强度(down,J/m2)': 222767.23, '世界时(UTC)': '2024-01-01 02:00:00', '北京时(UTC+8)': '2024-01-01 10:00:00', '备注': '成华区'}

--------------------------------------------------------------------------------

文件名: 蒲江25.xls
文件大小: 2.38 MB
解析区域: 蒲江
解析年份: 2025
读取状态: 成功
数据形状: (3648, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('float64'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'蒲江县': 3648}, '世界时(UTC)': {'2025-01-01 00:00:00': 1, '2025-04-11 23:00:00': 1, '2025-04-12 01:00:00': 1, '2025-04-12 02:00:00': 1, '2025-04-12 03:00:00': 1, '2025-04-12 04:00:00': 1, '2025-04-12 05:00:00': 1, '2025-04-12 06:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 08:00:00': 1}, '北京时(UTC+8)': {'2025-01-01 08:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 09:00:00': 1, '2025-04-12 10:00:00': 1, '2025-04-12 11:00:00': 1, '2025-04-12 12:00:00': 1, '2025-04-12 13:00:00': 1, '2025-04-12 14:00:00': 1, '2025-04-12 15:00:00': 1, '2025-04-12 16:00:00': 1}, '雪层温度(℃)': {'*': 3648}, '云底高度(m)': {'*': 220, '30.31': 3, '30.45': 3, '145.63': 3, '30.29': 3, '30.46': 3, '1014.06': 2, '1234.82': 2, '225.35': 2, '860.58': 2}}
样本数据:
  行1: {'参考地名': '蒲江县', '经度(lon)': 103.5, '纬度(lat)': 30.25, '世界时(UTC)': '2025-01-01 00:00:00', '北京时(UTC+8)': '2025-01-01 08:00:00', '海平面气压(hPa)': 1022.35, '地面气压(hPa)': 956.38, '气温2m(℃)': 1.99, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 0.59, '露点温度(℃)': 0.78, '相对湿度(%)': 91.69, '蒸发量(mm)': 0.0007, '潜在蒸发量(mm)': 0.0011, '经向风速(V,m/s)': -0.12, '纬向风速(U,m/s)': 0.81, '最大阵风(上一小时内，m/s)': 2.14, '云底高度(m)': '1141.87', '低层云量(lcc)': 0.28, '中层云量(mcc)': 0.29, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.34, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.0002, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0002, '雷暴概率(TT，K)': 34.0, 'K指数(K)': 6.93, '对流可用位能(J/kg)': 0.0}
  行2: {'参考地名': '蒲江县', '经度(lon)': 103.5, '纬度(lat)': 30.25, '世界时(UTC)': '2025-01-01 01:00:00', '北京时(UTC+8)': '2025-01-01 09:00:00', '海平面气压(hPa)': 1023.33, '地面气压(hPa)': 957.26, '气温2m(℃)': 2.5, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 4.48, '露点温度(℃)': 0.89, '相对湿度(%)': 89.12, '蒸发量(mm)': -0.0037, '潜在蒸发量(mm)': -0.005, '经向风速(V,m/s)': 0.1, '纬向风速(U,m/s)': 0.9, '最大阵风(上一小时内，m/s)': 2.89, '云底高度(m)': '1043.7', '低层云量(lcc)': 0.28, '中层云量(mcc)': 0.28, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.3, '总太阳辐射度(down,J/m2)': 158080.0, '净太阳辐射度(net,J/m2)': 139008.0, '直接辐射(J/m2)': 79616.0, '紫外强度(J/m2)': 17232.0, '径流(mm)': 0.0, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0002, '雷暴概率(TT，K)': 34.32, 'K指数(K)': 6.83, '对流可用位能(J/kg)': 0.0}
  行3: {'参考地名': '蒲江县', '经度(lon)': 103.5, '纬度(lat)': 30.25, '世界时(UTC)': '2025-01-01 02:00:00', '北京时(UTC+8)': '2025-01-01 10:00:00', '海平面气压(hPa)': 1024.15, '地面气压(hPa)': 958.02, '气温2m(℃)': 7.89, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 9.42, '露点温度(℃)': 2.75, '相对湿度(%)': 69.95, '蒸发量(mm)': -0.0347, '潜在蒸发量(mm)': -0.0555, '经向风速(V,m/s)': -0.54, '纬向风速(U,m/s)': 0.75, '最大阵风(上一小时内，m/s)': 3.83, '云底高度(m)': '1044.22', '低层云量(lcc)': 0.3, '中层云量(mcc)': 0.32, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.35, '总太阳辐射度(down,J/m2)': 644672.0, '净太阳辐射度(net,J/m2)': 571392.0, '直接辐射(J/m2)': 396096.0, '紫外强度(J/m2)': 69216.0, '径流(mm)': 0.0002, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0002, '雷暴概率(TT，K)': 34.66, 'K指数(K)': 7.17, '对流可用位能(J/kg)': 0.0}

--------------------------------------------------------------------------------

文件名: 蒲江24.xls
文件大小: 5.52 MB
解析区域: 蒲江
解析年份: 2024
读取状态: 成功
数据形状: (8808, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('float64'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'蒲江县': 8808}, '世界时(UTC)': {'2024-09-01 12:00:00': 2, '2024-09-01 02:00:00': 2, '2024-09-01 22:00:00': 2, '2024-09-01 21:00:00': 2, '2024-09-01 20:00:00': 2, '2024-09-01 19:00:00': 2, '2024-09-01 18:00:00': 2, '2024-09-01 17:00:00': 2, '2024-09-01 16:00:00': 2, '2024-09-01 15:00:00': 2}, '北京时(UTC+8)': {'2024-09-01 20:00:00': 2, '2024-09-01 10:00:00': 2, '2024-09-02 06:00:00': 2, '2024-09-02 05:00:00': 2, '2024-09-02 04:00:00': 2, '2024-09-02 03:00:00': 2, '2024-09-02 02:00:00': 2, '2024-09-02 01:00:00': 2, '2024-09-02 00:00:00': 2, '2024-09-01 23:00:00': 2}, '雪层温度(℃)': {'*': 8698, '0.01': 19, '-0.04': 4, '0.0': 4, '-0.02': 4, '-0.05': 4, '-0.07': 3, '-0.26': 3, '-0.32': 3, '-0.24': 3}, '云底高度(m)': {'*': 278, '30.4': 3, '66.71': 2, '870.8': 2, '711.64': 2, '54.18': 2, '1243.89': 2, '1179.31': 2, '1049.86': 2, '1063.3': 2}}
样本数据:
  行1: {'参考地名': '蒲江县', '经度(lon)': 103.5, '纬度(lat)': 30.25, '世界时(UTC)': '2024-01-01 00:00:00', '北京时(UTC+8)': '2024-01-01 08:00:00', '海平面气压(hPa)': 1023.46, '地面气压(hPa)': 957.88, '气温2m(℃)': 7.65, '降水量(mm)': 0.017, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 7.31, '露点温度(℃)': 6.87, '相对湿度(%)': 94.78, '蒸发量(mm)': -0.0025, '潜在蒸发量(mm)': -0.0026, '经向风速(V,m/s)': -0.24, '纬向风速(U,m/s)': 0.93, '最大阵风(上一小时内，m/s)': 1.79, '云底高度(m)': '96.97', '低层云量(lcc)': 0.87, '中层云量(mcc)': 0.94, '高层云量(hcc)': 0.63, '总云量(tcc)': 0.98, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.0005, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0004, '雷暴概率(TT，K)': 34.98, 'K指数(K)': 18.05, '对流可用位能(J/kg)': 0.0}
  行2: {'参考地名': '蒲江县', '经度(lon)': 103.5, '纬度(lat)': 30.25, '世界时(UTC)': '2024-01-01 01:00:00', '北京时(UTC+8)': '2024-01-01 09:00:00', '海平面气压(hPa)': 1023.95, '地面气压(hPa)': 958.37, '气温2m(℃)': 7.46, '降水量(mm)': 0.026, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 8.83, '露点温度(℃)': 6.71, '相对湿度(%)': 95.0, '蒸发量(mm)': -0.0037, '潜在蒸发量(mm)': -0.0044, '经向风速(V,m/s)': 0.12, '纬向风速(U,m/s)': 1.36, '最大阵风(上一小时内，m/s)': 2.45, '云底高度(m)': '96.7', '低层云量(lcc)': 0.82, '中层云量(mcc)': 0.97, '高层云量(hcc)': 0.57, '总云量(tcc)': 0.98, '总太阳辐射度(down,J/m2)': 68288.0, '净太阳辐射度(net,J/m2)': 59712.0, '直接辐射(J/m2)': 19008.0, '紫外强度(J/m2)': 4032.0, '径流(mm)': 0.0005, '地表径流(mm)': 0.0002, '地下径流(mm)': 0.0004, '雷暴概率(TT，K)': 35.11, 'K指数(K)': 18.36, '对流可用位能(J/kg)': 0.12}
  行3: {'参考地名': '蒲江县', '经度(lon)': 103.5, '纬度(lat)': 30.25, '世界时(UTC)': '2024-01-01 02:00:00', '北京时(UTC+8)': '2024-01-01 10:00:00', '海平面气压(hPa)': 1024.27, '地面气压(hPa)': 958.69, '气温2m(℃)': 9.55, '降水量(mm)': 0.053, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 9.99, '露点温度(℃)': 7.08, '相对湿度(%)': 84.56, '蒸发量(mm)': -0.0264, '潜在蒸发量(mm)': -0.0338, '经向风速(V,m/s)': 0.43, '纬向风速(U,m/s)': 1.45, '最大阵风(上一小时内，m/s)': 3.21, '云底高度(m)': '187.47', '低层云量(lcc)': 0.91, '中层云量(mcc)': 1.0, '高层云量(hcc)': 0.66, '总云量(tcc)': 1.0, '总太阳辐射度(down,J/m2)': 303488.0, '净太阳辐射度(net,J/m2)': 271808.0, '直接辐射(J/m2)': 135552.0, '紫外强度(J/m2)': 35088.0, '径流(mm)': 0.0007, '地表径流(mm)': 0.0002, '地下径流(mm)': 0.0004, '雷暴概率(TT，K)': 35.28, 'K指数(K)': 18.37, '对流可用位能(J/kg)': 1.38}

--------------------------------------------------------------------------------

文件名: 简阳23.xls
文件大小: 5.49 MB
解析区域: 简阳
解析年份: 2023
读取状态: 成功
数据形状: (8760, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('O'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'简阳市': 8760}, '世界时(UTC)': {'2023-01-01 00:00:00': 1, '2023-09-01 10:00:00': 1, '2023-09-01 04:00:00': 1, '2023-09-01 05:00:00': 1, '2023-09-01 06:00:00': 1, '2023-09-01 07:00:00': 1, '2023-09-01 08:00:00': 1, '2023-09-01 09:00:00': 1, '2023-09-01 11:00:00': 1, '2023-09-03 05:00:00': 1}, '北京时(UTC+8)': {'2023-01-01 08:00:00': 1, '2023-09-01 18:00:00': 1, '2023-09-01 12:00:00': 1, '2023-09-01 13:00:00': 1, '2023-09-01 14:00:00': 1, '2023-09-01 15:00:00': 1, '2023-09-01 16:00:00': 1, '2023-09-01 17:00:00': 1, '2023-09-01 19:00:00': 1, '2023-09-03 13:00:00': 1}, '雪层温度(℃)': {'*': 8759, '-0.27': 1}, '相对湿度(%)': {'*': 12, '68.56': 7, '85.58': 7, '70.02': 7, '61.8': 7, '85.84': 6, '73.46': 6, '74.07': 6, '59.4': 6, '98.08': 6}}
样本数据:
  行1: {'参考地名': '简阳市', '经度(lon)': 104.5, '纬度(lat)': 30.5, '世界时(UTC)': '2023-01-01 00:00:00', '北京时(UTC+8)': '2023-01-01 08:00:00', '海平面气压(hPa)': 1030.09, '地面气压(hPa)': 973.62, '气温2m(℃)': 4.57, '降水量(mm)': 0.002, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 4.36, '露点温度(℃)': 4.53, '相对湿度(%)': '99.69', '蒸发量(mm)': 0.0008, '潜在蒸发量(mm)': 0.0003, '经向风速(V,m/s)': -0.87, '纬向风速(U,m/s)': 0.35, '最大阵风(上一小时内，m/s)': 2.55, '云底高度(m)': '29.92', '低层云量(lcc)': 0.53, '中层云量(mcc)': 0.51, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.69, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.0014, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0014, '雷暴概率(TT，K)': 25.88, 'K指数(K)': 6.38, '对流可用位能(J/kg)': 4.19}
  行2: {'参考地名': '简阳市', '经度(lon)': 104.5, '纬度(lat)': 30.5, '世界时(UTC)': '2023-01-01 01:00:00', '北京时(UTC+8)': '2023-01-01 09:00:00', '海平面气压(hPa)': 1030.78, '地面气压(hPa)': 974.24, '气温2m(℃)': 4.86, '降水量(mm)': 0.001, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 5.79, '露点温度(℃)': 4.85, '相对湿度(%)': '99.98', '蒸发量(mm)': -0.0091, '潜在蒸发量(mm)': -0.004, '经向风速(V,m/s)': -0.95, '纬向风速(U,m/s)': -0.21, '最大阵风(上一小时内，m/s)': 3.16, '云底高度(m)': '29.96', '低层云量(lcc)': 0.66, '中层云量(mcc)': 0.52, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.75, '总太阳辐射度(down,J/m2)': 91520.0, '净太阳辐射度(net,J/m2)': 79360.0, '直接辐射(J/m2)': 14272.0, '紫外强度(J/m2)': 10904.0, '径流(mm)': 0.0014, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0014, '雷暴概率(TT，K)': 25.94, 'K指数(K)': 6.63, '对流可用位能(J/kg)': 3.81}
  行3: {'参考地名': '简阳市', '经度(lon)': 104.5, '纬度(lat)': 30.5, '世界时(UTC)': '2023-01-01 02:00:00', '北京时(UTC+8)': '2023-01-01 10:00:00', '海平面气压(hPa)': 1031.02, '地面气压(hPa)': 974.39, '气温2m(℃)': 6.11, '降水量(mm)': 0.001, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 8.31, '露点温度(℃)': 6.02, '相对湿度(%)': '99.39', '蒸发量(mm)': -0.0305, '潜在蒸发量(mm)': -0.0195, '经向风速(V,m/s)': -0.83, '纬向风速(U,m/s)': 0.07, '最大阵风(上一小时内，m/s)': 3.57, '云底高度(m)': '41.79', '低层云量(lcc)': 0.66, '中层云量(mcc)': 0.43, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.71, '总太阳辐射度(down,J/m2)': 319040.0, '净太阳辐射度(net,J/m2)': 276416.0, '直接辐射(J/m2)': 43968.0, '紫外强度(J/m2)': 35784.0, '径流(mm)': 0.0014, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0014, '雷暴概率(TT，K)': 26.02, 'K指数(K)': 6.78, '对流可用位能(J/kg)': 3.25}

--------------------------------------------------------------------------------

文件名: 成华25.xls
文件大小: 1.02 MB
解析区域: 成华
解析年份: 2025
读取状态: 成功
数据形状: (3648, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2025-01-01 00:00:00': 1, '2025-04-11 23:00:00': 1, '2025-04-12 01:00:00': 1, '2025-04-12 02:00:00': 1, '2025-04-12 03:00:00': 1, '2025-04-12 04:00:00': 1, '2025-04-12 05:00:00': 1, '2025-04-12 06:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 08:00:00': 1}, '北京时(UTC+8)': {'2025-01-01 08:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 09:00:00': 1, '2025-04-12 10:00:00': 1, '2025-04-12 11:00:00': 1, '2025-04-12 12:00:00': 1, '2025-04-12 13:00:00': 1, '2025-04-12 14:00:00': 1, '2025-04-12 15:00:00': 1, '2025-04-12 16:00:00': 1}, '备注': {'成华区': 3648}}
样本数据:
  行1: {'经度(lon)': 104.1, '纬度(lat)': 30.7, '地面气压(hPa)': 967.47, '气温(℃)': 1.92, '降水量(mm)': 0.0, '露点温度(℃)': 0.94, '经向风速(V,m/s)': -1.88, '纬向风速(U,m/s)': -0.81, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2025-01-01 00:00:00', '北京时(UTC+8)': '2025-01-01 08:00:00', '备注': '成华区'}
  行2: {'经度(lon)': 104.1, '纬度(lat)': 30.7, '地面气压(hPa)': 968.41, '气温(℃)': 3.43, '降水量(mm)': 0.0, '露点温度(℃)': 2.05, '经向风速(V,m/s)': -1.79, '纬向风速(U,m/s)': -0.72, '太阳辐射净强度(net,J/m2)': 124710.0, '太阳辐射总强度(down,J/m2)': 144488.75, '世界时(UTC)': '2025-01-01 01:00:00', '北京时(UTC+8)': '2025-01-01 09:00:00', '备注': '成华区'}
  行3: {'经度(lon)': 104.1, '纬度(lat)': 30.7, '地面气压(hPa)': 969.15, '气温(℃)': 6.61, '降水量(mm)': 0.0, '露点温度(℃)': 3.13, '经向风速(V,m/s)': -1.95, '纬向风速(U,m/s)': -0.79, '太阳辐射净强度(net,J/m2)': 461357.0, '太阳辐射总强度(down,J/m2)': 534528.75, '世界时(UTC)': '2025-01-01 02:00:00', '北京时(UTC+8)': '2025-01-01 10:00:00', '备注': '成华区'}

--------------------------------------------------------------------------------

文件名: 新都25.xls
文件大小: 1.02 MB
解析区域: 新都
解析年份: 2025
读取状态: 成功
数据形状: (3648, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2025-01-01 00:00:00': 1, '2025-04-11 23:00:00': 1, '2025-04-12 01:00:00': 1, '2025-04-12 02:00:00': 1, '2025-04-12 03:00:00': 1, '2025-04-12 04:00:00': 1, '2025-04-12 05:00:00': 1, '2025-04-12 06:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 08:00:00': 1}, '北京时(UTC+8)': {'2025-01-01 08:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 09:00:00': 1, '2025-04-12 10:00:00': 1, '2025-04-12 11:00:00': 1, '2025-04-12 12:00:00': 1, '2025-04-12 13:00:00': 1, '2025-04-12 14:00:00': 1, '2025-04-12 15:00:00': 1, '2025-04-12 16:00:00': 1}, '备注': {'新都区': 3648}}
样本数据:
  行1: {'经度(lon)': 104.2, '纬度(lat)': 30.8, '地面气压(hPa)': 963.19, '气温(℃)': 2.04, '降水量(mm)': 0.0, '露点温度(℃)': 1.03, '经向风速(V,m/s)': -1.84, '纬向风速(U,m/s)': -0.89, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2025-01-01 00:00:00', '北京时(UTC+8)': '2025-01-01 08:00:00', '备注': '新都区'}
  行2: {'经度(lon)': 104.2, '纬度(lat)': 30.8, '地面气压(hPa)': 964.14, '气温(℃)': 3.56, '降水量(mm)': 0.0, '露点温度(℃)': 2.07, '经向风速(V,m/s)': -1.73, '纬向风速(U,m/s)': -0.73, '太阳辐射净强度(net,J/m2)': 121287.5, '太阳辐射总强度(down,J/m2)': 140292.0, '世界时(UTC)': '2025-01-01 01:00:00', '北京时(UTC+8)': '2025-01-01 09:00:00', '备注': '新都区'}
  行3: {'经度(lon)': 104.2, '纬度(lat)': 30.8, '地面气压(hPa)': 964.88, '气温(℃)': 6.36, '降水量(mm)': 0.0, '露点温度(℃)': 2.98, '经向风速(V,m/s)': -1.94, '纬向风速(U,m/s)': -0.76, '太阳辐射净强度(net,J/m2)': 446447.53, '太阳辐射总强度(down,J/m2)': 516364.0, '世界时(UTC)': '2025-01-01 02:00:00', '北京时(UTC+8)': '2025-01-01 10:00:00', '备注': '新都区'}

--------------------------------------------------------------------------------

文件名: 攀枝花25.xls
文件大小: 1.39 MB
解析区域: 攀枝花
解析年份: 2025
读取状态: 成功
数据形状: (3648, 18)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '相对湿度(%)', '地表温度(℃)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '相对湿度(%)': dtype('O'), '地表温度(℃)': dtype('float64'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64')}
唯一值统计: {'参考地名': {'攀枝花': 3648}, '世界时(UTC)': {'2025-01-01 00:00:00': 1, '2025-04-11 23:00:00': 1, '2025-04-12 01:00:00': 1, '2025-04-12 02:00:00': 1, '2025-04-12 03:00:00': 1, '2025-04-12 04:00:00': 1, '2025-04-12 05:00:00': 1, '2025-04-12 06:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 08:00:00': 1}, '北京时(UTC+8)': {'2025-01-01 08:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 09:00:00': 1, '2025-04-12 10:00:00': 1, '2025-04-12 11:00:00': 1, '2025-04-12 12:00:00': 1, '2025-04-12 13:00:00': 1, '2025-04-12 14:00:00': 1, '2025-04-12 15:00:00': 1, '2025-04-12 16:00:00': 1}, '相对湿度(%)': {'39.46': 5, '48.12': 4, '62.99': 4, '57.5': 4, '49.23': 4, '38.91': 4, '36.51': 4, '*': 4, '56.44': 4, '37.82': 3}}
样本数据:
  行1: {'参考地名': '攀枝花', '经度(lon)': 101.75, '纬度(lat)': 26.5, '世界时(UTC)': '2025-01-01 00:00:00', '北京时(UTC+8)': '2025-01-01 08:00:00', '海平面气压(hPa)': 1016.77, '地面气压(hPa)': 831.92, '气温2m(℃)': 2.31, '降水量(mm)': 0.0, '相对湿度(%)': '86.84', '地表温度(℃)': 0.27, '蒸发量(mm)': 0.0, '潜在蒸发量(mm)': 0.0012, '经向风速(V,m/s)': 0.67, '纬向风速(U,m/s)': -0.61, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0}
  行2: {'参考地名': '攀枝花', '经度(lon)': 101.75, '纬度(lat)': 26.5, '世界时(UTC)': '2025-01-01 01:00:00', '北京时(UTC+8)': '2025-01-01 09:00:00', '海平面气压(hPa)': 1017.67, '地面气压(hPa)': 832.59, '气温2m(℃)': 3.71, '降水量(mm)': 0.0, '相对湿度(%)': '83.6', '地表温度(℃)': 5.16, '蒸发量(mm)': -0.0026, '潜在蒸发量(mm)': -0.0036, '经向风速(V,m/s)': 0.4, '纬向风速(U,m/s)': -0.42, '总太阳辐射度(down,J/m2)': 193280.0, '净太阳辐射度(net,J/m2)': 169920.0, '直接辐射(J/m2)': 104256.0}
  行3: {'参考地名': '攀枝花', '经度(lon)': 101.75, '纬度(lat)': 26.5, '世界时(UTC)': '2025-01-01 02:00:00', '北京时(UTC+8)': '2025-01-01 10:00:00', '海平面气压(hPa)': 1018.63, '地面气压(hPa)': 833.34, '气温2m(℃)': 9.91, '降水量(mm)': 0.0, '相对湿度(%)': '59.22', '地表温度(℃)': 11.27, '蒸发量(mm)': -0.0341, '潜在蒸发量(mm)': -0.0671, '经向风速(V,m/s)': 0.3, '纬向风速(U,m/s)': -0.25, '总太阳辐射度(down,J/m2)': 806144.0, '净太阳辐射度(net,J/m2)': 712256.0, '直接辐射(J/m2)': 547840.0}

--------------------------------------------------------------------------------

文件名: 攀枝花24.xls
文件大小: 3.15 MB
解析区域: 攀枝花
解析年份: 2024
读取状态: 成功
数据形状: (8808, 18)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '相对湿度(%)', '地表温度(℃)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '相对湿度(%)': dtype('O'), '地表温度(℃)': dtype('float64'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64')}
唯一值统计: {'参考地名': {'攀枝花': 8808}, '世界时(UTC)': {'2024-09-01 12:00:00': 2, '2024-09-01 02:00:00': 2, '2024-09-01 22:00:00': 2, '2024-09-01 21:00:00': 2, '2024-09-01 20:00:00': 2, '2024-09-01 19:00:00': 2, '2024-09-01 18:00:00': 2, '2024-09-01 17:00:00': 2, '2024-09-01 16:00:00': 2, '2024-09-01 15:00:00': 2}, '北京时(UTC+8)': {'2024-09-01 20:00:00': 2, '2024-09-01 10:00:00': 2, '2024-09-02 06:00:00': 2, '2024-09-02 05:00:00': 2, '2024-09-02 04:00:00': 2, '2024-09-02 03:00:00': 2, '2024-09-02 02:00:00': 2, '2024-09-02 01:00:00': 2, '2024-09-02 00:00:00': 2, '2024-09-01 23:00:00': 2}, '相对湿度(%)': {'77.4': 7, '57.79': 7, '94.56': 7, '90.64': 6, '76.18': 6, '86.53': 6, '74.5': 6, '79.71': 6, '82.94': 6, '71.06': 5}}
样本数据:
  行1: {'参考地名': '攀枝花', '经度(lon)': 101.75, '纬度(lat)': 26.5, '世界时(UTC)': '2024-01-01 00:00:00', '北京时(UTC+8)': '2024-01-01 08:00:00', '海平面气压(hPa)': 1017.16, '地面气压(hPa)': 832.32, '气温2m(℃)': 5.6, '降水量(mm)': 0.0, '相对湿度(%)': '66.56', '地表温度(℃)': 0.84, '蒸发量(mm)': -0.0005, '潜在蒸发量(mm)': 0.0006, '经向风速(V,m/s)': 1.14, '纬向风速(U,m/s)': -0.28, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0}
  行2: {'参考地名': '攀枝花', '经度(lon)': 101.75, '纬度(lat)': 26.5, '世界时(UTC)': '2024-01-01 01:00:00', '北京时(UTC+8)': '2024-01-01 09:00:00', '海平面气压(hPa)': 1017.65, '地面气压(hPa)': 832.62, '气温2m(℃)': 4.67, '降水量(mm)': 0.0, '相对湿度(%)': '70.58', '地表温度(℃)': 6.26, '蒸发量(mm)': -0.0019, '潜在蒸发量(mm)': -0.0019, '经向风速(V,m/s)': 0.86, '纬向风速(U,m/s)': -0.12, '总太阳辐射度(down,J/m2)': 205120.0, '净太阳辐射度(net,J/m2)': 180544.0, '直接辐射(J/m2)': 115520.0}
  行3: {'参考地名': '攀枝花', '经度(lon)': 101.75, '纬度(lat)': 26.5, '世界时(UTC)': '2024-01-01 02:00:00', '北京时(UTC+8)': '2024-01-01 10:00:00', '海平面气压(hPa)': 1018.4, '地面气压(hPa)': 833.21, '气温2m(℃)': 11.79, '降水量(mm)': 0.0, '相对湿度(%)': '48.65', '地表温度(℃)': 13.0, '蒸发量(mm)': -0.0233, '潜在蒸发量(mm)': -0.0813, '经向风速(V,m/s)': 0.71, '纬向风速(U,m/s)': 0.03, '总太阳辐射度(down,J/m2)': 870656.0, '净太阳辐射度(net,J/m2)': 769664.0, '直接辐射(J/m2)': 622464.0}

--------------------------------------------------------------------------------

文件名: 新都24.xls
文件大小: 2.36 MB
解析区域: 新都
解析年份: 2024
读取状态: 成功
数据形状: (8784, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2024-01-01 00:00:00': 1, '2024-09-01 03:00:00': 1, '2024-08-31 21:00:00': 1, '2024-08-31 22:00:00': 1, '2024-08-31 23:00:00': 1, '2024-09-01 00:00:00': 1, '2024-09-01 01:00:00': 1, '2024-09-01 02:00:00': 1, '2024-09-01 04:00:00': 1, '2024-08-31 02:00:00': 1}, '北京时(UTC+8)': {'2024-01-01 08:00:00': 1, '2024-09-01 11:00:00': 1, '2024-09-01 05:00:00': 1, '2024-09-01 06:00:00': 1, '2024-09-01 07:00:00': 1, '2024-09-01 08:00:00': 1, '2024-09-01 09:00:00': 1, '2024-09-01 10:00:00': 1, '2024-09-01 12:00:00': 1, '2024-08-31 10:00:00': 1}, '备注': {'新都区': 8784}}
样本数据:
  行1: {'经度(lon)': 104.2, '纬度(lat)': 30.8, '地面气压(hPa)': 964.63, '气温(℃)': 6.56, '降水量(mm)': 0.0, '露点温度(℃)': 6.09, '经向风速(V,m/s)': -0.01, '纬向风速(U,m/s)': 0.93, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2024-01-01 00:00:00', '北京时(UTC+8)': '2024-01-01 08:00:00', '备注': '新都区'}
  行2: {'经度(lon)': 104.2, '纬度(lat)': 30.8, '地面气压(hPa)': 965.33, '气温(℃)': 7.12, '降水量(mm)': 0.001, '露点温度(℃)': 6.47, '经向风速(V,m/s)': 0.11, '纬向风速(U,m/s)': 0.83, '太阳辐射净强度(net,J/m2)': 62206.5, '太阳辐射总强度(down,J/m2)': 71936.25, '世界时(UTC)': '2024-01-01 01:00:00', '北京时(UTC+8)': '2024-01-01 09:00:00', '备注': '新都区'}
  行3: {'经度(lon)': 104.2, '纬度(lat)': 30.8, '地面气压(hPa)': 966.06, '气温(℃)': 8.59, '降水量(mm)': 0.012, '露点温度(℃)': 6.8, '经向风速(V,m/s)': 0.11, '纬向风速(U,m/s)': 0.75, '太阳辐射净强度(net,J/m2)': 186026.0, '太阳辐射总强度(down,J/m2)': 215115.77, '世界时(UTC)': '2024-01-01 02:00:00', '北京时(UTC+8)': '2024-01-01 10:00:00', '备注': '新都区'}

--------------------------------------------------------------------------------

文件名: 简阳24.xls
文件大小: 5.52 MB
解析区域: 简阳
解析年份: 2024
读取状态: 成功
数据形状: (8808, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('O'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'简阳市': 8808}, '世界时(UTC)': {'2024-09-01 12:00:00': 2, '2024-09-01 02:00:00': 2, '2024-09-01 22:00:00': 2, '2024-09-01 21:00:00': 2, '2024-09-01 20:00:00': 2, '2024-09-01 19:00:00': 2, '2024-09-01 18:00:00': 2, '2024-09-01 17:00:00': 2, '2024-09-01 16:00:00': 2, '2024-09-01 15:00:00': 2}, '北京时(UTC+8)': {'2024-09-01 20:00:00': 2, '2024-09-01 10:00:00': 2, '2024-09-02 06:00:00': 2, '2024-09-02 05:00:00': 2, '2024-09-02 04:00:00': 2, '2024-09-02 03:00:00': 2, '2024-09-02 02:00:00': 2, '2024-09-02 01:00:00': 2, '2024-09-02 00:00:00': 2, '2024-09-01 23:00:00': 2}, '雪层温度(℃)': {'*': 8655, '0.01': 39, '-0.03': 4, '-0.01': 4, '-0.1': 4, '-0.06': 4, '-0.02': 4, '-0.18': 3, '-0.24': 3, '-0.05': 3}, '相对湿度(%)': {'*': 12, '94.13': 10, '73.13': 8, '85.69': 7, '82.59': 7, '82.11': 7, '65.84': 7, '82.61': 7, '93.42': 7, '92.97': 6}}
样本数据:
  行1: {'参考地名': '简阳市', '经度(lon)': 104.5, '纬度(lat)': 30.5, '世界时(UTC)': '2024-01-01 00:00:00', '北京时(UTC+8)': '2024-01-01 08:00:00', '海平面气压(hPa)': 1023.06, '地面气压(hPa)': 967.59, '气温2m(℃)': 7.51, '降水量(mm)': 0.001, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 7.73, '露点温度(℃)': 6.25, '相对湿度(%)': '91.71', '蒸发量(mm)': -0.0031, '潜在蒸发量(mm)': -0.0034, '经向风速(V,m/s)': -0.33, '纬向风速(U,m/s)': 1.09, '最大阵风(上一小时内，m/s)': 2.33, '云底高度(m)': '738.47', '低层云量(lcc)': 0.86, '中层云量(mcc)': 0.96, '高层云量(hcc)': 0.87, '总云量(tcc)': 0.99, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.0005, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0004, '雷暴概率(TT，K)': 37.24, 'K指数(K)': 20.04, '对流可用位能(J/kg)': 0.0}
  行2: {'参考地名': '简阳市', '经度(lon)': 104.5, '纬度(lat)': 30.5, '世界时(UTC)': '2024-01-01 01:00:00', '北京时(UTC+8)': '2024-01-01 09:00:00', '海平面气压(hPa)': 1023.46, '地面气压(hPa)': 967.97, '气温2m(℃)': 7.5, '降水量(mm)': 0.005, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 8.08, '露点温度(℃)': 6.19, '相对湿度(%)': '91.42', '蒸发量(mm)': -0.0053, '潜在蒸发量(mm)': -0.0073, '经向风速(V,m/s)': -0.73, '纬向风速(U,m/s)': 0.94, '最大阵风(上一小时内，m/s)': 2.84, '云底高度(m)': '759.2', '低层云量(lcc)': 0.86, '中层云量(mcc)': 0.94, '高层云量(hcc)': 0.9, '总云量(tcc)': 0.99, '总太阳辐射度(down,J/m2)': 55360.0, '净太阳辐射度(net,J/m2)': 46912.0, '直接辐射(J/m2)': 5888.0, '紫外强度(J/m2)': 8632.0, '径流(mm)': 0.0005, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0005, '雷暴概率(TT，K)': 37.31, 'K指数(K)': 20.4, '对流可用位能(J/kg)': 0.0}
  行3: {'参考地名': '简阳市', '经度(lon)': 104.5, '纬度(lat)': 30.5, '世界时(UTC)': '2024-01-01 02:00:00', '北京时(UTC+8)': '2024-01-01 10:00:00', '海平面气压(hPa)': 1023.85, '地面气压(hPa)': 968.31, '气温2m(℃)': 8.67, '降水量(mm)': 0.012, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 9.13, '露点温度(℃)': 6.6, '相对湿度(%)': '86.77', '蒸发量(mm)': -0.0163, '潜在蒸发量(mm)': -0.0222, '经向风速(V,m/s)': -0.63, '纬向风速(U,m/s)': 0.94, '最大阵风(上一小时内，m/s)': 3.05, '云底高度(m)': '1292.22', '低层云量(lcc)': 1.0, '中层云量(mcc)': 0.94, '高层云量(hcc)': 0.79, '总云量(tcc)': 1.0, '总太阳辐射度(down,J/m2)': 185024.0, '净太阳辐射度(net,J/m2)': 160704.0, '直接辐射(J/m2)': 51072.0, '紫外强度(J/m2)': 13960.0, '径流(mm)': 0.0005, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0005, '雷暴概率(TT，K)': 37.45, 'K指数(K)': 20.77, '对流可用位能(J/kg)': 0.12}

--------------------------------------------------------------------------------

文件名: 蒲江23.xls
文件大小: 5.49 MB
解析区域: 蒲江
解析年份: 2023
读取状态: 成功
数据形状: (8760, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('float64'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'蒲江县': 8760}, '世界时(UTC)': {'2023-01-01 00:00:00': 1, '2023-09-01 10:00:00': 1, '2023-09-01 04:00:00': 1, '2023-09-01 05:00:00': 1, '2023-09-01 06:00:00': 1, '2023-09-01 07:00:00': 1, '2023-09-01 08:00:00': 1, '2023-09-01 09:00:00': 1, '2023-09-01 11:00:00': 1, '2023-09-03 05:00:00': 1}, '北京时(UTC+8)': {'2023-01-01 08:00:00': 1, '2023-09-01 18:00:00': 1, '2023-09-01 12:00:00': 1, '2023-09-01 13:00:00': 1, '2023-09-01 14:00:00': 1, '2023-09-01 15:00:00': 1, '2023-09-01 16:00:00': 1, '2023-09-01 17:00:00': 1, '2023-09-01 19:00:00': 1, '2023-09-03 13:00:00': 1}, '雪层温度(℃)': {'*': 8758, '-0.44': 1, '-0.37': 1}, '云底高度(m)': {'*': 256, '30.53': 4, '30.36': 4, '30.52': 3, '1519.58': 3, '30.44': 3, '598.29': 3, '30.47': 3, '877.54': 2, '31.5': 2}}
样本数据:
  行1: {'参考地名': '蒲江县', '经度(lon)': 103.5, '纬度(lat)': 30.25, '世界时(UTC)': '2023-01-01 00:00:00', '北京时(UTC+8)': '2023-01-01 08:00:00', '海平面气压(hPa)': 1029.69, '地面气压(hPa)': 963.02, '气温2m(℃)': 5.14, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 3.78, '露点温度(℃)': 5.07, '相对湿度(%)': 99.52, '蒸发量(mm)': -0.0002, '潜在蒸发量(mm)': -0.0001, '经向风速(V,m/s)': -0.18, '纬向风速(U,m/s)': 0.59, '最大阵风(上一小时内，m/s)': 1.58, '云底高度(m)': '32.92', '低层云量(lcc)': 0.85, '中层云量(mcc)': 0.18, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.86, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.0014, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0014, '雷暴概率(TT，K)': 23.84, 'K指数(K)': 3.72, '对流可用位能(J/kg)': 0.0}
  行2: {'参考地名': '蒲江县', '经度(lon)': 103.5, '纬度(lat)': 30.25, '世界时(UTC)': '2023-01-01 01:00:00', '北京时(UTC+8)': '2023-01-01 09:00:00', '海平面气压(hPa)': 1030.79, '地面气压(hPa)': 963.99, '气温2m(℃)': 4.76, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 5.67, '露点温度(℃)': 4.72, '相对湿度(%)': 99.74, '蒸发量(mm)': -0.0023, '潜在蒸发量(mm)': -0.0013, '经向风速(V,m/s)': 0.14, '纬向风速(U,m/s)': 0.88, '最大阵风(上一小时内，m/s)': 2.19, '云底高度(m)': '36.46', '低层云量(lcc)': 0.88, '中层云量(mcc)': 0.07, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.89, '总太阳辐射度(down,J/m2)': 79168.0, '净太阳辐射度(net,J/m2)': 69952.0, '直接辐射(J/m2)': 20608.0, '紫外强度(J/m2)': 13736.0, '径流(mm)': 0.0014, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0014, '雷暴概率(TT，K)': 23.5, 'K指数(K)': 3.4, '对流可用位能(J/kg)': 0.19}
  行3: {'参考地名': '蒲江县', '经度(lon)': 103.5, '纬度(lat)': 30.25, '世界时(UTC)': '2023-01-01 02:00:00', '北京时(UTC+8)': '2023-01-01 10:00:00', '海平面气压(hPa)': 1030.99, '地面气压(hPa)': 964.24, '气温2m(℃)': 5.67, '降水量(mm)': 0.001, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 7.42, '露点温度(℃)': 5.27, '相对湿度(%)': 97.26, '蒸发量(mm)': -0.0319, '潜在蒸发量(mm)': -0.0229, '经向风速(V,m/s)': 0.38, '纬向风速(U,m/s)': 1.17, '最大阵风(上一小时内，m/s)': 2.98, '云底高度(m)': '40.04', '低层云量(lcc)': 0.73, '中层云量(mcc)': 0.09, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.76, '总太阳辐射度(down,J/m2)': 369280.0, '净太阳辐射度(net,J/m2)': 329088.0, '直接辐射(J/m2)': 119296.0, '紫外强度(J/m2)': 48328.0, '径流(mm)': 0.0012, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0014, '雷暴概率(TT，K)': 23.21, 'K指数(K)': 3.06, '对流可用位能(J/kg)': 1.12}

--------------------------------------------------------------------------------

文件名: 简阳25.xls
文件大小: 2.39 MB
解析区域: 简阳
解析年份: 2025
读取状态: 成功
数据形状: (3648, 36)
列名: ['参考地名', '经度(lon)', '纬度(lat)', '世界时(UTC)', '北京时(UTC+8)', '海平面气压(hPa)', '地面气压(hPa)', '气温2m(℃)', '降水量(mm)', '降雪量(mm)', '积雪深度(mm of water equivalent)', '积雪密度(kg/m3)', '雪层温度(℃)', '地表温度(℃)', '露点温度(℃)', '相对湿度(%)', '蒸发量(mm)', '潜在蒸发量(mm)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '最大阵风(上一小时内，m/s)', '云底高度(m)', '低层云量(lcc)', '中层云量(mcc)', '高层云量(hcc)', '总云量(tcc)', '总太阳辐射度(down,J/m2)', '净太阳辐射度(net,J/m2)', '直接辐射(J/m2)', '紫外强度(J/m2)', '径流(mm)', '地表径流(mm)', '地下径流(mm)', '雷暴概率(TT，K)', 'K指数(K)', '对流可用位能(J/kg)']
数据类型: {'参考地名': dtype('O'), '经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '海平面气压(hPa)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温2m(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '降雪量(mm)': dtype('float64'), '积雪深度(mm of water equivalent)': dtype('float64'), '积雪密度(kg/m3)': dtype('float64'), '雪层温度(℃)': dtype('O'), '地表温度(℃)': dtype('float64'), '露点温度(℃)': dtype('float64'), '相对湿度(%)': dtype('O'), '蒸发量(mm)': dtype('float64'), '潜在蒸发量(mm)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '最大阵风(上一小时内，m/s)': dtype('float64'), '云底高度(m)': dtype('O'), '低层云量(lcc)': dtype('float64'), '中层云量(mcc)': dtype('float64'), '高层云量(hcc)': dtype('float64'), '总云量(tcc)': dtype('float64'), '总太阳辐射度(down,J/m2)': dtype('float64'), '净太阳辐射度(net,J/m2)': dtype('float64'), '直接辐射(J/m2)': dtype('float64'), '紫外强度(J/m2)': dtype('float64'), '径流(mm)': dtype('float64'), '地表径流(mm)': dtype('float64'), '地下径流(mm)': dtype('float64'), '雷暴概率(TT，K)': dtype('float64'), 'K指数(K)': dtype('float64'), '对流可用位能(J/kg)': dtype('float64')}
唯一值统计: {'参考地名': {'简阳市': 3648}, '世界时(UTC)': {'2025-01-01 00:00:00': 1, '2025-04-11 23:00:00': 1, '2025-04-12 01:00:00': 1, '2025-04-12 02:00:00': 1, '2025-04-12 03:00:00': 1, '2025-04-12 04:00:00': 1, '2025-04-12 05:00:00': 1, '2025-04-12 06:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 08:00:00': 1}, '北京时(UTC+8)': {'2025-01-01 08:00:00': 1, '2025-04-12 07:00:00': 1, '2025-04-12 09:00:00': 1, '2025-04-12 10:00:00': 1, '2025-04-12 11:00:00': 1, '2025-04-12 12:00:00': 1, '2025-04-12 13:00:00': 1, '2025-04-12 14:00:00': 1, '2025-04-12 15:00:00': 1, '2025-04-12 16:00:00': 1}, '雪层温度(℃)': {'*': 3633, '0.01': 15}, '相对湿度(%)': {'70.64': 5, '57.76': 5, '55.87': 5, '69.92': 4, '93.77': 4, '70.59': 4, '58.61': 4, '54.25': 4, '94.2': 4, '79.01': 4}}
样本数据:
  行1: {'参考地名': '简阳市', '经度(lon)': 104.5, '纬度(lat)': 30.5, '世界时(UTC)': '2025-01-01 00:00:00', '北京时(UTC+8)': '2025-01-01 08:00:00', '海平面气压(hPa)': 1022.1, '地面气压(hPa)': 966.29, '气温2m(℃)': 2.52, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 1.52, '露点温度(℃)': 1.96, '相对湿度(%)': '96.08', '蒸发量(mm)': 0.0002, '潜在蒸发量(mm)': 0.0014, '经向风速(V,m/s)': -1.85, '纬向风速(U,m/s)': -0.27, '最大阵风(上一小时内，m/s)': 3.39, '云底高度(m)': '1519.62', '低层云量(lcc)': 0.21, '中层云量(mcc)': 0.02, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.21, '总太阳辐射度(down,J/m2)': 0.0, '净太阳辐射度(net,J/m2)': 0.0, '直接辐射(J/m2)': 0.0, '紫外强度(J/m2)': 0.0, '径流(mm)': 0.0002, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0002, '雷暴概率(TT，K)': 31.75, 'K指数(K)': 6.18, '对流可用位能(J/kg)': 0.0}
  行2: {'参考地名': '简阳市', '经度(lon)': 104.5, '纬度(lat)': 30.5, '世界时(UTC)': '2025-01-01 01:00:00', '北京时(UTC+8)': '2025-01-01 09:00:00', '海平面气压(hPa)': 1022.88, '地面气压(hPa)': 966.99, '气温2m(℃)': 3.13, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 5.0, '露点温度(℃)': 1.75, '相对湿度(%)': '90.63', '蒸发量(mm)': -0.0066, '潜在蒸发量(mm)': -0.0098, '经向风速(V,m/s)': -2.18, '纬向风速(U,m/s)': -0.85, '最大阵风(上一小时内，m/s)': 4.89, '云底高度(m)': '1613.45', '低层云量(lcc)': 0.14, '中层云量(mcc)': 0.02, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.15, '总太阳辐射度(down,J/m2)': 167552.0, '净太阳辐射度(net,J/m2)': 144256.0, '直接辐射(J/m2)': 81536.0, '紫外强度(J/m2)': 16224.0, '径流(mm)': 0.0002, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0002, '雷暴概率(TT，K)': 32.29, 'K指数(K)': 6.69, '对流可用位能(J/kg)': 0.0}
  行3: {'参考地名': '简阳市', '经度(lon)': 104.5, '纬度(lat)': 30.5, '世界时(UTC)': '2025-01-01 02:00:00', '北京时(UTC+8)': '2025-01-01 10:00:00', '海平面气压(hPa)': 1023.68, '地面气压(hPa)': 967.65, '气温2m(℃)': 8.27, '降水量(mm)': 0.0, '降雪量(mm)': 0.0, '积雪深度(mm of water equivalent)': 0.0, '积雪密度(kg/m3)': 0.0, '雪层温度(℃)': '*', '地表温度(℃)': 9.21, '露点温度(℃)': 3.28, '相对湿度(%)': '70.75', '蒸发量(mm)': -0.0312, '潜在蒸发量(mm)': -0.0578, '经向风速(V,m/s)': -1.85, '纬向风速(U,m/s)': -0.83, '最大阵风(上一小时内，m/s)': 5.47, '云底高度(m)': '1713.47', '低层云量(lcc)': 0.24, '中层云量(mcc)': 0.02, '高层云量(hcc)': 0.0, '总云量(tcc)': 0.25, '总太阳辐射度(down,J/m2)': 619072.0, '净太阳辐射度(net,J/m2)': 535552.0, '直接辐射(J/m2)': 312000.0, '紫外强度(J/m2)': 64296.0, '径流(mm)': 0.0002, '地表径流(mm)': 0.0, '地下径流(mm)': 0.0002, '雷暴概率(TT，K)': 33.05, 'K指数(K)': 7.48, '对流可用位能(J/kg)': 0.0}

--------------------------------------------------------------------------------

文件名: 成华23.xls
文件大小: 2.36 MB
解析区域: 成华
解析年份: 2023
读取状态: 成功
数据形状: (8760, 13)
列名: ['经度(lon)', '纬度(lat)', '地面气压(hPa)', '气温(℃)', '降水量(mm)', '露点温度(℃)', '经向风速(V,m/s)', '纬向风速(U,m/s)', '太阳辐射净强度(net,J/m2)', '太阳辐射总强度(down,J/m2)', '世界时(UTC)', '北京时(UTC+8)', '备注']
数据类型: {'经度(lon)': dtype('float64'), '纬度(lat)': dtype('float64'), '地面气压(hPa)': dtype('float64'), '气温(℃)': dtype('float64'), '降水量(mm)': dtype('float64'), '露点温度(℃)': dtype('float64'), '经向风速(V,m/s)': dtype('float64'), '纬向风速(U,m/s)': dtype('float64'), '太阳辐射净强度(net,J/m2)': dtype('float64'), '太阳辐射总强度(down,J/m2)': dtype('float64'), '世界时(UTC)': dtype('O'), '北京时(UTC+8)': dtype('O'), '备注': dtype('O')}
唯一值统计: {'世界时(UTC)': {'2023-01-01 00:00:00': 1, '2023-09-01 10:00:00': 1, '2023-09-01 04:00:00': 1, '2023-09-01 05:00:00': 1, '2023-09-01 06:00:00': 1, '2023-09-01 07:00:00': 1, '2023-09-01 08:00:00': 1, '2023-09-01 09:00:00': 1, '2023-09-01 11:00:00': 1, '2023-09-03 05:00:00': 1}, '北京时(UTC+8)': {'2023-01-01 08:00:00': 1, '2023-09-01 18:00:00': 1, '2023-09-01 12:00:00': 1, '2023-09-01 13:00:00': 1, '2023-09-01 14:00:00': 1, '2023-09-01 15:00:00': 1, '2023-09-01 16:00:00': 1, '2023-09-01 17:00:00': 1, '2023-09-01 19:00:00': 1, '2023-09-03 13:00:00': 1}, '备注': {'成华区': 8760}}
样本数据:
  行1: {'经度(lon)': 104.1, '纬度(lat)': 30.7, '地面气压(hPa)': 974.66, '气温(℃)': 3.26, '降水量(mm)': 0.001, '露点温度(℃)': 3.17, '经向风速(V,m/s)': 0.07, '纬向风速(U,m/s)': 0.51, '太阳辐射净强度(net,J/m2)': 0.0, '太阳辐射总强度(down,J/m2)': 0.0, '世界时(UTC)': '2023-01-01 00:00:00', '北京时(UTC+8)': '2023-01-01 08:00:00', '备注': '成华区'}
  行2: {'经度(lon)': 104.1, '纬度(lat)': 30.7, '地面气压(hPa)': 975.35, '气温(℃)': 3.91, '降水量(mm)': 0.0, '露点温度(℃)': 3.72, '经向风速(V,m/s)': 0.25, '纬向风速(U,m/s)': 0.5, '太阳辐射净强度(net,J/m2)': 73703.5, '太阳辐射总强度(down,J/m2)': 85392.25, '世界时(UTC)': '2023-01-01 01:00:00', '北京时(UTC+8)': '2023-01-01 09:00:00', '备注': '成华区'}
  行3: {'经度(lon)': 104.1, '纬度(lat)': 30.7, '地面气压(hPa)': 975.85, '气温(℃)': 4.9, '降水量(mm)': 0.001, '露点温度(℃)': 4.26, '经向风速(V,m/s)': 0.19, '纬向风速(U,m/s)': 0.77, '太阳辐射净强度(net,J/m2)': 287162.0, '太阳辐射总强度(down,J/m2)': 332710.75, '世界时(UTC)': '2023-01-01 02:00:00', '北京时(UTC+8)': '2023-01-01 10:00:00', '备注': '成华区'}

--------------------------------------------------------------------------------

文件名: 城市24小时-2023.xlsx
文件大小: 13.61 MB
解析区域: 多城市
解析年份: 2023
读取状态: 成功
数据形状: (182929, 21)
列名: ['省份', '城市', '日期', '小时', '空气质量指数类别', '空气质量指数级别', 'AQI', 'PM2_5', 'PM2_5_24h', 'PM10', 'PM10_24h', 'SO2', 'SO2_24h', 'NO2', 'NO2_24h', 'O3', 'O3_24h', 'O3_8h', 'O3_8h_24h', 'CO', 'CO_24h']
数据类型: {'省份': dtype('O'), '城市': dtype('O'), '日期': dtype('O'), '小时': dtype('int64'), '空气质量指数类别': dtype('O'), '空气质量指数级别': dtype('O'), 'AQI': dtype('int64'), 'PM2_5': dtype('int64'), 'PM2_5_24h': dtype('int64'), 'PM10': dtype('int64'), 'PM10_24h': dtype('int64'), 'SO2': dtype('int64'), 'SO2_24h': dtype('int64'), 'NO2': dtype('int64'), 'NO2_24h': dtype('int64'), 'O3': dtype('int64'), 'O3_24h': dtype('int64'), 'O3_8h': dtype('int64'), 'O3_8h_24h': dtype('int64'), 'CO': dtype('float64'), 'CO_24h': dtype('float64')}
唯一值统计: {'省份': {'四川': 182929}, '城市': {'绵阳': 8735, '南充': 8735, '成都': 8734, '宜宾': 8734, '攀枝花': 8734, '泸州': 8734, '自贡': 8734, '德阳': 8734, '资阳': 8726, '内江': 8726}, '日期': {'2023-03-11': 1008, '2023-01-01': 504, '2023-08-30': 504, '2023-08-27': 504, '2023-08-26': 504, '2023-08-25': 504, '2023-08-24': 504, '2023-08-23': 504, '2023-08-22': 504, '2023-08-21': 504}, '空气质量指数类别': {'优': 104828, '良': 60832, '轻度污染': 12061, '中度污染': 3561, '重度污染': 1615, '严重污染': 32}, '空气质量指数级别': {'一级': 104828, '二级': 60832, '三级': 12061, '四级': 3561, '五级': 1615, '六级': 32}}
样本数据:
  行1: {'省份': '四川', '城市': '成都', '日期': '2023-01-01', '小时': 0, '空气质量指数类别': '良', '空气质量指数级别': '二级', 'AQI': 83, 'PM2_5': 61, 'PM2_5_24h': 67, 'PM10': 65, 'PM10_24h': 78, 'SO2': 2, 'SO2_24h': 2, 'NO2': 35, 'NO2_24h': 34, 'O3': 13, 'O3_24h': 40, 'O3_8h': 20, 'O3_8h_24h': 28, 'CO': 0.72, 'CO_24h': 0.72}
  行2: {'省份': '四川', '城市': '成都', '日期': '2023-01-01', '小时': 1, '空气质量指数类别': '良', '空气质量指数级别': '二级', 'AQI': 71, 'PM2_5': 52, 'PM2_5_24h': 66, 'PM10': 59, 'PM10_24h': 77, 'SO2': 2, 'SO2_24h': 2, 'NO2': 31, 'NO2_24h': 34, 'O3': 16, 'O3_24h': 40, 'O3_8h': 16, 'O3_8h_24h': 16, 'CO': 0.7, 'CO_24h': 0.72}
  行3: {'省份': '四川', '城市': '成都', '日期': '2023-01-01', '小时': 2, '空气质量指数类别': '良', '空气质量指数级别': '二级', 'AQI': 74, 'PM2_5': 54, 'PM2_5_24h': 65, 'PM10': 59, 'PM10_24h': 76, 'SO2': 2, 'SO2_24h': 2, 'NO2': 31, 'NO2_24h': 34, 'O3': 16, 'O3_24h': 41, 'O3_8h': 15, 'O3_8h_24h': 17, 'CO': 0.69, 'CO_24h': 0.72}

--------------------------------------------------------------------------------

文件名: 城市24小时-24-25.xlsx
文件大小: 16.96 MB
解析区域: 多城市
解析年份: 2024-2025
读取状态: 成功
数据形状: (229650, 21)
列名: ['省份', '城市', '日期', '小时', '空气质量指数类别', '空气质量指数级别', 'AQI', 'PM2_5', 'PM2_5_24h', 'PM10', 'PM10_24h', 'SO2', 'SO2_24h', 'NO2', 'NO2_24h', 'O3', 'O3_24h', 'O3_8h', 'O3_8h_24h', 'CO', 'CO_24h']
数据类型: {'省份': dtype('O'), '城市': dtype('O'), '日期': dtype('O'), '小时': dtype('int64'), '空气质量指数类别': dtype('O'), '空气质量指数级别': dtype('O'), 'AQI': dtype('int64'), 'PM2_5': dtype('int64'), 'PM2_5_24h': dtype('int64'), 'PM10': dtype('int64'), 'PM10_24h': dtype('int64'), 'SO2': dtype('int64'), 'SO2_24h': dtype('int64'), 'NO2': dtype('int64'), 'NO2_24h': dtype('int64'), 'O3': dtype('int64'), 'O3_24h': dtype('int64'), 'O3_8h': dtype('int64'), 'O3_8h_24h': dtype('int64'), 'CO': dtype('float64'), 'CO_24h': dtype('float64')}
唯一值统计: {'省份': {'四川': 229650}, '城市': {'成都': 10944, '德阳': 10944, '南充': 10943, '广元': 10943, '绵阳': 10943, '攀枝花': 10942, '自贡': 10942, '乐山': 10942, '宜宾': 10941, '泸州': 10941}, '日期': {'2024-01-01': 504, '2024-10-17': 504, '2024-10-29': 504, '2024-10-28': 504, '2024-10-27': 504, '2024-10-26': 504, '2024-10-24': 504, '2024-10-23': 504, '2024-10-22': 504, '2024-10-20': 504}, '空气质量指数类别': {'优': 137849, '良': 73217, '轻度污染': 13726, '中度污染': 3155, '重度污染': 1384, '严重污染': 319}, '空气质量指数级别': {'一级': 137849, '二级': 73217, '三级': 13726, '四级': 3155, '五级': 1384, '六级': 319}}
样本数据:
  行1: {'省份': '四川', '城市': '成都', '日期': '2024-01-01', '小时': 0, '空气质量指数类别': '重度污染', '空气质量指数级别': '五级', 'AQI': 203, 'PM2_5': 153, 'PM2_5_24h': 145, 'PM10': 176, 'PM10_24h': 168, 'SO2': 3, 'SO2_24h': 2, 'NO2': 51, 'NO2_24h': 43, 'O3': 4, 'O3_24h': 0, 'O3_8h': 26, 'O3_8h_24h': 14, 'CO': 1.22, 'CO_24h': 1.2}
  行2: {'省份': '四川', '城市': '成都', '日期': '2024-01-01', '小时': 1, '空气质量指数类别': '重度污染', '空气质量指数级别': '五级', 'AQI': 214, 'PM2_5': 164, 'PM2_5_24h': 145, 'PM10': 186, 'PM10_24h': 168, 'SO2': 3, 'SO2_24h': 2, 'NO2': 51, 'NO2_24h': 43, 'O3': 3, 'O3_24h': 0, 'O3_8h': 0, 'O3_8h_24h': 0, 'CO': 1.23, 'CO_24h': 1.2}
  行3: {'省份': '四川', '城市': '成都', '日期': '2024-01-01', '小时': 2, '空气质量指数类别': '重度污染', '空气质量指数级别': '五级', 'AQI': 206, 'PM2_5': 156, 'PM2_5_24h': 145, 'PM10': 178, 'PM10_24h': 168, 'SO2': 2, 'SO2_24h': 2, 'NO2': 49, 'NO2_24h': 44, 'O3': 3, 'O3_24h': 0, 'O3_8h': 0, 'O3_8h_24h': 0, 'CO': 1.22, 'CO_24h': 1.2}

--------------------------------------------------------------------------------

