# 大宽表生成逻辑详细说明

## 概述

大宽表生成是将三类不同的数据源（负荷数据、气象数据、空气质量数据）按照时间和空间维度进行关联合并的过程。

## 数据源分析

### 1. 负荷数据（主表）
**文件**: `complete_load_analysis_data.csv`
- **记录数**: 350,881条
- **文件大小**: 179MB
- **时间粒度**: 小时级
- **关键字段**:
  - `Timestamp`: 时间戳（主连接键）
  - `ConsumerAddress`: 用户地址（用于提取区域信息）
  - `HourlyAvgLoad`, `DailyMaxLoad`等: 负荷指标

### 2. 气象数据（按区域分文件）
**文件模式**: `天气数据/{区域名}{年份}.xls`
- **文件数量**: 约68个文件
- **覆盖区域**: 简阳、成华、双流、大邑、崇州、彭州等22个区域
- **覆盖年份**: 2023、2024、2025
- **时间粒度**: 小时级
- **关键字段**:
  - `北京时(UTC+8)`: 时间戳（连接键）
  - `气温2m(℃)`: 温度
  - `相对湿度(%)`: 湿度
  - `海平面气压(hPa)`: 气压
  - `降水量(mm)`: 降水
  - `纬向风速(U,m/s)`, `经向风速(V,m/s)`: 风速

### 3. 空气质量数据（按年份分文件）
**文件模式**: `天气数据/城市24小时-{年份}.xlsx`
- **文件数量**: 2个文件
  - `城市24小时-2023.xlsx`
  - `城市24小时-24-25.xlsx`
- **覆盖城市**: 21个城市（成都、绵阳、宜宾等）
- **时间粒度**: 小时级
- **关键字段**:
  - `日期` + `小时`: 组合成时间戳（连接键）
  - `城市`: 城市名称（空间连接键）
  - `AQI`: 空气质量指数
  - `PM2_5`, `PM10`: 颗粒物浓度
  - `SO2`, `NO2`, `O3`, `CO`: 气体污染物浓度

## 连接逻辑

### 第一步：负荷数据预处理
```python
# 1. 加载负荷数据作为主表
wide_table = load_data.copy()

# 2. 添加时间特征
wide_table['Year'] = wide_table['Timestamp'].dt.year
wide_table['Month'] = wide_table['Timestamp'].dt.month
wide_table['Hour'] = wide_table['Timestamp'].dt.hour

# 3. 从地址提取区域信息
def extract_region(address):
    for region in ['简阳', '成华', '双流', ...]:
        if region in address:
            return region
    return None

wide_table['Region'] = wide_table['ConsumerAddress'].apply(extract_region)
```

### 第二步：气象数据连接
```python
# 连接条件：时间 + 区域
for region_year, weather_df in weather_data.items():
    region, year = region_year.split('_')
    
    # 1. 筛选匹配的负荷数据
    mask = (wide_table['Year'] == year) & (wide_table['Region'] == region)
    
    # 2. 特征映射
    weather_features = {
        '气温2m(℃)': f'Temperature_{region}',
        '相对湿度(%)': f'Humidity_{region}',
        '海平面气压(hPa)': f'Pressure_{region}',
        '降水量(mm)': f'Precipitation_{region}',
        '纬向风速(U,m/s)': f'WindSpeed_U_{region}',
        '经向风速(V,m/s)': f'WindSpeed_V_{region}'
    }
    
    # 3. 时间序列合并（最近邻匹配）
    merged_subset = pd.merge_asof(
        load_subset.sort_values('Timestamp'),
        weather_for_merge.sort_values('Timestamp'),
        on='Timestamp',
        direction='nearest'
    )
```

### 第三步：空气质量数据连接
```python
# 连接条件：时间 + 城市
for year, aq_df in air_quality_data.items():
    # 1. 筛选对应年份的负荷数据
    mask = (wide_table['Year'] == year)
    
    # 2. 按城市分组处理
    for city in aq_df['城市'].unique():
        city_data = aq_df[aq_df['城市'] == city]
        
        # 3. 特征重命名
        rename_dict = {
            'AQI': f'AQI_{city}',
            'PM2_5': f'PM2_5_{city}',
            'PM10': f'PM10_{city}',
            'SO2': f'SO2_{city}',
            'NO2': f'NO2_{city}',
            'O3': f'O3_{city}',
            'CO': f'CO_{city}'
        }
        
        # 4. 时间序列合并
        merged_subset = pd.merge_asof(
            load_subset.sort_values('Timestamp'),
            aq_for_merge.sort_values('Timestamp'),
            on='Timestamp',
            direction='nearest'
        )
```

## 连接策略

### 1. 时间连接
- **方法**: `pd.merge_asof` 最近邻时间匹配
- **原因**: 不同数据源的时间戳可能不完全对齐
- **方向**: `direction='nearest'` 选择最近的时间点

### 2. 空间连接
- **气象数据**: 通过区域名称精确匹配
- **空气质量数据**: 通过城市名称匹配（覆盖更广）
- **负荷数据**: 从用户地址中提取区域信息

### 3. 特征命名
- **气象特征**: `{特征类型}_{区域名}` (如: `Temperature_简阳`)
- **空气质量特征**: `{污染物}_{城市名}` (如: `AQI_成都`)

## 数据覆盖情况

### 时间覆盖
- **负荷数据**: 2023-01-01 到 2024-12-31
- **气象数据**: 2023-2025年（按区域文件分布）
- **空气质量数据**: 2023年 + 2024-2025年

### 空间覆盖
- **负荷数据**: 主要集中在简阳、新都等区域
- **气象数据**: 22个区域，但与负荷数据匹配的有限
- **空气质量数据**: 21个城市，覆盖面最广

## 连接结果

### 成功连接的数据
1. **空气质量数据**: 21个城市 × 7个指标 = 147个特征
2. **气象数据**: 连接成功的区域较少（因为区域匹配限制）

### 连接挑战
1. **区域匹配问题**: 负荷数据的地址信息与气象数据的区域名称不完全匹配
2. **时间对齐问题**: 不同数据源的时间精度和格式差异
3. **数据完整性**: 不同数据源的时间覆盖范围不同

## 优化建议

### 1. 改进区域匹配
```python
# 建立更完善的地址-区域映射表
address_region_mapping = {
    '简阳市': '简阳',
    '成华区': '成华',
    '双流区': '双流',
    # ... 更多映射关系
}
```

### 2. 增强时间对齐
```python
# 使用更精确的时间窗口匹配
tolerance = pd.Timedelta('30min')  # 30分钟容差
merged = pd.merge_asof(..., tolerance=tolerance)
```

### 3. 处理缺失值
```python
# 对于缺失的气象数据，可以使用邻近区域的数据填充
# 对于缺失的空气质量数据，可以使用城市级别的平均值
```

## 最终大宽表结构

- **总列数**: 202列
- **负荷特征**: ~47列（原始负荷数据）
- **时间特征**: 7列（Year, Month, Day, Hour, DayOfWeek, IsWeekend, Season）
- **空间特征**: 1列（Region）
- **气象特征**: 少量（由于匹配限制）
- **空气质量特征**: 147列（21城市 × 7指标）

这种设计确保了数据的完整性和可用性，为后续的空调负荷识别和柔性调控分析提供了丰富的特征基础。
