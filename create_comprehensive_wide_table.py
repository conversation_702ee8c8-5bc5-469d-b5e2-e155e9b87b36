#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空调负荷柔性调控能力分析系统 - 综合大宽表生成脚本

整合负荷数据、气象数据和空气质量数据，生成完整的建模用大宽表

数据类型：
1. 负荷数据：complete_load_analysis_data.csv
2. 气象数据：地名开头的xls文件（温度、湿度、气压等）
3. 空气质量数据：城市24小时开头的xlsx文件（AQI、PM2.5等）

作者: AI Assistant
创建时间: 2025-07-13
"""

import pandas as pd
import numpy as np
import warnings
import logging
import os
import glob
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple

warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/comprehensive_wide_table.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ComprehensiveWideTableCreator:
    """综合大宽表创建器"""
    
    def __init__(self):
        """初始化"""
        self.load_data = None
        self.weather_data = {}  # 气象数据
        self.air_quality_data = {}  # 空气质量数据
        self.wide_table = None
        
        # 创建必要目录
        Path('logs').mkdir(exist_ok=True)
        Path('data/processed').mkdir(parents=True, exist_ok=True)
        
        logger.info("综合大宽表创建器初始化完成")
    
    def load_load_data(self, sample_size: Optional[int] = None) -> pd.DataFrame:
        """加载负荷数据"""
        if sample_size:
            logger.info(f"加载负荷数据样本 ({sample_size} 行)...")
            self.load_data = pd.read_csv('complete_load_analysis_data.csv', nrows=sample_size)
        else:
            logger.info("加载完整负荷数据集...")
            # 分块读取大文件
            chunk_size = 50000
            chunks = []
            for i, chunk in enumerate(pd.read_csv('complete_load_analysis_data.csv', chunksize=chunk_size)):
                chunks.append(chunk)
                if (i + 1) % 5 == 0:  # 每5个chunk显示一次进度
                    logger.info(f"已加载 {(i + 1) * chunk_size} 行数据...")

            self.load_data = pd.concat(chunks, ignore_index=True)
            logger.info(f"完整数据集加载完成: {self.load_data.shape}")
        
        # 时间处理
        self.load_data['Timestamp'] = pd.to_datetime(self.load_data['Timestamp'])
        
        # 添加时间特征
        self.load_data['Year'] = self.load_data['Timestamp'].dt.year
        self.load_data['Month'] = self.load_data['Timestamp'].dt.month
        self.load_data['Day'] = self.load_data['Timestamp'].dt.day
        self.load_data['Hour'] = self.load_data['Timestamp'].dt.hour
        self.load_data['DayOfWeek'] = self.load_data['Timestamp'].dt.dayofweek
        self.load_data['IsWeekend'] = (self.load_data['DayOfWeek'] >= 5).astype(int)
        
        # 季节特征
        self.load_data['Season'] = self.load_data['Month'].map({
            12: 'Winter', 1: 'Winter', 2: 'Winter',
            3: 'Spring', 4: 'Spring', 5: 'Spring',
            6: 'Summer', 7: 'Summer', 8: 'Summer',
            9: 'Autumn', 10: 'Autumn', 11: 'Autumn'
        })
        
        # 从地址提取区域信息
        if 'ConsumerAddress' in self.load_data.columns:
            def extract_region(address):
                if pd.isna(address):
                    return None
                address = str(address)
                regions = ['简阳', '成华', '双流', '大邑', '崇州', '彭州', '新津', '新都', 
                          '武侯', '温江', '甘孜', '蒲江', '邛崃', '郫都', '都江堰', 
                          '金堂', '金牛', '锦江', '青白江', '青羊', '龙泉驿']
                for region in regions:
                    if region in address:
                        return region
                return None
            
            self.load_data['Region'] = self.load_data['ConsumerAddress'].apply(extract_region)
        
        logger.info(f"负荷数据加载完成: {self.load_data.shape}")
        return self.load_data
    
    def load_weather_data(self, target_regions: List[str] = None) -> Dict[str, pd.DataFrame]:
        """加载气象数据（地名开头的文件）"""
        logger.info("加载气象数据...")
        
        if target_regions is None:
            target_regions = ['简阳', '成华', '双流', '大邑']  # 默认区域
        
        weather_files = glob.glob('天气数据/*.xls') + glob.glob('天气数据/*.xlsx')
        
        for file_path in weather_files:
            file_name = Path(file_path).stem
            
            # 跳过空气质量数据文件
            if '城市24小时' in file_name:
                continue
            
            # 解析区域和年份
            region = None
            year = None
            
            for target_region in target_regions:
                if target_region in file_name:
                    region = target_region
                    if '23' in file_name:
                        year = 2023
                    elif '24' in file_name:
                        year = 2024
                    elif '25' in file_name:
                        year = 2025
                    break
            
            if region and year:
                try:
                    df = pd.read_excel(file_path)
                    
                    # 处理时间列
                    time_col = None
                    for col in df.columns:
                        if any(keyword in col for keyword in ['北京时', 'UTC+8', '时间']):
                            time_col = col
                            break
                    
                    if time_col:
                        df['Timestamp'] = pd.to_datetime(df[time_col])
                        df = df.sort_values('Timestamp').reset_index(drop=True)
                        
                        # 添加元数据
                        df['region'] = region
                        df['year'] = year
                        
                        key = f"{region}_{year}"
                        self.weather_data[key] = df
                        logger.info(f"加载气象数据 {key}: {df.shape}")
                    
                except Exception as e:
                    logger.warning(f"加载气象文件失败 {file_path}: {e}")
        
        logger.info(f"气象数据加载完成: {len(self.weather_data)} 个数据集")
        return self.weather_data
    
    def load_air_quality_data(self) -> Dict[str, pd.DataFrame]:
        """加载空气质量数据（城市24小时开头的文件）"""
        logger.info("加载空气质量数据...")
        
        air_quality_files = glob.glob('天气数据/城市24小时*.xlsx')
        
        for file_path in air_quality_files:
            file_name = Path(file_path).stem
            
            try:
                df = pd.read_excel(file_path)
                
                # 处理时间列
                if '日期' in df.columns and '小时' in df.columns:
                    # 合并日期和小时
                    df['Timestamp'] = pd.to_datetime(df['日期']) + pd.to_timedelta(df['小时'], unit='h')
                    df = df.sort_values('Timestamp').reset_index(drop=True)
                    
                    # 确定年份
                    if '2023' in file_name:
                        year = 2023
                    elif '24-25' in file_name:
                        year = 2024  # 主要年份
                    else:
                        year = df['Timestamp'].dt.year.iloc[0]
                    
                    key = f"air_quality_{year}"
                    self.air_quality_data[key] = df
                    logger.info(f"加载空气质量数据 {key}: {df.shape}")
                    
                    # 显示城市列表
                    if '城市' in df.columns:
                        cities = df['城市'].unique()
                        logger.info(f"  覆盖城市: {len(cities)} 个")
                
            except Exception as e:
                logger.warning(f"加载空气质量文件失败 {file_path}: {e}")
        
        logger.info(f"空气质量数据加载完成: {len(self.air_quality_data)} 个数据集")
        return self.air_quality_data
    
    def create_wide_table(self) -> pd.DataFrame:
        """创建综合大宽表"""
        logger.info("开始创建综合大宽表...")
        
        if self.load_data is None:
            raise ValueError("请先加载负荷数据")
        
        # 从负荷数据开始
        wide_table = self.load_data.copy()
        logger.info(f"起始数据形状: {wide_table.shape}")
        
        # 1. 合并气象数据
        weather_merged_count = 0
        for key, weather_df in self.weather_data.items():
            region, year = key.split('_')
            year = int(year)
            
            # 筛选匹配的负荷数据
            mask = (wide_table['Year'] == year)
            if 'Region' in wide_table.columns:
                mask = mask & (wide_table['Region'] == region)
            
            matching_records = mask.sum()
            if matching_records == 0:
                logger.info(f"没有找到匹配的负荷数据 {key}")
                continue
            
            # 选择主要气象特征
            weather_features = {}
            for col in weather_df.columns:
                if '气温' in col or '温度' in col:
                    weather_features[col] = f'Temperature_{region}'
                elif '湿度' in col:
                    weather_features[col] = f'Humidity_{region}'
                elif '气压' in col:
                    weather_features[col] = f'Pressure_{region}'
                elif '降水' in col:
                    weather_features[col] = f'Precipitation_{region}'
                elif '风速' in col and 'U' in col:
                    weather_features[col] = f'WindSpeed_U_{region}'
                elif '风速' in col and 'V' in col:
                    weather_features[col] = f'WindSpeed_V_{region}'
            
            if weather_features:
                # 准备合并数据
                weather_for_merge = weather_df[['Timestamp'] + list(weather_features.keys())].copy()
                weather_for_merge = weather_for_merge.rename(columns=weather_features)
                weather_for_merge = weather_for_merge.dropna(subset=['Timestamp'])
                
                # 时间序列合并
                try:
                    load_subset = wide_table[mask].copy().sort_values('Timestamp')
                    weather_for_merge = weather_for_merge.sort_values('Timestamp')
                    
                    merged_subset = pd.merge_asof(
                        load_subset,
                        weather_for_merge,
                        on='Timestamp',
                        direction='nearest'
                    )
                    
                    # 更新原始数据
                    for col in weather_features.values():
                        if col in merged_subset.columns:
                            wide_table.loc[mask, col] = merged_subset[col].values
                    
                    weather_merged_count += 1
                    logger.info(f"气象数据 {key} 合并完成，匹配记录: {matching_records}")
                    
                except Exception as e:
                    logger.warning(f"气象数据 {key} 合并失败: {e}")
        
        # 2. 合并空气质量数据
        air_quality_merged_count = 0
        for key, aq_df in self.air_quality_data.items():
            year = int(key.split('_')[-1])
            
            # 筛选对应年份的负荷数据
            mask = (wide_table['Year'] == year)
            matching_records = mask.sum()
            
            if matching_records == 0:
                logger.info(f"没有找到匹配的负荷数据 {key}")
                continue
            
            # 选择主要空气质量特征
            aq_features = ['AQI', 'PM2_5', 'PM10', 'SO2', 'NO2', 'O3', 'CO']
            available_features = [col for col in aq_features if col in aq_df.columns]
            
            if available_features and '城市' in aq_df.columns:
                # 按城市分组处理
                for city in aq_df['城市'].unique():
                    city_data = aq_df[aq_df['城市'] == city].copy()
                    
                    if len(city_data) == 0:
                        continue
                    
                    # 准备合并数据
                    aq_for_merge = city_data[['Timestamp'] + available_features].copy()
                    
                    # 重命名列
                    rename_dict = {col: f'{col}_{city}' for col in available_features}
                    aq_for_merge = aq_for_merge.rename(columns=rename_dict)
                    aq_for_merge = aq_for_merge.dropna(subset=['Timestamp'])
                    
                    # 时间序列合并
                    try:
                        load_subset = wide_table[mask].copy().sort_values('Timestamp')
                        aq_for_merge = aq_for_merge.sort_values('Timestamp')
                        
                        merged_subset = pd.merge_asof(
                            load_subset,
                            aq_for_merge,
                            on='Timestamp',
                            direction='nearest'
                        )
                        
                        # 更新原始数据
                        for col in rename_dict.values():
                            if col in merged_subset.columns:
                                wide_table.loc[mask, col] = merged_subset[col].values
                        
                        logger.info(f"空气质量数据 {city}_{year} 合并完成")
                        
                    except Exception as e:
                        logger.warning(f"空气质量数据 {city}_{year} 合并失败: {e}")
                
                air_quality_merged_count += 1
        
        self.wide_table = wide_table
        logger.info(f"综合大宽表创建完成，形状: {wide_table.shape}")
        logger.info(f"合并了 {weather_merged_count} 个气象数据集，{air_quality_merged_count} 个空气质量数据集")
        
        return wide_table

    def save_wide_table(self, filename: str = None) -> str:
        """保存综合大宽表"""
        if self.wide_table is None:
            raise ValueError("请先创建大宽表")

        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"comprehensive_wide_table_{timestamp}.csv"

        filepath = os.path.join('data/processed', filename)

        # 保存为CSV
        self.wide_table.to_csv(filepath, index=False, encoding='utf-8')
        logger.info(f"综合大宽表已保存至: {filepath}")

        # 生成详细摘要
        summary_file = filepath.replace('.csv', '_summary.txt')
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("综合大宽表数据摘要\n")
            f.write("=" * 60 + "\n")
            f.write(f"创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"数据形状: {self.wide_table.shape}\n")
            f.write(f"时间范围: {self.wide_table['Timestamp'].min()} 到 {self.wide_table['Timestamp'].max()}\n\n")

            # 数据类型统计
            f.write("数据类型统计:\n")
            f.write("-" * 30 + "\n")
            f.write("1. 负荷数据特征\n")
            load_cols = [col for col in self.wide_table.columns if any(keyword in col for keyword in
                        ['Load', 'Energy', 'Power', 'Factor'])]
            f.write(f"   负荷相关列数: {len(load_cols)}\n")

            f.write("2. 时间特征\n")
            time_cols = [col for col in self.wide_table.columns if col in
                        ['Year', 'Month', 'Day', 'Hour', 'DayOfWeek', 'IsWeekend', 'Season']]
            f.write(f"   时间特征列数: {len(time_cols)}\n")

            f.write("3. 气象数据特征\n")
            weather_cols = [col for col in self.wide_table.columns if any(keyword in col for keyword in
                           ['Temperature', 'Humidity', 'Pressure', 'Precipitation', 'WindSpeed'])]
            f.write(f"   气象特征列数: {len(weather_cols)}\n")
            if weather_cols:
                f.write("   气象特征列:\n")
                for col in weather_cols:
                    non_null = self.wide_table[col].notna().sum()
                    pct = (non_null / len(self.wide_table)) * 100
                    f.write(f"     - {col}: {non_null}/{len(self.wide_table)} ({pct:.1f}%)\n")

            f.write("4. 空气质量数据特征\n")
            aq_cols = [col for col in self.wide_table.columns if any(keyword in col for keyword in
                      ['AQI', 'PM2_5', 'PM10', 'SO2', 'NO2', 'O3', 'CO'])]
            f.write(f"   空气质量特征列数: {len(aq_cols)}\n")
            if aq_cols:
                f.write("   空气质量特征列:\n")
                for col in aq_cols:
                    non_null = self.wide_table[col].notna().sum()
                    pct = (non_null / len(self.wide_table)) * 100
                    f.write(f"     - {col}: {non_null}/{len(self.wide_table)} ({pct:.1f}%)\n")

            # 区域分布
            f.write("\n5. 区域分布\n")
            f.write("-" * 30 + "\n")
            if 'Region' in self.wide_table.columns:
                region_counts = self.wide_table['Region'].value_counts()
                for region, count in region_counts.items():
                    pct = (count / len(self.wide_table)) * 100
                    f.write(f"   {region}: {count} ({pct:.1f}%)\n")

            # 缺失值统计
            f.write("\n6. 缺失值统计\n")
            f.write("-" * 30 + "\n")
            missing_stats = self.wide_table.isnull().sum()
            missing_stats = missing_stats[missing_stats > 0].sort_values(ascending=False)

            if len(missing_stats) > 0:
                for col, count in missing_stats.items():
                    pct = (count / len(self.wide_table)) * 100
                    f.write(f"   {col}: {count} ({pct:.2f}%)\n")
            else:
                f.write("   无缺失值\n")

        logger.info(f"数据摘要已保存至: {summary_file}")
        return filepath

def main():
    """主函数"""
    print("空调负荷柔性调控能力分析系统 - 综合大宽表生成")
    print("=" * 70)

    try:
        # 创建综合大宽表生成器
        creator = ComprehensiveWideTableCreator()

        # 1. 加载负荷数据（加载完整数据集）
        print("1. 加载负荷数据...")
        creator.load_load_data(sample_size=None)  # 加载全部数据

        # 2. 加载气象数据
        print("2. 加载气象数据...")
        creator.load_weather_data(target_regions=['简阳', '成华', '双流'])

        # 3. 加载空气质量数据
        print("3. 加载空气质量数据...")
        creator.load_air_quality_data()

        # 4. 创建综合大宽表
        print("4. 创建综合大宽表...")
        wide_table = creator.create_wide_table()
        print(f"   综合大宽表创建完成: {wide_table.shape}")

        # 5. 保存大宽表
        print("5. 保存综合大宽表...")
        filepath = creator.save_wide_table()
        print(f"   综合大宽表已保存至: {filepath}")

        # 6. 显示摘要信息
        print("\n6. 综合大宽表摘要:")
        print(f"   数据形状: {wide_table.shape}")
        print(f"   时间范围: {wide_table['Timestamp'].min()} 到 {wide_table['Timestamp'].max()}")

        # 特征统计
        weather_cols = [col for col in wide_table.columns if any(keyword in col for keyword in
                       ['Temperature', 'Humidity', 'Pressure', 'Precipitation', 'WindSpeed'])]
        aq_cols = [col for col in wide_table.columns if any(keyword in col for keyword in
                  ['AQI', 'PM2_5', 'PM10', 'SO2', 'NO2', 'O3', 'CO'])]

        print(f"   气象特征数: {len(weather_cols)}")
        print(f"   空气质量特征数: {len(aq_cols)}")

        if 'Region' in wide_table.columns:
            regions = wide_table['Region'].value_counts()
            print(f"   覆盖区域: {len(regions)} 个")

        print("\n" + "=" * 70)
        print("综合大宽表生成完成！")
        print("数据包含:")
        print("✓ 负荷数据（电力消费模式）")
        print("✓ 气象数据（温度、湿度、气压等）")
        print("✓ 空气质量数据（AQI、PM2.5等）")
        print("✓ 时间特征（年月日时、季节、工作日等）")
        print("\n下一步建议:")
        print("1. 进行数据质量检查和可视化分析")
        print("2. 开始特征工程和相关性分析")
        print("3. 构建空调负荷识别和预测模型")

    except Exception as e:
        logger.error(f"综合大宽表生成失败: {e}")
        print(f"错误: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())
