#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空调负荷柔性调控能力分析系统 - 环境配置脚本
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """执行命令并显示进度"""
    print(f"正在{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print(f"✓ {description}完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description}失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def main():
    """主函数：配置开发环境"""
    print("=" * 60)
    print("空调负荷柔性调控能力分析系统 - 环境配置")
    print("=" * 60)
    
    # 检查conda是否安装
    if not run_command("conda --version", "检查conda环境"):
        print("请先安装Anaconda或Miniconda")
        return False
    
    # 创建conda环境
    env_name = "ac_load_analysis"
    if not run_command(f"conda create -n {env_name} python=3.9 -y", 
                      f"创建conda环境 {env_name}"):
        return False
    
    # 激活环境并安装依赖
    activate_cmd = f"conda activate {env_name}"
    pip_cmd = f"{activate_cmd} && pip install -r requirements.txt"
    
    if not run_command(pip_cmd, "安装Python依赖包"):
        return False
    
    # 创建项目目录结构
    directories = [
        "data/raw",           # 原始数据
        "data/processed",     # 处理后的数据
        "data/interim",       # 中间数据
        "models",             # 模型文件
        "results",            # 分析结果
        "notebooks",          # Jupyter notebooks
        "src",                # 源代码
        "src/data",           # 数据处理模块
        "src/features",       # 特征工程模块
        "src/models",         # 模型模块
        "src/visualization",  # 可视化模块
        "logs",               # 日志文件
        "config",             # 配置文件
    ]
    
    print("创建项目目录结构...")
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"  ✓ 创建目录: {directory}")
    
    # 创建配置文件
    create_config_files()
    
    print("\n" + "=" * 60)
    print("环境配置完成！")
    print(f"请使用以下命令激活环境: conda activate {env_name}")
    print("然后运行: jupyter lab")
    print("=" * 60)
    
    return True

def create_config_files():
    """创建配置文件"""
    # 创建.gitignore
    gitignore_content = """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Jupyter Notebook
.ipynb_checkpoints

# 环境文件
.env
.venv
env/
venv/
ENV/

# 数据文件
data/raw/*
data/processed/*
data/interim/*
!data/raw/.gitkeep
!data/processed/.gitkeep
!data/interim/.gitkeep

# 模型文件
models/*.pkl
models/*.joblib
models/*.h5

# 结果文件
results/*
!results/.gitkeep

# 日志文件
logs/*
!logs/.gitkeep

# IDE
.vscode/
.idea/
*.swp
*.swo

# 系统文件
.DS_Store
Thumbs.db
"""
    
    with open(".gitignore", "w", encoding="utf-8") as f:
        f.write(gitignore_content)
    
    # 创建配置文件
    config_content = """# 空调负荷分析系统配置文件
[data]
# 数据路径配置
raw_data_path = "data/raw"
processed_data_path = "data/processed"
interim_data_path = "data/interim"

# 天气数据路径
weather_data_path = "天气数据"

# 负荷数据文件
load_data_file = "complete_load_analysis_data.csv"

[model]
# 模型参数
random_state = 42
test_size = 0.2
validation_size = 0.1

# 温度敏感度分析参数
base_temperature = 18.0  # 基准温度（空调不工作温度）
temperature_threshold = 25.0  # 空调启动温度阈值

[features]
# 特征工程参数
lag_features = [1, 2, 3, 6, 12, 24]  # 滞后特征时间窗口
rolling_windows = [3, 6, 12, 24]     # 滚动窗口大小

[visualization]
# 可视化配置
figure_size = (12, 8)
dpi = 300
style = "seaborn-v0_8"

[logging]
# 日志配置
level = "INFO"
format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
"""
    
    with open("config/config.ini", "w", encoding="utf-8") as f:
        f.write(config_content)
    
    print("  ✓ 创建配置文件")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 