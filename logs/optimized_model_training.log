2025-07-14 08:07:49,881 - INFO - 优化空调负荷模型训练器初始化完成
2025-07-14 08:07:49,881 - INFO - 加载并准备数据...
2025-07-14 08:07:49,881 - INFO - 加载数据文件: data/processed/final_wide_table_20250714_075137.csv
2025-07-14 08:07:51,302 - INFO - 已加载 250000 行数据...
2025-07-14 08:07:51,934 - INFO - 数据加载完成，形状: (350880, 127)
2025-07-14 08:07:51,934 - INFO - 创建空调负荷目标变量...
2025-07-14 08:07:52,144 - INFO - 使用温度列: Temperature_甘孜
2025-07-14 08:07:52,160 - INFO - 空调负荷统计: 均值=2.98, 标准差=15.55
2025-07-14 08:07:52,160 - INFO - 非零空调负荷比例: 51.18%
2025-07-14 08:07:52,182 - INFO - 准备特征数据...
2025-07-14 08:07:52,455 - INFO - 选择了 53 个有效特征
2025-07-14 08:07:52,455 - INFO - 特征列表前10个: ['WindSpeed_U_金堂', 'HourlyMinLoad', 'SO2_甘孜', 'Precipitation_锦江', 'Pressure_金堂', 'Pressure_都江堰', 'Precipitation_简阳', 'Humidity_甘孜', 'Precipitation_新都', 'Temp_Squared']
2025-07-14 08:07:52,475 - INFO - 数据分割和预处理...
2025-07-14 08:07:52,507 - INFO - 移除异常值后数据形状: X=(303435, 53), y=(303435,)
2025-07-14 08:07:52,593 - INFO - 进行特征选择...
2025-07-14 08:07:52,737 - INFO - 最终数据形状:
2025-07-14 08:07:52,737 - INFO -   训练集: (182061, 40)
2025-07-14 08:07:52,737 - INFO -   验证集: (60687, 40)
2025-07-14 08:07:52,737 - INFO -   测试集: (60687, 40)
2025-07-14 08:07:52,737 - INFO -   选中特征数: 40
2025-07-14 08:07:52,765 - INFO - 训练优化的XGBoost模型...
2025-07-14 08:07:52,765 - INFO - 开始网格搜索调参...
2025-07-14 08:13:35,009 - INFO - 最佳参数: {'colsample_bytree': 0.9, 'learning_rate': 0.05, 'max_depth': 4, 'n_estimators': 200, 'reg_alpha': 0.1, 'reg_lambda': 1.5, 'subsample': 0.8}
2025-07-14 08:13:35,009 - INFO - 最佳交叉验证分数: 0.0714
2025-07-14 08:13:36,093 - INFO - XGBoost模型性能:
2025-07-14 08:13:36,094 - INFO -   验证集 R²: 0.0743
2025-07-14 08:13:36,094 - INFO -   测试集 R²: 0.0743
2025-07-14 08:13:36,094 - INFO -   测试集 RMSE: 0.0323
2025-07-14 08:13:36,094 - INFO - 训练优化的深度学习模型...
2025-07-14 08:13:36,140 - INFO - 开始训练深度学习模型...
2025-07-14 08:13:40,829 - WARNING - You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. 
2025-07-14 08:13:43,275 - WARNING - You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. 
2025-07-14 08:13:47,573 - WARNING - You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. 
2025-07-14 08:13:57,041 - WARNING - You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. 
2025-07-14 08:13:59,044 - WARNING - You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. 
2025-07-14 08:14:13,545 - WARNING - You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. 
2025-07-14 08:14:16,188 - WARNING - You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. 
2025-07-14 08:14:57,777 - INFO - 深度学习模型性能:
2025-07-14 08:14:57,777 - INFO -   验证集 R²: 0.0685
2025-07-14 08:14:57,777 - INFO -   测试集 R²: 0.0666
2025-07-14 08:14:57,777 - INFO -   测试集 RMSE: 0.0324
2025-07-14 08:14:57,777 - INFO - 创建模型比较图表...
2025-07-14 08:14:58,937 - INFO - 模型比较图表已保存至: figures/optimized_models/model_comparison.png
2025-07-14 08:14:58,938 - INFO - 保存训练好的模型...
2025-07-14 08:14:58,940 - WARNING - You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. 
2025-07-14 08:14:58,952 - INFO - 模型保存完成:
2025-07-14 08:14:58,952 - INFO -   xgboost_model: models/optimized/xgboost_model_20250714_081458.pkl
2025-07-14 08:14:58,952 - INFO -   dl_model: models/optimized/dl_model_20250714_081458.h5
2025-07-14 08:14:58,952 - INFO -   scaler: models/optimized/scaler_20250714_081458.pkl
2025-07-14 08:14:58,952 - INFO -   feature_selector: models/optimized/feature_selector_20250714_081458.pkl
2025-07-14 08:14:58,952 - INFO -   model_info: models/optimized/model_info_20250714_081458.json
