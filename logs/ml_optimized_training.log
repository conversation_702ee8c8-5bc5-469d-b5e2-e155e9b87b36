2025-07-14 09:09:14,155 - INFO - 机器学习优化空调负荷模型训练器初始化完成
2025-07-14 09:09:14,155 - INFO - 加载并准备数据...
2025-07-14 09:09:14,155 - INFO - 加载数据文件: data/processed/final_wide_table_20250714_075137.csv
2025-07-14 09:09:15,510 - INFO - 已加载 250000 行数据...
2025-07-14 09:09:16,184 - INFO - 数据加载完成，形状: (350880, 127)
2025-07-14 09:09:16,185 - INFO - 创建复杂的空调负荷目标变量...
2025-07-14 09:09:16,477 - INFO - 使用温度列: Temperature_甘孜
2025-07-14 09:09:16,477 - INFO - 使用湿度列: Humidity_甘孜
2025-07-14 09:09:17,369 - INFO - 处理进度: 0/350880
2025-07-14 09:09:20,886 - INFO - 处理进度: 50000/350880
2025-07-14 09:09:24,607 - INFO - 处理进度: 100000/350880
2025-07-14 09:09:28,314 - INFO - 处理进度: 150000/350880
2025-07-14 09:09:32,856 - INFO - 处理进度: 200000/350880
2025-07-14 09:09:36,750 - INFO - 处理进度: 250000/350880
2025-07-14 09:09:40,848 - INFO - 处理进度: 300000/350880
2025-07-14 09:09:44,508 - INFO - 处理进度: 350000/350880
2025-07-14 09:09:44,585 - INFO - 复杂空调负荷目标变量统计:
2025-07-14 09:09:44,585 - INFO -   mean: 9.4705
2025-07-14 09:09:44,585 - INFO -   std: 32.2720
2025-07-14 09:09:44,585 - INFO -   min: 0.0000
2025-07-14 09:09:44,586 - INFO -   max: 363.2966
2025-07-14 09:09:44,586 - INFO -   non_zero_ratio: 1.0000
2025-07-14 09:09:44,586 - INFO -   q25: 0.0327
2025-07-14 09:09:44,586 - INFO -   q50: 0.0976
2025-07-14 09:09:44,586 - INFO -   q75: 0.2291
2025-07-14 09:09:44,586 - INFO -   q95: 71.8965
2025-07-14 09:09:44,791 - INFO - 进行高级特征工程...
2025-07-14 09:09:45,749 - INFO - 高级特征工程完成，选择了 67 个特征
2025-07-14 09:09:45,750 - INFO - 特征类别分布:
2025-07-14 09:09:45,750 - INFO -   时间特征: 19
2025-07-14 09:09:45,750 - INFO -   气象特征: 25
2025-07-14 09:09:45,750 - INFO -   空气质量特征: 7
2025-07-14 09:09:45,750 - INFO -   交互特征: 3
2025-07-14 09:09:45,750 - INFO -   衍生特征: 8
2025-07-14 09:09:45,783 - INFO - 数据分割和预处理...
2025-07-14 09:09:45,854 - INFO - 移除极端异常值后数据形状: X=(348979, 67), y=(348979,)
2025-07-14 09:09:45,854 - INFO - 保留数据比例: 99.46%
2025-07-14 09:09:45,855 - INFO - 进行智能特征选择...
2025-07-14 09:10:49,169 - INFO - 最终数据形状:
2025-07-14 09:10:49,169 - INFO -   训练集: (244285, 50)
2025-07-14 09:10:49,169 - INFO -   验证集: (52347, 50)
2025-07-14 09:10:49,169 - INFO -   测试集: (52347, 50)
2025-07-14 09:10:49,169 - INFO -   选中特征数: 50
2025-07-14 09:10:49,170 - INFO - 前10个最重要特征:
2025-07-14 09:10:49,170 - INFO -   HourlyMinLoad: 1.0000
2025-07-14 09:10:49,170 - INFO -   HourlyMaxLoad: 0.9999
2025-07-14 09:10:49,170 - INFO -   HourlyActiveEnergy: 0.9998
2025-07-14 09:10:49,170 - INFO -   HourlyReactiveEnergy: 0.3525
2025-07-14 09:10:49,170 - INFO -   Hour: 0.2269
2025-07-14 09:10:49,170 - INFO -   Month_cos: 0.1704
2025-07-14 09:10:49,170 - INFO -   Month: 0.1385
2025-07-14 09:10:49,170 - INFO -   Hour_sin: 0.1268
2025-07-14 09:10:49,170 - INFO -   Hour_Month_interaction: 0.1258
2025-07-14 09:10:49,170 - INFO -   Hour_cos: 0.1240
2025-07-14 09:10:49,196 - INFO - 训练优化的XGBoost模型...
2025-07-14 09:10:49,196 - INFO - 开始随机搜索调参...
2025-07-14 09:11:46,807 - INFO - 最佳参数: {'subsample': 0.8, 'reg_lambda': 1, 'reg_alpha': 0.1, 'n_estimators': 500, 'min_child_weight': 5, 'max_depth': 10, 'learning_rate': 0.05, 'colsample_bytree': 0.8}
2025-07-14 09:11:46,808 - INFO - 最佳交叉验证分数: 0.9846
2025-07-14 09:11:54,532 - INFO - XGBoost模型性能:
2025-07-14 09:11:54,532 - INFO -   验证集 R²: 0.7133
2025-07-14 09:11:54,532 - INFO -   测试集 R²: -1.2211
2025-07-14 09:11:54,532 - INFO -   测试集 RMSE: 71.6076
2025-07-14 09:11:54,532 - INFO -   测试集 MAE: 53.1549
2025-07-14 09:11:54,532 - INFO - 训练高级深度学习模型...
2025-07-14 09:11:54,637 - INFO - 开始训练高级深度学习模型...
2025-07-14 09:12:01,506 - WARNING - You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. 
2025-07-14 09:12:06,335 - WARNING - You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. 
2025-07-14 09:12:11,198 - WARNING - You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. 
2025-07-14 09:12:26,861 - WARNING - You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. 
2025-07-14 09:12:44,188 - WARNING - You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. 
2025-07-14 09:12:51,013 - WARNING - You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. 
2025-07-14 09:12:57,055 - WARNING - You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. 
2025-07-14 09:13:09,061 - WARNING - You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. 
2025-07-14 09:13:14,675 - WARNING - You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. 
2025-07-14 09:15:29,054 - INFO - 深度学习模型性能:
2025-07-14 09:15:29,054 - INFO -   验证集 R²: 0.9505
2025-07-14 09:15:29,054 - INFO -   测试集 R²: -63.1683
2025-07-14 09:15:29,054 - INFO -   测试集 RMSE: 384.8901
2025-07-14 09:15:29,054 - INFO -   测试集 MAE: 322.9851
2025-07-14 09:15:29,054 - INFO - 创建综合分析图表...
2025-07-14 09:15:30,524 - INFO - 综合分析图表已保存至: figures/ml_analysis/comprehensive_analysis.png
2025-07-14 09:15:30,525 - INFO - 保存模型和分析结果...
2025-07-14 09:15:30,539 - WARNING - You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. 
2025-07-14 09:15:30,574 - INFO - 模型和分析结果保存完成:
2025-07-14 09:15:30,574 - INFO -   xgboost_model: models/ml_optimized/xgboost_model_20250714_091530.pkl
2025-07-14 09:15:30,574 - INFO -   dl_model: models/ml_optimized/dl_model_20250714_091530.h5
2025-07-14 09:15:30,574 - INFO -   model_architecture: figures/ml_analysis/model_architecture_20250714_091530.png
2025-07-14 09:15:30,574 - INFO -   scaler: models/ml_optimized/scaler_20250714_091530.pkl
2025-07-14 09:15:30,574 - INFO -   analysis_results: models/ml_optimized/analysis_results_20250714_091530.json
2025-07-14 09:15:30,574 - INFO -   interpretability_report: models/ml_optimized/interpretability_report_20250714_091530.txt
