{"timestamp": "20250714_081458", "data_shape": [350880, 127], "selected_features": ["WindSpeed_U_金堂", "HourlyMinLoad", "SO2_甘孜", "Precipitation_锦江", "Pressure_金堂", "Pressure_都江堰", "Humidity_甘孜", "Temp_Squared", "WindSpeed_V_金堂", "Precipitation_金堂", "Humidity_都江堰", "Temperature_简阳", "DailyMaxLoad", "WindSpeed_V_锦江", "PM10_甘孜", "Weekend_Hour_Interaction", "Temperature_甘孜", "HourlyActiveEnergy", "HourlyReactiveEnergy", "Humidity_简阳", "Hour_Month_Interaction", "AQI_甘孜", "Pressure_甘孜", "Heating_Degree_Days", "CO_甘孜", "Hour", "Precipitation_甘孜", "HourlyMaxLoad", "NO2_甘孜", "O3_甘孜", "PM2_5_甘孜", "HourlyLoadFactor", "Pressure_简阳", "WindSpeed_U_简阳", "WindSpeed_V_甘孜", "WindSpeed_U_都江堰", "Precipitation_都江堰", "Humidity_金堂", "Temperature_金堂", "WindSpeed_U_甘孜"], "feature_count": 40, "xgboost_performance": {"best_params": {"colsample_bytree": 0.9, "learning_rate": 0.05, "max_depth": 4, "n_estimators": 200, "reg_alpha": 0.1, "reg_lambda": 1.5, "subsample": 0.8}, "val_r2": 0.07425415911423605, "test_r2": 0.07426315681780316, "test_rmse": 0.03229854537285931}, "dl_performance": {"val_r2": 0.06845621473686614, "test_r2": 0.06663765773068209, "test_rmse": 0.032431297686215554}}