机器学习优化空调负荷识别模型可解释性报告
============================================================
生成时间: 2025-07-14 09:15:30

1. 数据概况
------------------------------
总样本数: 350,880
有效样本数: 350,880
特征数量: 50
非零空调负荷比例: 100.00%
平均空调负荷: 9.4705 kW
空调负荷标准差: 32.2720 kW

2. 模型性能对比
------------------------------
XGBoost模型:
  测试集 R²: -1.2211
  测试集 RMSE: 71.6076
  测试集 MAE: 53.1549

深度学习模型:
  测试集 R²: -63.1683
  测试集 RMSE: 384.8901
  测试集 MAE: 322.9851

性能差异 (XGBoost - 深度学习):
  R²差异: +61.9473
  → XGBoost表现更好

3. 特征重要性分析
------------------------------
前15个最重要特征:
   1. SO2_甘孜: 0.3036
   2. Month_cos: 0.1931
   3. Month: 0.1042
   4. Humidity_甘孜: 0.0989
   5. Humidity_squared: 0.0511
   6. HourlyActiveEnergy: 0.0471
   7. Hour: 0.0403
   8. HourlyMaxLoad: 0.0355
   9. Precipitation_甘孜: 0.0320
  10. Temp_squared: 0.0188
  11. Month_sin: 0.0182
  12. Temp_Month_interaction: 0.0112
  13. Hour_cos: 0.0105
  14. Hour_sin: 0.0099
  15. Temperature_甘孜: 0.0056

4. 可解释性总结
------------------------------
模型决策主要基于以下因素:
• 温度相关特征：直接影响空调使用需求
• 时间特征：反映用户使用习惯和季节性模式
• 交互特征：捕捉复杂的非线性关系
• 衍生特征：增强模型对物理规律的理解

模型优势:
• 基于物理驱动的特征工程
• 多层次的特征选择策略
• 考虑了时间序列特性
• 具有良好的可解释性
