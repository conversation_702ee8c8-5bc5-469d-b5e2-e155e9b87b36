#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速数据探索脚本
空调负荷柔性调控能力分析系统
"""

import pandas as pd
import numpy as np
import os
import glob
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def explore_load_data():
    """探索负荷数据"""
    print("=" * 60)
    print("负荷数据探索")
    print("=" * 60)
    
    load_file = "complete_load_analysis_data.csv"
    
    if not os.path.exists(load_file):
        print(f"负荷数据文件不存在: {load_file}")
        return
    
    print(f"文件大小: {os.path.getsize(load_file) / (1024*1024):.2f} MB")
    
    # 读取前几行了解结构
    try:
        df_sample = pd.read_csv(load_file, nrows=10)
        print(f"\n数据形状: {df_sample.shape}")
        print(f"列名: {list(df_sample.columns)}")
        print("\n前5行数据:")
        print(df_sample.head())
        
        # 数据类型
        print("\n数据类型:")
        print(df_sample.dtypes)
        
        # 读取更多行进行统计
        df_stats = pd.read_csv(load_file, nrows=1000)
        print(f"\n统计样本形状: {df_stats.shape}")
        
        # 缺失值统计
        missing_data = df_stats.isnull().sum()
        missing_percent = (missing_data / len(df_stats)) * 100
        print("\n缺失值统计:")
        missing_df = pd.DataFrame({
            '缺失值数量': missing_data,
            '缺失值百分比': missing_percent
        })
        print(missing_df[missing_df['缺失值数量'] > 0])
        
        # 数值列统计
        numeric_cols = df_stats.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            print(f"\n数值列统计 (前5列):")
            print(df_stats[numeric_cols[:5]].describe())
        
        # 分类列统计
        categorical_cols = df_stats.select_dtypes(include=['object']).columns
        if len(categorical_cols) > 0:
            print(f"\n分类列统计:")
            for col in categorical_cols[:3]:  # 只显示前3个
                print(f"\n{col}:")
                print(df_stats[col].value_counts().head(5))
        
    except Exception as e:
        print(f"读取负荷数据时出错: {e}")

def explore_weather_data():
    """探索天气数据"""
    print("\n" + "=" * 60)
    print("天气数据探索")
    print("=" * 60)
    
    weather_dir = "天气数据"
    
    if not os.path.exists(weather_dir):
        print(f"天气数据目录不存在: {weather_dir}")
        return
    
    # 获取所有天气数据文件
    weather_files = glob.glob(os.path.join(weather_dir, "*.xls"))
    weather_files.extend(glob.glob(os.path.join(weather_dir, "*.xlsx")))
    
    print(f"找到 {len(weather_files)} 个天气数据文件")
    
    # 分析文件命名规律
    file_info = []
    for file in weather_files:
        filename = os.path.basename(file)
        file_size = os.path.getsize(file) / (1024*1024)
        
        if "城市24小时" in filename:
            file_type = "城市24小时数据"
            region = "多城市"
            year = "2023-2025"
        else:
            parts = filename.replace(".xls", "").replace(".xlsx", "").split()
            if len(parts) >= 2:
                region = parts[0]
                year = parts[1]
                file_type = "区域天气数据"
            else:
                region = "未知"
                year = "未知"
                file_type = "其他"
        
        file_info.append({
            'filename': filename,
            'file_type': file_type,
            'region': region,
            'year': year,
            'size_mb': file_size
        })
    
    # 统计信息
    info_df = pd.DataFrame(file_info)
    print("\n按类型统计:")
    print(info_df['file_type'].value_counts())
    
    print("\n按区域统计:")
    print(info_df['region'].value_counts().head(10))
    
    print("\n按年份统计:")
    print(info_df['year'].value_counts())
    
    # 读取几个示例文件
    print("\n示例文件内容:")
    sample_files = weather_files[:2]  # 取前2个文件
    
    for i, file in enumerate(sample_files):
        print(f"\n文件 {i+1}: {os.path.basename(file)}")
        try:
            if file.endswith('.xlsx'):
                df = pd.read_excel(file, engine='openpyxl', nrows=5)
            else:
                df = pd.read_excel(file, engine='xlrd', nrows=5)
            
            print(f"  形状: {df.shape}")
            print(f"  列名: {list(df.columns)}")
            print("  前3行:")
            print(df.head(3))
            
        except Exception as e:
            print(f"  读取失败: {e}")

def main():
    """主函数"""
    print("空调负荷柔性调控能力分析系统 - 快速数据探索")
    print("空调负荷柔性调控能力分析系统")
    print("=" * 60)
    
    # 探索负荷数据
    explore_load_data()
    
    # 探索天气数据
    explore_weather_data()
    
    print("\n" + "=" * 60)
    print("快速探索完成！")
    print("建议接下来:")
    print("1. 打开 notebooks/01_数据探索与整理.ipynb 进行详细分析")
    print("2. 根据探索结果制定数据预处理策略")
    print("3. 开始数据清洗和标准化工作")
    print("=" * 60)

if __name__ == "__main__":
    main() 