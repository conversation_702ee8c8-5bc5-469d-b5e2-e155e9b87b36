#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空调负荷分离逻辑可视化脚本

展示当前空调负荷分离的核心逻辑和效果

作者: AI Assistant
创建时间: 2025-07-14
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def calculate_ac_load_ratio(temperature, month):
    """
    计算空调负荷比例（复现训练脚本中的逻辑）
    
    Args:
        temperature: 温度值
        month: 月份
    
    Returns:
        空调负荷比例 (0-1)
    """
    ac_ratio = 0.0
    
    # 制冷需求（夏季）
    if temperature > 26 and month in [6, 7, 8, 9]:
        ac_ratio = min((temperature - 26) / 12 * 0.8, 0.85)
    
    # 制热需求（冬季）
    elif temperature < 16 and month in [12, 1, 2, 3]:
        ac_ratio = min((16 - temperature) / 12 * 0.7, 0.75)
    
    # 过渡季节的轻微调节
    elif ((temperature > 24 and temperature <= 26) or 
          (temperature >= 16 and temperature < 18)):
        ac_ratio = 0.1
    
    return ac_ratio

def create_temperature_response_curves():
    """创建温度-空调负荷响应曲线"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('空调负荷分离逻辑 - 温度响应曲线分析', fontsize=16, fontweight='bold')
    
    # 温度范围
    temperatures = np.arange(-5, 45, 0.5)
    
    # 1. 夏季响应曲线（7月）
    ax1 = axes[0, 0]
    summer_ratios = [calculate_ac_load_ratio(t, 7) for t in temperatures]
    ax1.plot(temperatures, summer_ratios, 'r-', linewidth=3, label='夏季制冷')
    ax1.axvline(x=26, color='orange', linestyle='--', alpha=0.7, label='制冷启动温度')
    ax1.axhline(y=0.85, color='red', linestyle='--', alpha=0.7, label='最大制冷比例')
    ax1.set_xlabel('温度 (°C)')
    ax1.set_ylabel('空调负荷比例')
    ax1.set_title('夏季制冷响应曲线 (7月)')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    ax1.set_ylim(0, 1)
    
    # 2. 冬季响应曲线（1月）
    ax2 = axes[0, 1]
    winter_ratios = [calculate_ac_load_ratio(t, 1) for t in temperatures]
    ax2.plot(temperatures, winter_ratios, 'b-', linewidth=3, label='冬季制热')
    ax2.axvline(x=16, color='cyan', linestyle='--', alpha=0.7, label='制热启动温度')
    ax2.axhline(y=0.75, color='blue', linestyle='--', alpha=0.7, label='最大制热比例')
    ax2.set_xlabel('温度 (°C)')
    ax2.set_ylabel('空调负荷比例')
    ax2.set_title('冬季制热响应曲线 (1月)')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    ax2.set_ylim(0, 1)
    
    # 3. 过渡季节响应曲线（4月）
    ax3 = axes[1, 0]
    spring_ratios = [calculate_ac_load_ratio(t, 4) for t in temperatures]
    ax3.plot(temperatures, spring_ratios, 'g-', linewidth=3, label='过渡季节')
    ax3.axvspan(16, 18, alpha=0.2, color='green', label='轻微制热区')
    ax3.axvspan(24, 26, alpha=0.2, color='orange', label='轻微制冷区')
    ax3.set_xlabel('温度 (°C)')
    ax3.set_ylabel('空调负荷比例')
    ax3.set_title('过渡季节响应曲线 (4月)')
    ax3.grid(True, alpha=0.3)
    ax3.legend()
    ax3.set_ylim(0, 0.2)
    
    # 4. 全年综合响应曲线
    ax4 = axes[1, 1]
    months = [1, 4, 7, 10]  # 代表四季
    month_names = ['冬季(1月)', '春季(4月)', '夏季(7月)', '秋季(10月)']
    colors = ['blue', 'green', 'red', 'orange']
    
    for month, name, color in zip(months, month_names, colors):
        ratios = [calculate_ac_load_ratio(t, month) for t in temperatures]
        ax4.plot(temperatures, ratios, color=color, linewidth=2, label=name)
    
    ax4.set_xlabel('温度 (°C)')
    ax4.set_ylabel('空调负荷比例')
    ax4.set_title('四季空调负荷响应对比')
    ax4.grid(True, alpha=0.3)
    ax4.legend()
    ax4.set_ylim(0, 1)
    
    plt.tight_layout()
    
    # 保存图表
    Path('figures/ac_logic_analysis').mkdir(parents=True, exist_ok=True)
    plt.savefig('figures/ac_logic_analysis/temperature_response_curves.png', 
                dpi=300, bbox_inches='tight')
    print("温度响应曲线图已保存至: figures/ac_logic_analysis/temperature_response_curves.png")
    plt.close()

def create_seasonal_pattern_analysis():
    """创建季节性模式分析"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('空调负荷分离逻辑 - 季节性模式分析', fontsize=16, fontweight='bold')
    
    # 1. 月度空调负荷潜力分析
    ax1 = axes[0, 0]
    months = range(1, 13)
    month_names = ['1月', '2月', '3月', '4月', '5月', '6月', 
                   '7月', '8月', '9月', '10月', '11月', '12月']
    
    # 假设典型温度
    typical_temps = [5, 8, 15, 20, 25, 30, 32, 31, 28, 22, 15, 8]
    monthly_ratios = [calculate_ac_load_ratio(temp, month) 
                     for temp, month in zip(typical_temps, months)]
    
    bars = ax1.bar(months, monthly_ratios, color=['blue' if m in [12,1,2,3] else 
                                                 'green' if m in [4,5,10,11] else 'red' 
                                                 for m in months])
    ax1.set_xlabel('月份')
    ax1.set_ylabel('典型空调负荷比例')
    ax1.set_title('月度空调负荷潜力分析')
    ax1.set_xticks(months)
    ax1.set_xticklabels([f'{m}月' for m in months], rotation=45)
    ax1.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, ratio in zip(bars, monthly_ratios):
        if ratio > 0:
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{ratio:.2f}', ha='center', va='bottom')
    
    # 2. 温度分布与空调负荷关系
    ax2 = axes[0, 1]
    
    # 模拟一年的温度分布
    np.random.seed(42)
    summer_temps = np.random.normal(30, 4, 1000)
    winter_temps = np.random.normal(8, 3, 1000)
    spring_temps = np.random.normal(18, 3, 500)
    autumn_temps = np.random.normal(20, 3, 500)
    
    all_temps = np.concatenate([summer_temps, winter_temps, spring_temps, autumn_temps])
    all_months = np.concatenate([
        np.full(1000, 7),  # 夏季
        np.full(1000, 1),  # 冬季
        np.full(500, 4),   # 春季
        np.full(500, 10)   # 秋季
    ])
    
    ac_ratios = [calculate_ac_load_ratio(temp, month) 
                for temp, month in zip(all_temps, all_months)]
    
    ax2.scatter(all_temps, ac_ratios, alpha=0.5, s=1)
    ax2.set_xlabel('温度 (°C)')
    ax2.set_ylabel('空调负荷比例')
    ax2.set_title('温度分布与空调负荷关系')
    ax2.grid(True, alpha=0.3)
    
    # 3. 日内空调负荷模式（简化版）
    ax3 = axes[1, 0]
    hours = range(24)
    
    # 夏季典型日（30°C）
    summer_hourly = [calculate_ac_load_ratio(30, 7) for _ in hours]
    # 冬季典型日（5°C）
    winter_hourly = [calculate_ac_load_ratio(5, 1) for _ in hours]
    
    ax3.plot(hours, summer_hourly, 'r-', linewidth=3, label='夏季典型日 (30°C)')
    ax3.plot(hours, winter_hourly, 'b-', linewidth=3, label='冬季典型日 (5°C)')
    ax3.set_xlabel('小时')
    ax3.set_ylabel('空调负荷比例')
    ax3.set_title('日内空调负荷模式')
    ax3.set_xticks(range(0, 24, 4))
    ax3.grid(True, alpha=0.3)
    ax3.legend()
    
    # 4. 空调负荷比例分布统计
    ax4 = axes[1, 1]

    # 统计不同比例区间的分布
    ratio_bins = [0, 0.1, 0.2, 0.4, 0.6, 0.8, 1.0]
    ratio_labels = ['0%', '0-10%', '10-20%', '20-40%', '40-60%', '60-80%', '80%+']

    ratio_counts = []
    for i in range(len(ratio_bins)-1):
        count = sum(1 for r in ac_ratios if ratio_bins[i] <= r < ratio_bins[i+1])
        ratio_counts.append(count)

    # 添加最后一个区间
    ratio_counts.append(sum(1 for r in ac_ratios if r >= ratio_bins[-1]))

    # 只保留非零的计数和对应的标签
    non_zero_counts = []
    non_zero_labels = []
    for count, label in zip(ratio_counts, ratio_labels[1:]):
        if count > 0:
            non_zero_counts.append(count)
            non_zero_labels.append(label)

    if non_zero_counts:
        ax4.pie(non_zero_counts, labels=non_zero_labels,
                autopct='%1.1f%%', startangle=90)
    else:
        ax4.text(0.5, 0.5, '无数据', ha='center', va='center', transform=ax4.transAxes)

    ax4.set_title('空调负荷比例分布统计')
    
    plt.tight_layout()
    
    # 保存图表
    plt.savefig('figures/ac_logic_analysis/seasonal_pattern_analysis.png', 
                dpi=300, bbox_inches='tight')
    print("季节性模式分析图已保存至: figures/ac_logic_analysis/seasonal_pattern_analysis.png")
    plt.close()

def analyze_actual_data():
    """分析实际数据中的空调负荷分离效果"""
    
    try:
        # 查找最新的大宽表文件
        processed_dir = Path('data/processed')
        wide_table_files = list(processed_dir.glob('final_wide_table_*.csv'))
        
        if not wide_table_files:
            print("未找到大宽表文件，跳过实际数据分析")
            return
        
        file_path = max(wide_table_files, key=lambda x: x.stat().st_mtime)
        print(f"分析文件: {file_path}")
        
        # 读取部分数据进行分析
        df = pd.read_csv(file_path, nrows=50000)
        
        # 找到温度列
        temp_cols = [col for col in df.columns if 'Temperature' in col and df[col].notna().sum() > 1000]
        
        if not temp_cols:
            print("未找到有效的温度数据，跳过实际数据分析")
            return
        
        temp_col = temp_cols[0]
        print(f"使用温度列: {temp_col}")
        
        # 复现空调负荷计算逻辑
        df['ac_load_ratio'] = 0.0
        
        # 制冷需求
        cooling_mask = (df[temp_col] > 26) & (df['Month'].isin([6, 7, 8, 9]))
        df.loc[cooling_mask, 'ac_load_ratio'] = np.minimum(
            (df.loc[cooling_mask, temp_col] - 26) / 12 * 0.8, 0.85
        )
        
        # 制热需求
        heating_mask = (df[temp_col] < 16) & (df['Month'].isin([12, 1, 2, 3]))
        df.loc[heating_mask, 'ac_load_ratio'] = np.minimum(
            (16 - df.loc[heating_mask, temp_col]) / 12 * 0.7, 0.75
        )
        
        # 过渡季节
        transition_mask = (~cooling_mask) & (~heating_mask) & (
            ((df[temp_col] > 24) & (df[temp_col] <= 26)) |
            ((df[temp_col] >= 16) & (df[temp_col] < 18))
        )
        df.loc[transition_mask, 'ac_load_ratio'] = 0.1
        
        # 计算空调负荷
        if 'HourlyAvgLoad' in df.columns:
            df['ac_load'] = df['HourlyAvgLoad'] * df['ac_load_ratio']
        else:
            print("未找到负荷数据列")
            return
        
        # 创建实际数据分析图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('实际数据中的空调负荷分离效果分析', fontsize=16, fontweight='bold')
        
        # 1. 温度vs空调负荷比例散点图
        ax1 = axes[0, 0]
        valid_data = df[(df[temp_col].notna()) & (df['ac_load_ratio'] > 0)]
        ax1.scatter(valid_data[temp_col], valid_data['ac_load_ratio'], 
                   alpha=0.5, s=1, c=valid_data['Month'], cmap='viridis')
        ax1.set_xlabel('温度 (°C)')
        ax1.set_ylabel('空调负荷比例')
        ax1.set_title('实际数据：温度vs空调负荷比例')
        ax1.grid(True, alpha=0.3)
        
        # 2. 月度空调负荷统计
        ax2 = axes[0, 1]
        monthly_stats = df.groupby('Month')['ac_load_ratio'].agg(['mean', 'count'])
        months = monthly_stats.index
        ax2.bar(months, monthly_stats['mean'], alpha=0.7)
        ax2.set_xlabel('月份')
        ax2.set_ylabel('平均空调负荷比例')
        ax2.set_title('月度平均空调负荷比例')
        ax2.set_xticks(months)
        ax2.grid(True, alpha=0.3)
        
        # 3. 小时级空调负荷模式
        ax3 = axes[1, 0]
        hourly_stats = df.groupby('Hour')['ac_load_ratio'].mean()
        ax3.plot(hourly_stats.index, hourly_stats.values, 'o-', linewidth=2)
        ax3.set_xlabel('小时')
        ax3.set_ylabel('平均空调负荷比例')
        ax3.set_title('日内空调负荷模式')
        ax3.set_xticks(range(0, 24, 4))
        ax3.grid(True, alpha=0.3)
        
        # 4. 空调负荷分布直方图
        ax4 = axes[1, 1]
        ax4.hist(df['ac_load'][df['ac_load'] > 0], bins=50, alpha=0.7, edgecolor='black')
        ax4.set_xlabel('空调负荷 (kW)')
        ax4.set_ylabel('频次')
        ax4.set_title('空调负荷分布直方图')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        plt.savefig('figures/ac_logic_analysis/actual_data_analysis.png', 
                    dpi=300, bbox_inches='tight')
        print("实际数据分析图已保存至: figures/ac_logic_analysis/actual_data_analysis.png")
        plt.close()
        
        # 打印统计信息
        print(f"\n实际数据分析结果:")
        print(f"数据样本数: {len(df):,}")
        print(f"非零空调负荷比例: {(df['ac_load'] > 0).mean():.2%}")
        print(f"平均空调负荷: {df['ac_load'].mean():.2f} kW")
        print(f"最大空调负荷: {df['ac_load'].max():.2f} kW")
        print(f"温度范围: {df[temp_col].min():.1f}°C - {df[temp_col].max():.1f}°C")
        
    except Exception as e:
        print(f"实际数据分析失败: {e}")

def main():
    """主函数"""
    print("空调负荷分离逻辑可视化分析")
    print("=" * 50)
    
    # 1. 创建温度响应曲线
    print("1. 生成温度响应曲线...")
    create_temperature_response_curves()
    
    # 2. 创建季节性模式分析
    print("2. 生成季节性模式分析...")
    create_seasonal_pattern_analysis()
    
    # 3. 分析实际数据
    print("3. 分析实际数据...")
    analyze_actual_data()
    
    print("\n" + "=" * 50)
    print("空调负荷分离逻辑可视化分析完成！")
    print("查看结果:")
    print("- 温度响应曲线: figures/ac_logic_analysis/temperature_response_curves.png")
    print("- 季节性模式分析: figures/ac_logic_analysis/seasonal_pattern_analysis.png")
    print("- 实际数据分析: figures/ac_logic_analysis/actual_data_analysis.png")

if __name__ == "__main__":
    main()
