# 空调负荷分离逻辑详细分析

## 📋 概述

当前的空调负荷分离逻辑基于**温度驱动的比例分配方法**，通过分析环境温度与空调使用需求的关系，从总负荷中估算出空调负荷部分。

## 🔍 核心分离逻辑

### 1. 基本原理

```
空调负荷 = 总负荷 × 空调负荷比例
```

其中，空调负荷比例根据以下因素动态计算：
- **环境温度**（主要驱动因子）
- **季节性**（月份）
- **时间性**（小时）
- **随机噪声**（模拟真实波动）

### 2. 温度驱动的分离算法

#### 2.1 制冷需求（夏季）
```python
# 条件：温度 > 26°C 且 月份 ∈ [6,7,8,9]
cooling_mask = (temperature > 26) & (month in [6,7,8,9])

# 计算公式
ac_load_ratio = min((temperature - 26) / 12 * 0.8, 0.85)
```

**逻辑解释**：
- **启动温度**：26°C（舒适温度上限）
- **线性增长**：温度每升高1°C，空调负荷比例增加约6.7%
- **最大比例**：85%（考虑其他负荷如照明、设备等）
- **饱和温度**：38°C（26 + 12）时达到最大比例

#### 2.2 制热需求（冬季）
```python
# 条件：温度 < 16°C 且 月份 ∈ [12,1,2,3]
heating_mask = (temperature < 16) & (month in [12,1,2,3])

# 计算公式
ac_load_ratio = min((16 - temperature) / 12 * 0.7, 0.75)
```

**逻辑解释**：
- **启动温度**：16°C（舒适温度下限）
- **线性增长**：温度每降低1°C，空调负荷比例增加约5.8%
- **最大比例**：75%（制热效率通常低于制冷）
- **饱和温度**：4°C（16 - 12）时达到最大比例

#### 2.3 过渡季节调节
```python
# 条件：轻微不适温度范围
transition_mask = (
    ((temperature > 24) & (temperature <= 26)) |  # 轻微偏热
    ((temperature >= 16) & (temperature < 18))    # 轻微偏冷
)

# 固定比例
ac_load_ratio = 0.1  # 10%的轻微调节
```

### 3. 备用分离逻辑（无温度数据时）

当缺少温度数据时，使用基于时间模式的简化估算：

```python
# 夏季高峰（7-8月，14-18点）
summer_peak: ac_load_ratio = 0.6

# 夏季一般（6,9月，10-20点）
summer_normal: ac_load_ratio = 0.4

# 冬季高峰（1,12月，8-10点和18-22点）
winter_peak: ac_load_ratio = 0.5
```

### 4. 噪声处理

```python
# 添加3%标准差的高斯噪声
noise = np.random.normal(0, 0.03, len(data))
ac_load_ratio = np.clip(ac_load_ratio + noise, 0, 1)
```

**目的**：模拟真实世界中的随机波动和用户行为差异。

## 📊 分离效果分析

### 当前训练结果
- **非零空调负荷比例**：51.18%
- **平均空调负荷**：2.98 kW
- **标准差**：15.55 kW

### 温度-负荷关系曲线

```
空调负荷比例 (%)
    85% ┤                    ████████
        │                ████        
    70% ┤            ████            
        │        ████                
    50% ┤    ████                    
        │████                        
    10% ┤                            ████
        │                        ████    
     0% └────┬────┬────┬────┬────┬────┬────
           4°C  16°C  24°C  26°C  38°C
           
         制热   过渡   舒适   制冷
```

## 🎯 逻辑优势

### 1. 物理合理性
- **基于热力学原理**：温度是空调使用的主要驱动因子
- **符合用户行为**：舒适温度范围外才大量使用空调
- **季节性考虑**：不同季节的空调使用模式差异

### 2. 数据驱动
- **实际温度数据**：使用真实的气象观测数据
- **地域特异性**：不同区域使用对应的温度数据
- **时间精度**：小时级的精细化分析

### 3. 工程实用性
- **计算简单**：线性关系便于理解和调试
- **参数可调**：阈值温度和比例系数可根据实际情况调整
- **鲁棒性好**：有备用逻辑处理数据缺失情况

## ⚠️ 当前局限性

### 1. 简化假设
- **线性关系**：实际温度-负荷关系可能非线性
- **固定阈值**：26°C和16°C可能因地区而异
- **忽略湿度**：湿度对空调使用也有重要影响

### 2. 缺少验证
- **无真实标签**：缺乏实际空调负荷数据进行验证
- **用户行为**：未考虑个体用户的使用习惯差异
- **建筑特性**：未考虑建筑保温性能等因素

### 3. 模型限制
- **静态参数**：参数固定，无法自适应学习
- **单一特征**：主要依赖温度，特征相对单一

## 🚀 改进建议

### 1. 短期改进
- **参数优化**：通过历史数据统计优化阈值温度
- **湿度集成**：加入湿度指数（如体感温度）
- **区域差异**：不同气候区域使用不同参数

### 2. 中期改进
- **非线性建模**：使用sigmoid或分段函数
- **多特征融合**：结合湿度、风速、日照等因素
- **用户分类**：基于历史用电模式进行用户分群

### 3. 长期改进
- **深度学习**：使用神经网络学习复杂的非线性关系
- **时序建模**：考虑空调负荷的时间依赖性
- **强化学习**：基于用户反馈优化分离策略

## 📈 验证方法建议

### 1. 间接验证
- **季节性检验**：夏冬季空调负荷应显著高于春秋季
- **时段性检验**：白天空调负荷应高于夜间
- **温度相关性**：空调负荷与温度应呈现预期的相关性

### 2. 对比验证
- **文献对比**：与已发表的空调负荷比例研究对比
- **行业标准**：参考电力行业的空调负荷估算标准
- **专家评估**：邀请领域专家评估分离结果的合理性

### 3. 实测验证（理想情况）
- **智能电表**：如有条件，使用智能电表的分项计量数据
- **用户调研**：通过问卷调查了解实际空调使用情况
- **实验验证**：在小范围内进行实测对比

---

**总结**：当前的空调负荷分离逻辑基于温度驱动的物理模型，具有良好的理论基础和工程实用性，但仍有改进空间。通过逐步优化和验证，可以进一步提高分离精度和可靠性。
