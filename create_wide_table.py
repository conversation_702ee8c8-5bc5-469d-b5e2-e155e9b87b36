#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空调负荷柔性调控能力分析系统 - 数据探索分析与大宽表生成脚本

本脚本实现以下功能：
1. 数据加载与探索分析
2. 数据预处理与清洗
3. 特征工程
4. 负荷与天气数据合并
5. 生成建模用的大宽表
6. 数据质量报告生成

作者: AI Assistant
创建时间: 2025-07-13
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
import logging
import os
import glob
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union
import json

# 设置中文字体和警告过滤
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/data_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ACLoadDataAnalyzer:
    """空调负荷数据分析器"""

    def __init__(self, config_path: Optional[str] = None):
        """
        初始化数据分析器

        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.load_data = None
        self.weather_data = {}
        self.merged_data = None

        # 创建必要的目录
        self._create_directories()

        logger.info("空调负荷数据分析器初始化完成")

    def _load_config(self, config_path: Optional[str]) -> Dict:
        """加载配置文件"""
        default_config = {
            'data_paths': {
                'load_data_file': 'complete_load_analysis_data.csv',
                'weather_data_dir': '天气数据',
                'output_dir': 'data/processed',
                'interim_dir': 'data/interim',
                'results_dir': 'results'
            },
            'processing': {
                'chunk_size': 50000,
                'missing_threshold': 0.3,
                'outlier_method': 'iqr',
                'outlier_factor': 1.5
            },
            'features': {
                'base_temperature': 18.0,
                'cooling_threshold': 25.0,
                'heating_threshold': 15.0,
                'lag_features': [1, 2, 3, 6, 12, 24],
                'rolling_windows': [3, 6, 12, 24]
            },
            'visualization': {
                'figure_size': (12, 8),
                'dpi': 300,
                'style': 'seaborn-v0_8'
            }
        }

        if config_path and os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                default_config.update(user_config)

        return default_config

    def _create_directories(self):
        """创建必要的目录"""
        dirs = [
            self.config['data_paths']['output_dir'],
            self.config['data_paths']['interim_dir'],
            self.config['data_paths']['results_dir'],
            'logs',
            'figures'
        ]

        for dir_path in dirs:
            Path(dir_path).mkdir(parents=True, exist_ok=True)

    def load_load_data(self, sample_size: Optional[int] = None) -> pd.DataFrame:
        """
        加载负荷数据

        Args:
            sample_size: 采样大小，None表示加载全部数据

        Returns:
            负荷数据DataFrame
        """
        logger.info("开始加载负荷数据...")

        file_path = self.config['data_paths']['load_data_file']
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"负荷数据文件不存在: {file_path}")

        try:
            # 获取文件大小
            file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
            logger.info(f"负荷数据文件大小: {file_size:.2f} MB")

            # 根据文件大小决定加载策略
            if file_size > 500:  # 大于500MB使用分块加载
                chunk_size = self.config['processing']['chunk_size']
                chunks = []

                for i, chunk in enumerate(pd.read_csv(file_path, chunksize=chunk_size)):
                    chunks.append(chunk)
                    if sample_size and len(chunks) * chunk_size >= sample_size:
                        break
                    if i % 10 == 0:
                        logger.info(f"已加载 {(i+1) * chunk_size} 行数据...")

                self.load_data = pd.concat(chunks, ignore_index=True)
            else:
                # 小文件直接加载
                self.load_data = pd.read_csv(file_path)
                if sample_size:
                    self.load_data = self.load_data.sample(n=min(sample_size, len(self.load_data)))

            logger.info(f"负荷数据加载完成，形状: {self.load_data.shape}")
            return self.load_data

        except Exception as e:
            logger.error(f"加载负荷数据失败: {e}")
            raise

    def load_weather_data(self, regions: Optional[List[str]] = None,
                         years: Optional[List[int]] = None) -> Dict[str, pd.DataFrame]:
        """
        加载天气数据

        Args:
            regions: 指定加载的区域列表，None表示加载所有区域
            years: 指定加载的年份列表，None表示加载所有年份

        Returns:
            天气数据字典，键为"区域_年份"格式
        """
        logger.info("开始加载天气数据...")

        weather_dir = self.config['data_paths']['weather_data_dir']
        if not os.path.exists(weather_dir):
            raise FileNotFoundError(f"天气数据目录不存在: {weather_dir}")

        # 获取所有天气数据文件
        weather_files = []
        weather_files.extend(glob.glob(os.path.join(weather_dir, "*.xls")))
        weather_files.extend(glob.glob(os.path.join(weather_dir, "*.xlsx")))

        logger.info(f"找到 {len(weather_files)} 个天气数据文件")

        weather_data = {}

        for file_path in weather_files:
            file_name = Path(file_path).stem

            try:
                # 解析文件名
                if "城市24小时" in file_name:
                    # 处理城市24小时数据
                    if "2023" in file_name:
                        region, year = "城市24小时", 2023
                    elif "24-25" in file_name:
                        region, year = "城市24小时", 2024  # 主要年份
                    else:
                        continue
                else:
                    # 解析区域天气数据文件名
                    parts = file_name.replace('.xls', '').replace('.xlsx', '')
                    if len(parts) >= 2:
                        # 提取区域名（去掉年份）
                        region = ''.join([c for c in parts if not c.isdigit()])
                        # 提取年份
                        year_str = ''.join([c for c in parts if c.isdigit()])
                        if year_str:
                            year = 2000 + int(year_str) if len(year_str) == 2 else int(year_str)
                        else:
                            continue
                    else:
                        continue

                # 过滤条件
                if regions and region not in regions:
                    continue
                if years and year not in years:
                    continue

                # 读取Excel文件
                try:
                    if file_path.endswith('.xlsx'):
                        df = pd.read_excel(file_path, engine='openpyxl')
                    else:
                        df = pd.read_excel(file_path, engine='xlrd')
                except:
                    # 尝试其他引擎
                    df = pd.read_excel(file_path)

                # 标准化列名
                df.columns = [str(col).strip() for col in df.columns]

                # 添加元数据
                df['region'] = region
                df['year'] = year
                df['data_source'] = file_name

                # 存储数据
                key = f"{region}_{year}"
                weather_data[key] = df

                logger.info(f"加载天气数据 {key}: {df.shape}")

            except Exception as e:
                logger.warning(f"加载天气文件 {file_path} 失败: {e}")
                continue

        self.weather_data = weather_data
        logger.info(f"天气数据加载完成，共加载 {len(weather_data)} 个数据集")
        return weather_data

    def explore_load_data(self) -> Dict:
        """
        探索负荷数据

        Returns:
            探索结果字典
        """
        if self.load_data is None:
            raise ValueError("请先加载负荷数据")

        logger.info("开始探索负荷数据...")

        df = self.load_data
        exploration_results = {}

        # 基本信息
        exploration_results['basic_info'] = {
            'shape': df.shape,
            'columns': df.columns.tolist(),
            'dtypes': df.dtypes.to_dict(),
            'memory_usage': df.memory_usage(deep=True).sum() / (1024**2)  # MB
        }

        # 缺失值分析
        missing_values = df.isnull().sum()
        missing_percent = (missing_values / len(df)) * 100
        exploration_results['missing_values'] = {
            'missing_counts': missing_values.to_dict(),
            'missing_percentages': missing_percent.to_dict(),
            'columns_with_missing': missing_values[missing_values > 0].index.tolist()
        }

        # 数值列统计
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            exploration_results['numeric_stats'] = df[numeric_cols].describe().to_dict()

        # 分类列统计
        categorical_cols = df.select_dtypes(include=['object']).columns
        exploration_results['categorical_stats'] = {}
        for col in categorical_cols:
            if col in df.columns:
                value_counts = df[col].value_counts()
                exploration_results['categorical_stats'][col] = {
                    'unique_count': df[col].nunique(),
                    'top_values': value_counts.head(10).to_dict()
                }

        # 时间列分析
        if 'Timestamp' in df.columns:
            try:
                df['Timestamp'] = pd.to_datetime(df['Timestamp'])
                exploration_results['time_analysis'] = {
                    'time_range': {
                        'start': df['Timestamp'].min().strftime('%Y-%m-%d %H:%M:%S'),
                        'end': df['Timestamp'].max().strftime('%Y-%m-%d %H:%M:%S')
                    },
                    'time_span_days': (df['Timestamp'].max() - df['Timestamp'].min()).days,
                    'frequency': 'hourly'  # 基于数据结构推断
                }
            except:
                logger.warning("时间列解析失败")

        logger.info("负荷数据探索完成")
        return exploration_results

    def explore_weather_data(self) -> Dict:
        """
        探索天气数据

        Returns:
            探索结果字典
        """
        if not self.weather_data:
            raise ValueError("请先加载天气数据")

        logger.info("开始探索天气数据...")

        exploration_results = {
            'datasets_count': len(self.weather_data),
            'datasets_info': {},
            'regions': set(),
            'years': set(),
            'common_columns': None,
            'data_quality': {}
        }

        all_columns = []

        for key, df in self.weather_data.items():
            region, year = key.split('_')
            exploration_results['regions'].add(region)
            exploration_results['years'].add(int(year))

            # 数据集基本信息
            exploration_results['datasets_info'][key] = {
                'shape': df.shape,
                'columns': df.columns.tolist(),
                'memory_usage': df.memory_usage(deep=True).sum() / (1024**2),  # MB
                'missing_values': df.isnull().sum().sum(),
                'missing_percentage': (df.isnull().sum().sum() / df.size) * 100
            }

            all_columns.append(set(df.columns))

        # 找出所有数据集的公共列
        if all_columns:
            exploration_results['common_columns'] = list(set.intersection(*all_columns))

        # 转换集合为列表以便JSON序列化
        exploration_results['regions'] = sorted(list(exploration_results['regions']))
        exploration_results['years'] = sorted(list(exploration_results['years']))

        logger.info("天气数据探索完成")
        return exploration_results

    def preprocess_load_data(self) -> pd.DataFrame:
        """
        预处理负荷数据

        Returns:
            预处理后的负荷数据
        """
        if self.load_data is None:
            raise ValueError("请先加载负荷数据")

        logger.info("开始预处理负荷数据...")

        df = self.load_data.copy()

        # 1. 时间列处理
        if 'Timestamp' in df.columns:
            df['Timestamp'] = pd.to_datetime(df['Timestamp'], errors='coerce')
            df = df.dropna(subset=['Timestamp'])  # 删除时间解析失败的行
            df = df.sort_values('Timestamp').reset_index(drop=True)

        # 2. 数值列处理
        numeric_columns = [
            'HourlyAvgLoad', 'HourlyMaxLoad', 'HourlyMinLoad', 'HourlyStdDev',
            'HourlyActiveEnergy', 'HourlyReactiveEnergy', 'HourlyPowerFactor',
            'DailyMaxLoad', 'DailyMinLoad', 'DailyAvgLoad', 'DailyTotalEnergy',
            'WeeklyMaxLoad', 'WeeklyMinLoad', 'WeeklyAvgLoad', 'WeeklyTotalEnergy'
        ]

        for col in numeric_columns:
            if col in df.columns:
                # 转换为数值类型
                df[col] = pd.to_numeric(df[col], errors='coerce')

                # 异常值处理（使用IQR方法）
                if self.config['processing']['outlier_method'] == 'iqr':
                    Q1 = df[col].quantile(0.25)
                    Q3 = df[col].quantile(0.75)
                    IQR = Q3 - Q1
                    factor = self.config['processing']['outlier_factor']

                    lower_bound = Q1 - factor * IQR
                    upper_bound = Q3 + factor * IQR

                    # 将异常值设为NaN
                    df.loc[(df[col] < lower_bound) | (df[col] > upper_bound), col] = np.nan

        # 3. 缺失值处理
        missing_threshold = self.config['processing']['missing_threshold']

        # 删除缺失值过多的列
        missing_ratio = df.isnull().sum() / len(df)
        cols_to_drop = missing_ratio[missing_ratio > missing_threshold].index
        if len(cols_to_drop) > 0:
            logger.info(f"删除缺失值过多的列: {list(cols_to_drop)}")
            df = df.drop(columns=cols_to_drop)

        # 对剩余的数值列进行插值
        for col in numeric_columns:
            if col in df.columns and df[col].isnull().any():
                # 使用前向填充和后向填充
                df[col] = df[col].fillna(method='ffill').fillna(method='bfill')

        # 4. 添加时间特征
        if 'Timestamp' in df.columns:
            df['Year'] = df['Timestamp'].dt.year
            df['Month'] = df['Timestamp'].dt.month
            df['Day'] = df['Timestamp'].dt.day
            df['Hour'] = df['Timestamp'].dt.hour
            df['DayOfWeek'] = df['Timestamp'].dt.dayofweek  # 0=Monday
            df['IsWeekend'] = (df['DayOfWeek'] >= 5).astype(int)
            df['Season'] = df['Month'].map({
                12: 'Winter', 1: 'Winter', 2: 'Winter',
                3: 'Spring', 4: 'Spring', 5: 'Spring',
                6: 'Summer', 7: 'Summer', 8: 'Summer',
                9: 'Autumn', 10: 'Autumn', 11: 'Autumn'
            })

        logger.info(f"负荷数据预处理完成，形状: {df.shape}")
        return df

    def generate_analysis_report(self) -> str:
        """
        生成数据分析报告

        Returns:
            分析报告文本
        """
        logger.info("生成数据分析报告...")

        report = []
        report.append("=" * 80)
        report.append("空调负荷柔性调控能力分析系统 - 数据探索报告")
        report.append("=" * 80)
        report.append(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")

        # 负荷数据分析
        if self.load_data is not None:
            load_results = self.explore_load_data()
            report.append("1. 负荷数据分析")
            report.append("-" * 40)
            report.append(f"数据形状: {load_results['basic_info']['shape']}")
            report.append(f"内存使用: {load_results['basic_info']['memory_usage']:.2f} MB")
            report.append(f"列数: {len(load_results['basic_info']['columns'])}")

            if 'time_analysis' in load_results:
                report.append(f"时间范围: {load_results['time_analysis']['time_range']['start']} 到 {load_results['time_analysis']['time_range']['end']}")
                report.append(f"时间跨度: {load_results['time_analysis']['time_span_days']} 天")

            missing_cols = load_results['missing_values']['columns_with_missing']
            if missing_cols:
                report.append(f"存在缺失值的列: {len(missing_cols)} 个")
            else:
                report.append("数据完整，无缺失值")
            report.append("")

        # 天气数据分析
        if self.weather_data:
            weather_results = self.explore_weather_data()
            report.append("2. 天气数据分析")
            report.append("-" * 40)
            report.append(f"数据集数量: {weather_results['datasets_count']}")
            report.append(f"覆盖区域: {len(weather_results['regions'])} 个")
            report.append(f"覆盖年份: {weather_results['years']}")
            report.append(f"区域列表: {', '.join(weather_results['regions'][:10])}{'...' if len(weather_results['regions']) > 10 else ''}")
            report.append("")

        report.append("3. 建议")
        report.append("-" * 40)
        report.append("- 数据加载完成，可以进行下一步的数据合并和特征工程")
        report.append("- 建议检查负荷数据与天气数据的时间对齐情况")
        report.append("- 可以开始构建负荷-温度关系模型")
        report.append("")

        report_text = "\n".join(report)

        # 保存报告
        report_file = os.path.join(self.config['data_paths']['results_dir'], 'data_exploration_report.txt')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_text)

        logger.info(f"分析报告已保存至: {report_file}")
        return report_text

def main():
    """主函数 - 执行数据探索分析"""
    print("空调负荷柔性调控能力分析系统 - 数据探索分析")
    print("=" * 60)

    try:
        # 创建分析器实例
        analyzer = ACLoadDataAnalyzer()

        # 1. 加载负荷数据（先加载少量数据进行测试）
        print("1. 加载负荷数据...")
        load_data = analyzer.load_load_data(sample_size=10000)  # 先加载1万行测试
        print(f"   负荷数据加载完成: {load_data.shape}")

        # 2. 加载天气数据（先加载部分区域）
        print("2. 加载天气数据...")
        weather_data = analyzer.load_weather_data(regions=['简阳', '成华'], years=[2023, 2024])
        print(f"   天气数据加载完成: {len(weather_data)} 个数据集")

        # 3. 数据探索
        print("3. 执行数据探索...")
        load_exploration = analyzer.explore_load_data()
        weather_exploration = analyzer.explore_weather_data()

        # 4. 生成报告
        print("4. 生成分析报告...")
        report = analyzer.generate_analysis_report()
        print(report)

        print("\n" + "=" * 60)
        print("数据探索分析完成！")
        print("下一步建议:")
        print("1. 检查生成的报告文件")
        print("2. 根据探索结果调整数据处理策略")
        print("3. 开始数据预处理和特征工程")

    except Exception as e:
        logger.error(f"数据探索分析失败: {e}")
        print(f"错误: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())