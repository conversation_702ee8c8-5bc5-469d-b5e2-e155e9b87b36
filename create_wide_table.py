#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空调负荷柔性调控能力分析系统 - 数据探索分析与大宽表生成脚本

本脚本实现以下功能：
1. 数据加载与探索分析
2. 数据预处理与清洗
3. 特征工程
4. 负荷与天气数据合并
5. 生成建模用的大宽表
6. 数据质量报告生成

作者: AI Assistant
创建时间: 2025-07-13
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
import logging
import os
import glob
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union
import json

# 设置中文字体和警告过滤
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/data_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ACLoadDataAnalyzer:
    """空调负荷数据分析器"""

    def __init__(self, config_path: Optional[str] = None):
        """
        初始化数据分析器

        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.load_data = None
        self.weather_data = {}
        self.merged_data = None

        # 创建必要的目录
        self._create_directories()

        logger.info("空调负荷数据分析器初始化完成")

    def _load_config(self, config_path: Optional[str]) -> Dict:
        """加载配置文件"""
        default_config = {
            'data_paths': {
                'load_data_file': 'complete_load_analysis_data.csv',
                'weather_data_dir': '天气数据',
                'output_dir': 'data/processed',
                'interim_dir': 'data/interim',
                'results_dir': 'results'
            },
            'processing': {
                'chunk_size': 50000,
                'missing_threshold': 0.3,
                'outlier_method': 'iqr',
                'outlier_factor': 1.5
            },
            'features': {
                'base_temperature': 18.0,
                'cooling_threshold': 25.0,
                'heating_threshold': 15.0,
                'lag_features': [1, 2, 3, 6, 12, 24],
                'rolling_windows': [3, 6, 12, 24]
            },
            'visualization': {
                'figure_size': (12, 8),
                'dpi': 300,
                'style': 'seaborn-v0_8'
            }
        }

        if config_path and os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                default_config.update(user_config)

        return default_config

    def _create_directories(self):
        """创建必要的目录"""
        dirs = [
            self.config['data_paths']['output_dir'],
            self.config['data_paths']['interim_dir'],
            self.config['data_paths']['results_dir'],
            'logs',
            'figures'
        ]

        for dir_path in dirs:
            Path(dir_path).mkdir(parents=True, exist_ok=True)

    def load_load_data(self, sample_size: Optional[int] = None) -> pd.DataFrame:
        """
        加载负荷数据

        Args:
            sample_size: 采样大小，None表示加载全部数据

        Returns:
            负荷数据DataFrame
        """
        logger.info("开始加载负荷数据...")

        file_path = self.config['data_paths']['load_data_file']
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"负荷数据文件不存在: {file_path}")

        try:
            # 获取文件大小
            file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
            logger.info(f"负荷数据文件大小: {file_size:.2f} MB")

            # 根据文件大小决定加载策略
            if file_size > 500:  # 大于500MB使用分块加载
                chunk_size = self.config['processing']['chunk_size']
                chunks = []

                for i, chunk in enumerate(pd.read_csv(file_path, chunksize=chunk_size)):
                    chunks.append(chunk)
                    if sample_size and len(chunks) * chunk_size >= sample_size:
                        break
                    if i % 10 == 0:
                        logger.info(f"已加载 {(i+1) * chunk_size} 行数据...")

                self.load_data = pd.concat(chunks, ignore_index=True)
            else:
                # 小文件直接加载
                self.load_data = pd.read_csv(file_path)
                if sample_size:
                    self.load_data = self.load_data.sample(n=min(sample_size, len(self.load_data)))

            logger.info(f"负荷数据加载完成，形状: {self.load_data.shape}")
            return self.load_data

        except Exception as e:
            logger.error(f"加载负荷数据失败: {e}")
            raise

    def load_weather_data(self, regions: Optional[List[str]] = None,
                         years: Optional[List[int]] = None) -> Dict[str, pd.DataFrame]:
        """
        加载天气数据

        Args:
            regions: 指定加载的区域列表，None表示加载所有区域
            years: 指定加载的年份列表，None表示加载所有年份

        Returns:
            天气数据字典，键为"区域_年份"格式
        """
        logger.info("开始加载天气数据...")

        weather_dir = self.config['data_paths']['weather_data_dir']
        if not os.path.exists(weather_dir):
            raise FileNotFoundError(f"天气数据目录不存在: {weather_dir}")

        # 获取所有天气数据文件
        weather_files = []
        weather_files.extend(glob.glob(os.path.join(weather_dir, "*.xls")))
        weather_files.extend(glob.glob(os.path.join(weather_dir, "*.xlsx")))

        logger.info(f"找到 {len(weather_files)} 个天气数据文件")

        weather_data = {}

        for file_path in weather_files:
            file_name = Path(file_path).stem

            try:
                # 解析文件名
                if "城市24小时" in file_name:
                    # 处理城市24小时数据
                    if "2023" in file_name:
                        region, year = "城市24小时", 2023
                    elif "24-25" in file_name:
                        region, year = "城市24小时", 2024  # 主要年份
                    else:
                        continue
                else:
                    # 解析区域天气数据文件名
                    parts = file_name.replace('.xls', '').replace('.xlsx', '')
                    if len(parts) >= 2:
                        # 提取区域名（去掉年份）
                        region = ''.join([c for c in parts if not c.isdigit()])
                        # 提取年份
                        year_str = ''.join([c for c in parts if c.isdigit()])
                        if year_str:
                            year = 2000 + int(year_str) if len(year_str) == 2 else int(year_str)
                        else:
                            continue
                    else:
                        continue

                # 过滤条件
                if regions and region not in regions:
                    continue
                if years and year not in years:
                    continue

                # 读取Excel文件
                try:
                    if file_path.endswith('.xlsx'):
                        df = pd.read_excel(file_path, engine='openpyxl')
                    else:
                        df = pd.read_excel(file_path, engine='xlrd')
                except:
                    # 尝试其他引擎
                    df = pd.read_excel(file_path)

                # 标准化列名
                df.columns = [str(col).strip() for col in df.columns]

                # 添加元数据
                df['region'] = region
                df['year'] = year
                df['data_source'] = file_name

                # 存储数据
                key = f"{region}_{year}"
                weather_data[key] = df

                logger.info(f"加载天气数据 {key}: {df.shape}")

            except Exception as e:
                logger.warning(f"加载天气文件 {file_path} 失败: {e}")
                continue

        self.weather_data = weather_data
        logger.info(f"天气数据加载完成，共加载 {len(weather_data)} 个数据集")
        return weather_data

    def explore_load_data(self) -> Dict:
        """
        探索负荷数据

        Returns:
            探索结果字典
        """
        if self.load_data is None:
            raise ValueError("请先加载负荷数据")

        logger.info("开始探索负荷数据...")

        df = self.load_data
        exploration_results = {}

        # 基本信息
        exploration_results['basic_info'] = {
            'shape': df.shape,
            'columns': df.columns.tolist(),
            'dtypes': df.dtypes.to_dict(),
            'memory_usage': df.memory_usage(deep=True).sum() / (1024**2)  # MB
        }

        # 缺失值分析
        missing_values = df.isnull().sum()
        missing_percent = (missing_values / len(df)) * 100
        exploration_results['missing_values'] = {
            'missing_counts': missing_values.to_dict(),
            'missing_percentages': missing_percent.to_dict(),
            'columns_with_missing': missing_values[missing_values > 0].index.tolist()
        }

        # 数值列统计
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            exploration_results['numeric_stats'] = df[numeric_cols].describe().to_dict()

        # 分类列统计
        categorical_cols = df.select_dtypes(include=['object']).columns
        exploration_results['categorical_stats'] = {}
        for col in categorical_cols:
            if col in df.columns:
                value_counts = df[col].value_counts()
                exploration_results['categorical_stats'][col] = {
                    'unique_count': df[col].nunique(),
                    'top_values': value_counts.head(10).to_dict()
                }

        # 时间列分析
        if 'Timestamp' in df.columns:
            try:
                df['Timestamp'] = pd.to_datetime(df['Timestamp'])
                exploration_results['time_analysis'] = {
                    'time_range': {
                        'start': df['Timestamp'].min().strftime('%Y-%m-%d %H:%M:%S'),
                        'end': df['Timestamp'].max().strftime('%Y-%m-%d %H:%M:%S')
                    },
                    'time_span_days': (df['Timestamp'].max() - df['Timestamp'].min()).days,
                    'frequency': 'hourly'  # 基于数据结构推断
                }
            except:
                logger.warning("时间列解析失败")

        logger.info("负荷数据探索完成")
        return exploration_results

    def explore_weather_data(self) -> Dict:
        """
        探索天气数据

        Returns:
            探索结果字典
        """
        if not self.weather_data:
            raise ValueError("请先加载天气数据")

        logger.info("开始探索天气数据...")

        exploration_results = {
            'datasets_count': len(self.weather_data),
            'datasets_info': {},
            'regions': set(),
            'years': set(),
            'common_columns': None,
            'data_quality': {}
        }

        all_columns = []

        for key, df in self.weather_data.items():
            region, year = key.split('_')
            exploration_results['regions'].add(region)
            exploration_results['years'].add(int(year))

            # 数据集基本信息
            exploration_results['datasets_info'][key] = {
                'shape': df.shape,
                'columns': df.columns.tolist(),
                'memory_usage': df.memory_usage(deep=True).sum() / (1024**2),  # MB
                'missing_values': df.isnull().sum().sum(),
                'missing_percentage': (df.isnull().sum().sum() / df.size) * 100
            }

            all_columns.append(set(df.columns))

        # 找出所有数据集的公共列
        if all_columns:
            exploration_results['common_columns'] = list(set.intersection(*all_columns))

        # 转换集合为列表以便JSON序列化
        exploration_results['regions'] = sorted(list(exploration_results['regions']))
        exploration_results['years'] = sorted(list(exploration_results['years']))

        logger.info("天气数据探索完成")
        return exploration_results

    def preprocess_load_data(self) -> pd.DataFrame:
        """
        预处理负荷数据

        Returns:
            预处理后的负荷数据
        """
        if self.load_data is None:
            raise ValueError("请先加载负荷数据")

        logger.info("开始预处理负荷数据...")

        df = self.load_data.copy()

        # 1. 时间列处理
        if 'Timestamp' in df.columns:
            df['Timestamp'] = pd.to_datetime(df['Timestamp'], errors='coerce')
            df = df.dropna(subset=['Timestamp'])  # 删除时间解析失败的行
            df = df.sort_values('Timestamp').reset_index(drop=True)

        # 2. 数值列处理
        numeric_columns = [
            'HourlyAvgLoad', 'HourlyMaxLoad', 'HourlyMinLoad', 'HourlyStdDev',
            'HourlyActiveEnergy', 'HourlyReactiveEnergy', 'HourlyPowerFactor',
            'DailyMaxLoad', 'DailyMinLoad', 'DailyAvgLoad', 'DailyTotalEnergy',
            'WeeklyMaxLoad', 'WeeklyMinLoad', 'WeeklyAvgLoad', 'WeeklyTotalEnergy'
        ]

        for col in numeric_columns:
            if col in df.columns:
                # 转换为数值类型
                df[col] = pd.to_numeric(df[col], errors='coerce')

                # 异常值处理（使用IQR方法）
                if self.config['processing']['outlier_method'] == 'iqr':
                    Q1 = df[col].quantile(0.25)
                    Q3 = df[col].quantile(0.75)
                    IQR = Q3 - Q1
                    factor = self.config['processing']['outlier_factor']

                    lower_bound = Q1 - factor * IQR
                    upper_bound = Q3 + factor * IQR

                    # 将异常值设为NaN
                    df.loc[(df[col] < lower_bound) | (df[col] > upper_bound), col] = np.nan

        # 3. 缺失值处理
        missing_threshold = self.config['processing']['missing_threshold']

        # 删除缺失值过多的列
        missing_ratio = df.isnull().sum() / len(df)
        cols_to_drop = missing_ratio[missing_ratio > missing_threshold].index
        if len(cols_to_drop) > 0:
            logger.info(f"删除缺失值过多的列: {list(cols_to_drop)}")
            df = df.drop(columns=cols_to_drop)

        # 对剩余的数值列进行插值
        for col in numeric_columns:
            if col in df.columns and df[col].isnull().any():
                # 使用前向填充和后向填充
                df[col] = df[col].fillna(method='ffill').fillna(method='bfill')

        # 4. 添加时间特征
        if 'Timestamp' in df.columns:
            df['Year'] = df['Timestamp'].dt.year
            df['Month'] = df['Timestamp'].dt.month
            df['Day'] = df['Timestamp'].dt.day
            df['Hour'] = df['Timestamp'].dt.hour
            df['DayOfWeek'] = df['Timestamp'].dt.dayofweek  # 0=Monday
            df['IsWeekend'] = (df['DayOfWeek'] >= 5).astype(int)
            df['Season'] = df['Month'].map({
                12: 'Winter', 1: 'Winter', 2: 'Winter',
                3: 'Spring', 4: 'Spring', 5: 'Spring',
                6: 'Summer', 7: 'Summer', 8: 'Summer',
                9: 'Autumn', 10: 'Autumn', 11: 'Autumn'
            })

        logger.info(f"负荷数据预处理完成，形状: {df.shape}")
        return df

    def preprocess_weather_data(self) -> Dict[str, pd.DataFrame]:
        """
        预处理天气数据

        Returns:
            预处理后的天气数据字典
        """
        if not self.weather_data:
            raise ValueError("请先加载天气数据")

        logger.info("开始预处理天气数据...")

        processed_weather = {}

        for key, df in self.weather_data.items():
            logger.info(f"处理天气数据: {key}")

            df_processed = df.copy()

            # 1. 标准化列名（尝试识别常见的气象要素）
            column_mapping = {
                '时间': 'Timestamp', '日期': 'Timestamp', 'time': 'Timestamp', 'date': 'Timestamp',
                '温度': 'Temperature', '气温': 'Temperature', 'temp': 'Temperature', 'temperature': 'Temperature',
                '湿度': 'Humidity', '相对湿度': 'Humidity', 'humidity': 'Humidity', 'rh': 'Humidity',
                '气压': 'Pressure', '大气压': 'Pressure', 'pressure': 'Pressure',
                '降水': 'Precipitation', '降水量': 'Precipitation', 'rain': 'Precipitation', 'precipitation': 'Precipitation',
                '风速': 'WindSpeed', 'wind_speed': 'WindSpeed', 'windspeed': 'WindSpeed',
                '风向': 'WindDirection', 'wind_direction': 'WindDirection', 'winddir': 'WindDirection'
            }

            # 应用列名映射
            for old_name, new_name in column_mapping.items():
                if old_name in df_processed.columns:
                    df_processed = df_processed.rename(columns={old_name: new_name})

            # 2. 时间列处理
            time_columns = ['Timestamp', 'time', 'date', '时间', '日期']
            timestamp_col = None

            for col in time_columns:
                if col in df_processed.columns:
                    timestamp_col = col
                    break

            if timestamp_col:
                try:
                    df_processed['Timestamp'] = pd.to_datetime(df_processed[timestamp_col], errors='coerce')
                    if timestamp_col != 'Timestamp':
                        df_processed = df_processed.drop(columns=[timestamp_col])

                    # 删除时间解析失败的行
                    df_processed = df_processed.dropna(subset=['Timestamp'])
                    df_processed = df_processed.sort_values('Timestamp').reset_index(drop=True)

                except Exception as e:
                    logger.warning(f"时间列处理失败 {key}: {e}")

            # 3. 数值列处理
            numeric_columns = ['Temperature', 'Humidity', 'Pressure', 'Precipitation', 'WindSpeed']

            for col in numeric_columns:
                if col in df_processed.columns:
                    # 转换为数值类型
                    df_processed[col] = pd.to_numeric(df_processed[col], errors='coerce')

                    # 基于物理约束的异常值处理
                    if col == 'Temperature':
                        # 温度范围：-50°C 到 60°C
                        df_processed.loc[(df_processed[col] < -50) | (df_processed[col] > 60), col] = np.nan
                    elif col == 'Humidity':
                        # 湿度范围：0% 到 100%
                        df_processed.loc[(df_processed[col] < 0) | (df_processed[col] > 100), col] = np.nan
                    elif col == 'Pressure':
                        # 气压范围：800 到 1100 hPa
                        df_processed.loc[(df_processed[col] < 800) | (df_processed[col] > 1100), col] = np.nan
                    elif col == 'Precipitation':
                        # 降水量：非负值，上限500mm/h
                        df_processed.loc[(df_processed[col] < 0) | (df_processed[col] > 500), col] = np.nan
                    elif col == 'WindSpeed':
                        # 风速：非负值，上限200m/s
                        df_processed.loc[(df_processed[col] < 0) | (df_processed[col] > 200), col] = np.nan

            # 4. 缺失值插值
            for col in numeric_columns:
                if col in df_processed.columns and df_processed[col].isnull().any():
                    # 使用线性插值
                    df_processed[col] = df_processed[col].interpolate(method='linear')
                    # 剩余的用前向和后向填充
                    df_processed[col] = df_processed[col].fillna(method='ffill').fillna(method='bfill')

            # 5. 添加时间特征
            if 'Timestamp' in df_processed.columns:
                df_processed['Year'] = df_processed['Timestamp'].dt.year
                df_processed['Month'] = df_processed['Timestamp'].dt.month
                df_processed['Day'] = df_processed['Timestamp'].dt.day
                df_processed['Hour'] = df_processed['Timestamp'].dt.hour
                df_processed['DayOfWeek'] = df_processed['Timestamp'].dt.dayofweek
                df_processed['IsWeekend'] = (df_processed['DayOfWeek'] >= 5).astype(int)

            processed_weather[key] = df_processed
            logger.info(f"天气数据 {key} 预处理完成，形状: {df_processed.shape}")

        logger.info("天气数据预处理完成")
        return processed_weather

    def create_temperature_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        创建温度相关特征

        Args:
            df: 包含温度数据的DataFrame

        Returns:
            添加温度特征后的DataFrame
        """
        if 'Temperature' not in df.columns:
            logger.warning("数据中没有温度列，跳过温度特征创建")
            return df

        df_features = df.copy()

        base_temp = self.config['features']['base_temperature']
        cooling_threshold = self.config['features']['cooling_threshold']
        heating_threshold = self.config['features']['heating_threshold']

        # 冷却度日 (Cooling Degree Days)
        df_features['CoolingDegreeDays'] = np.maximum(df_features['Temperature'] - base_temp, 0)

        # 加热度日 (Heating Degree Days)
        df_features['HeatingDegreeDays'] = np.maximum(base_temp - df_features['Temperature'], 0)

        # 空调需求指标
        df_features['CoolingDemand'] = np.maximum(df_features['Temperature'] - cooling_threshold, 0)
        df_features['HeatingDemand'] = np.maximum(heating_threshold - df_features['Temperature'], 0)

        # 温度舒适度指标
        df_features['TempComfortIndex'] = 1 - np.abs(df_features['Temperature'] - 22) / 20  # 22°C为最舒适温度
        df_features['TempComfortIndex'] = np.clip(df_features['TempComfortIndex'], 0, 1)

        # 温度变化率
        df_features['TempChange'] = df_features['Temperature'].diff()
        df_features['TempChangeAbs'] = np.abs(df_features['TempChange'])

        # 滚动窗口特征
        for window in [3, 6, 12, 24]:
            df_features[f'TempMA_{window}h'] = df_features['Temperature'].rolling(window=window, min_periods=1).mean()
            df_features[f'TempStd_{window}h'] = df_features['Temperature'].rolling(window=window, min_periods=1).std()
            df_features[f'TempMax_{window}h'] = df_features['Temperature'].rolling(window=window, min_periods=1).max()
            df_features[f'TempMin_{window}h'] = df_features['Temperature'].rolling(window=window, min_periods=1).min()

        return df_features