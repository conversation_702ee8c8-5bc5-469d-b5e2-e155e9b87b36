#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空调负荷柔性调控能力分析系统 - 基础大宽表生成脚本

基于已有的数据探索结果，生成用于建模的基础大宽表

作者: AI Assistant
创建时间: 2025-07-13
"""

import pandas as pd
import numpy as np
import warnings
import logging
import os
import glob
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional

warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/wide_table_creation.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BasicWideTableCreator:
    """基础大宽表创建器"""
    
    def __init__(self):
        """初始化"""
        self.load_data = None
        self.weather_data = {}
        self.wide_table = None
        
        # 创建必要目录
        Path('logs').mkdir(exist_ok=True)
        Path('data/processed').mkdir(parents=True, exist_ok=True)
        
        logger.info("基础大宽表创建器初始化完成")
    
    def load_sample_data(self, load_sample_size: int = 50000, 
                        weather_regions: List[str] = None) -> None:
        """
        加载样本数据
        
        Args:
            load_sample_size: 负荷数据样本大小
            weather_regions: 天气数据区域列表
        """
        logger.info("开始加载样本数据...")
        
        # 加载负荷数据样本
        logger.info(f"加载负荷数据样本 ({load_sample_size} 行)...")
        self.load_data = pd.read_csv('complete_load_analysis_data.csv', nrows=load_sample_size)
        logger.info(f"负荷数据加载完成: {self.load_data.shape}")
        
        # 加载天气数据
        if weather_regions is None:
            weather_regions = ['简阳', '成华']  # 默认加载这两个区域
        
        logger.info(f"加载天气数据，区域: {weather_regions}")
        weather_files = glob.glob('天气数据/*.xls') + glob.glob('天气数据/*.xlsx')
        
        for file_path in weather_files:
            file_name = Path(file_path).stem
            
            # 解析区域和年份
            region = None
            year = None
            
            for target_region in weather_regions:
                if target_region in file_name:
                    region = target_region
                    # 提取年份
                    if '23' in file_name:
                        year = 2023
                    elif '24' in file_name:
                        year = 2024
                    elif '25' in file_name:
                        year = 2025
                    break
            
            if region and year:
                try:
                    df = pd.read_excel(file_path)
                    df['region'] = region
                    df['year'] = year
                    
                    key = f"{region}_{year}"
                    self.weather_data[key] = df
                    logger.info(f"加载天气数据 {key}: {df.shape}")
                    
                except Exception as e:
                    logger.warning(f"加载天气文件失败 {file_path}: {e}")
        
        logger.info(f"数据加载完成，天气数据集: {len(self.weather_data)} 个")
    
    def preprocess_data(self) -> None:
        """预处理数据"""
        logger.info("开始数据预处理...")
        
        # 预处理负荷数据
        if self.load_data is not None:
            logger.info("预处理负荷数据...")
            
            # 时间列处理
            self.load_data['Timestamp'] = pd.to_datetime(self.load_data['Timestamp'])
            
            # 添加时间特征
            self.load_data['Year'] = self.load_data['Timestamp'].dt.year
            self.load_data['Month'] = self.load_data['Timestamp'].dt.month
            self.load_data['Day'] = self.load_data['Timestamp'].dt.day
            self.load_data['Hour'] = self.load_data['Timestamp'].dt.hour
            self.load_data['DayOfWeek'] = self.load_data['Timestamp'].dt.dayofweek
            self.load_data['IsWeekend'] = (self.load_data['DayOfWeek'] >= 5).astype(int)
            
            # 季节特征
            self.load_data['Season'] = self.load_data['Month'].map({
                12: 'Winter', 1: 'Winter', 2: 'Winter',
                3: 'Spring', 4: 'Spring', 5: 'Spring',
                6: 'Summer', 7: 'Summer', 8: 'Summer',
                9: 'Autumn', 10: 'Autumn', 11: 'Autumn'
            })
            
            logger.info(f"负荷数据预处理完成: {self.load_data.shape}")
        
        # 预处理天气数据
        for key, df in self.weather_data.items():
            logger.info(f"预处理天气数据: {key}")
            
            # 尝试识别时间列
            time_columns = [col for col in df.columns if any(keyword in col.lower()
                           for keyword in ['时间', 'time', '日期', 'date', '北京时', 'utc'])]
            
            if time_columns:
                time_col = time_columns[0]
                try:
                    df['Timestamp'] = pd.to_datetime(df[time_col])
                    df = df.sort_values('Timestamp').reset_index(drop=True)
                    
                    # 添加时间特征
                    df['Year'] = df['Timestamp'].dt.year
                    df['Month'] = df['Timestamp'].dt.month
                    df['Day'] = df['Timestamp'].dt.day
                    df['Hour'] = df['Timestamp'].dt.hour
                    
                    self.weather_data[key] = df
                    logger.info(f"天气数据 {key} 时间处理完成")
                    
                except Exception as e:
                    logger.warning(f"天气数据 {key} 时间处理失败: {e}")
        
        logger.info("数据预处理完成")
    
    def create_wide_table(self) -> pd.DataFrame:
        """
        创建大宽表
        
        Returns:
            合并后的大宽表
        """
        logger.info("开始创建大宽表...")
        
        if self.load_data is None:
            raise ValueError("请先加载负荷数据")
        
        # 从负荷数据开始
        wide_table = self.load_data.copy()
        
        # 尝试从地址信息中提取区域信息
        if 'ConsumerAddress' in wide_table.columns:
            # 简单的区域匹配逻辑
            def extract_region(address):
                if pd.isna(address):
                    return None
                address = str(address)
                for region in ['简阳', '成华', '双流', '大邑', '崇州', '彭州', '新津', '新都', 
                              '武侯', '温江', '甘孜', '蒲江', '邛崃', '郫都', '都江堰', 
                              '金堂', '金牛', '锦江', '青白江', '青羊', '龙泉驿']:
                    if region in address:
                        return region
                return None
            
            wide_table['Region'] = wide_table['ConsumerAddress'].apply(extract_region)
            logger.info("从地址信息中提取区域信息完成")
        
        # 合并天气数据
        weather_merged_count = 0
        
        for key, weather_df in self.weather_data.items():
            region, year = key.split('_')
            year = int(year)
            
            if 'Timestamp' not in weather_df.columns:
                logger.warning(f"天气数据 {key} 缺少时间列，跳过合并")
                continue
            
            # 筛选对应区域和年份的负荷数据
            mask = (wide_table['Year'] == year)
            if 'Region' in wide_table.columns:
                mask = mask & (wide_table['Region'] == region)
            
            matching_load_data = wide_table[mask]
            
            if len(matching_load_data) == 0:
                logger.info(f"没有找到匹配的负荷数据 {key}")
                continue
            
            # 准备天气数据用于合并
            weather_for_merge = weather_df.copy()
            
            # 选择主要的天气特征列
            weather_cols = []
            for col in weather_for_merge.columns:
                col_lower = col.lower()
                if any(keyword in col_lower for keyword in 
                      ['温度', 'temp', '湿度', 'humidity', '气压', 'pressure', 
                       '风速', 'wind', '降水', 'rain', 'precipitation']):
                    weather_cols.append(col)
            
            if weather_cols:
                # 重命名天气列以避免冲突
                weather_rename = {}
                for col in weather_cols:
                    new_name = f"Weather_{col}_{region}"
                    weather_rename[col] = new_name
                
                weather_for_merge = weather_for_merge.rename(columns=weather_rename)
                
                # 按时间合并（使用最近邻匹配）
                merge_cols = ['Timestamp'] + list(weather_rename.values())
                weather_for_merge = weather_for_merge[merge_cols].dropna(subset=['Timestamp'])
                
                # 使用merge_asof进行时间序列合并
                try:
                    # 确保时间列已排序
                    wide_table = wide_table.sort_values('Timestamp')
                    weather_for_merge = weather_for_merge.sort_values('Timestamp')
                    
                    # 只对匹配的数据进行合并
                    load_subset = wide_table[mask].copy()
                    merged_subset = pd.merge_asof(
                        load_subset.sort_values('Timestamp'),
                        weather_for_merge,
                        on='Timestamp',
                        direction='nearest'
                    )
                    
                    # 更新原始数据
                    for col in weather_rename.values():
                        if col in merged_subset.columns:
                            wide_table.loc[mask, col] = merged_subset[col].values
                    
                    weather_merged_count += 1
                    logger.info(f"天气数据 {key} 合并完成，匹配记录: {len(merged_subset)}")
                    
                except Exception as e:
                    logger.warning(f"天气数据 {key} 合并失败: {e}")
        
        self.wide_table = wide_table
        logger.info(f"大宽表创建完成，形状: {wide_table.shape}，合并了 {weather_merged_count} 个天气数据集")
        
        return wide_table
    
    def save_wide_table(self, filename: str = None) -> str:
        """
        保存大宽表
        
        Args:
            filename: 文件名，默认自动生成
            
        Returns:
            保存的文件路径
        """
        if self.wide_table is None:
            raise ValueError("请先创建大宽表")
        
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"wide_table_basic_{timestamp}.csv"
        
        filepath = os.path.join('data/processed', filename)
        
        # 保存为CSV
        self.wide_table.to_csv(filepath, index=False, encoding='utf-8')
        logger.info(f"大宽表已保存至: {filepath}")
        
        # 保存数据摘要
        summary_file = filepath.replace('.csv', '_summary.txt')
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("大宽表数据摘要\n")
            f.write("=" * 50 + "\n")
            f.write(f"创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"数据形状: {self.wide_table.shape}\n")
            f.write(f"列数: {len(self.wide_table.columns)}\n")
            f.write(f"行数: {len(self.wide_table)}\n\n")
            
            f.write("列名列表:\n")
            for i, col in enumerate(self.wide_table.columns, 1):
                f.write(f"{i:3d}. {col}\n")
            
            f.write(f"\n缺失值统计:\n")
            missing_stats = self.wide_table.isnull().sum()
            missing_stats = missing_stats[missing_stats > 0]
            if len(missing_stats) > 0:
                for col, count in missing_stats.items():
                    pct = (count / len(self.wide_table)) * 100
                    f.write(f"{col}: {count} ({pct:.2f}%)\n")
            else:
                f.write("无缺失值\n")
        
        logger.info(f"数据摘要已保存至: {summary_file}")
        return filepath

def main():
    """主函数"""
    print("空调负荷柔性调控能力分析系统 - 基础大宽表生成")
    print("=" * 60)
    
    try:
        # 创建大宽表生成器
        creator = BasicWideTableCreator()
        
        # 1. 加载样本数据
        print("1. 加载样本数据...")
        creator.load_sample_data(load_sample_size=20000, weather_regions=['简阳', '成华'])
        
        # 2. 数据预处理
        print("2. 数据预处理...")
        creator.preprocess_data()
        
        # 3. 创建大宽表
        print("3. 创建大宽表...")
        wide_table = creator.create_wide_table()
        print(f"   大宽表创建完成: {wide_table.shape}")
        
        # 4. 保存大宽表
        print("4. 保存大宽表...")
        filepath = creator.save_wide_table()
        print(f"   大宽表已保存至: {filepath}")
        
        # 5. 显示基本信息
        print("\n5. 大宽表基本信息:")
        print(f"   形状: {wide_table.shape}")
        print(f"   时间范围: {wide_table['Timestamp'].min()} 到 {wide_table['Timestamp'].max()}")
        
        weather_cols = [col for col in wide_table.columns if col.startswith('Weather_')]
        print(f"   天气特征列数: {len(weather_cols)}")
        
        if len(weather_cols) > 0:
            print("   天气特征列示例:")
            for col in weather_cols[:5]:
                print(f"     - {col}")
        
        print("\n" + "=" * 60)
        print("基础大宽表生成完成！")
        print("下一步建议:")
        print("1. 检查生成的大宽表文件")
        print("2. 进行数据质量验证")
        print("3. 开始特征工程和模型构建")
        
    except Exception as e:
        logger.error(f"大宽表生成失败: {e}")
        print(f"错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
