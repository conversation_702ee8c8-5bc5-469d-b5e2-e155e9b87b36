#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试气象数据合并问题的简化脚本
"""

import pandas as pd
import numpy as np
import warnings

warnings.filterwarnings('ignore')

def test_weather_merge():
    """测试气象数据合并"""
    print("测试气象数据合并...")
    
    # 1. 加载更多负荷数据以确保包含都江堰数据
    print("1. 加载负荷数据...")
    load_data = pd.read_csv('complete_load_analysis_data.csv', nrows=50000)
    load_data['Timestamp'] = pd.to_datetime(load_data['Timestamp'])
    
    # 添加区域信息
    def extract_region(address):
        if pd.isna(address):
            return None
        address = str(address)
        if '都江堰' in address:
            return '都江堰'
        elif '简阳' in address:
            return '简阳'
        elif '新都' in address:
            return '新都'
        return None
    
    load_data['Region'] = load_data['ConsumerAddress'].apply(extract_region)
    load_data['Year'] = load_data['Timestamp'].dt.year
    
    print(f"负荷数据形状: {load_data.shape}")
    print(f"区域分布: {load_data['Region'].value_counts().to_dict()}")
    
    # 2. 加载一个气象数据文件
    print("\n2. 加载气象数据...")
    weather_file = '天气数据/都江堰24.xls'
    weather_df = pd.read_excel(weather_file)
    
    print(f"气象数据形状: {weather_df.shape}")
    print(f"气象数据列: {list(weather_df.columns)}")
    
    # 处理时间列
    weather_df['Timestamp'] = pd.to_datetime(weather_df['北京时(UTC+8)'])
    weather_df = weather_df.sort_values('Timestamp').reset_index(drop=True)
    
    # 选择一个简单的数值列
    pressure_col = '海平面气压(hPa)'
    if pressure_col in weather_df.columns:
        print(f"\n{pressure_col} 列信息:")
        print(f"数据类型: {weather_df[pressure_col].dtype}")
        print(f"前5个值: {list(weather_df[pressure_col].head())}")
        print(f"缺失值: {weather_df[pressure_col].isnull().sum()}")
        
        # 3. 尝试合并
        print("\n3. 尝试合并...")
        
        # 筛选都江堰的负荷数据
        dujiangyan_load = load_data[
            (load_data['Region'] == '都江堰') & 
            (load_data['Year'] == 2024)
        ].copy()
        
        print(f"都江堰2024年负荷数据: {len(dujiangyan_load)} 条")
        
        if len(dujiangyan_load) > 0:
            # 准备气象数据
            weather_for_merge = weather_df[['Timestamp', pressure_col]].copy()
            weather_for_merge = weather_for_merge.rename(columns={pressure_col: 'Pressure_都江堰'})
            weather_for_merge = weather_for_merge.dropna()
            
            print(f"气象数据用于合并: {len(weather_for_merge)} 条")
            
            # 排序
            dujiangyan_load = dujiangyan_load.sort_values('Timestamp')
            weather_for_merge = weather_for_merge.sort_values('Timestamp')
            
            print(f"负荷数据时间范围: {dujiangyan_load['Timestamp'].min()} 到 {dujiangyan_load['Timestamp'].max()}")
            print(f"气象数据时间范围: {weather_for_merge['Timestamp'].min()} 到 {weather_for_merge['Timestamp'].max()}")
            
            try:
                # 尝试merge_asof
                merged = pd.merge_asof(
                    dujiangyan_load,
                    weather_for_merge,
                    on='Timestamp',
                    direction='nearest'
                )
                
                print(f"合并成功！合并后形状: {merged.shape}")
                print(f"新增列: Pressure_都江堰")
                print(f"气象数据覆盖率: {merged['Pressure_都江堰'].notna().sum() / len(merged) * 100:.1f}%")
                
                # 检查合并结果
                print(f"气象数据前5个值: {list(merged['Pressure_都江堰'].head())}")
                
                return True
                
            except Exception as e:
                print(f"合并失败: {e}")
                import traceback
                traceback.print_exc()
                return False
        else:
            print("没有找到匹配的负荷数据")
            return False
    else:
        print(f"未找到列: {pressure_col}")
        return False

if __name__ == "__main__":
    success = test_weather_merge()
    if success:
        print("\n✓ 气象数据合并测试成功！")
    else:
        print("\n✗ 气象数据合并测试失败！")
