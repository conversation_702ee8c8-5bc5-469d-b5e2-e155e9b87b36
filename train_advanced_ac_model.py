#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
空调负荷柔性调控能力分析系统 - 高级模型训练脚本

使用完整数据集训练多种先进模型：
1. 传统机器学习模型（XGBoost、LightGBM、Random Forest）
2. 深度学习模型（神经网络、LSTM）
3. 集成学习模型

作者: AI Assistant
创建时间: 2025-07-14
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
import logging
import os
import joblib
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple

# 机器学习库
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestRegressor, VotingRegressor
from sklearn.linear_model import Ridge, ElasticNet
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import xgboost as xgb
import lightgbm as lgb

# 深度学习库
try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    print("TensorFlow未安装，将跳过深度学习模型")

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/advanced_model_training.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AdvancedACLoadModelTrainer:
    """高级空调负荷模型训练器"""
    
    def __init__(self):
        """初始化"""
        self.data = None
        self.X_train = None
        self.X_val = None
        self.X_test = None
        self.y_train = None
        self.y_val = None
        self.y_test = None
        self.scaler = None
        self.models = {}
        self.results = {}
        
        # 创建必要目录
        Path('logs').mkdir(exist_ok=True)
        Path('models/advanced').mkdir(parents=True, exist_ok=True)
        Path('figures/model_comparison').mkdir(parents=True, exist_ok=True)
        
        logger.info("高级空调负荷模型训练器初始化完成")
    
    def load_data(self, file_path: str = None) -> pd.DataFrame:
        """加载完整大宽表数据"""
        if file_path is None:
            # 查找最新的大宽表文件
            processed_dir = Path('data/processed')
            wide_table_files = list(processed_dir.glob('final_wide_table_*.csv'))
            
            if not wide_table_files:
                raise FileNotFoundError("未找到大宽表文件")
            
            file_path = max(wide_table_files, key=lambda x: x.stat().st_mtime)
        
        logger.info(f"加载数据文件: {file_path}")
        
        # 分块加载大文件
        chunk_size = 50000
        chunks = []
        
        for i, chunk in enumerate(pd.read_csv(file_path, chunksize=chunk_size)):
            chunks.append(chunk)
            if (i + 1) % 5 == 0:
                logger.info(f"已加载 {(i + 1) * chunk_size} 行数据...")
        
        self.data = pd.concat(chunks, ignore_index=True)
        logger.info(f"数据加载完成，形状: {self.data.shape}")
        
        return self.data
    
    def create_target_variable(self) -> pd.Series:
        """创建目标变量：空调负荷"""
        logger.info("创建目标变量...")
        
        # 基于温度和季节创建空调负荷比例
        df = self.data.copy()
        
        # 初始化空调负荷比例
        df['ac_load_ratio'] = 0.0
        
        # 找到温度列
        temp_cols = [col for col in df.columns if 'Temperature' in col and df[col].notna().sum() > 1000]
        
        if temp_cols:
            # 使用第一个可用的温度列
            temp_col = temp_cols[0]
            logger.info(f"使用温度列: {temp_col}")
            
            # 制冷需求（夏季高温）
            cooling_mask = (df[temp_col] > 26) & (df['Month'].isin([6, 7, 8, 9]))
            df.loc[cooling_mask, 'ac_load_ratio'] = np.minimum(
                (df.loc[cooling_mask, temp_col] - 26) / 15 * 0.7, 0.8
            )
            
            # 制热需求（冬季低温）
            heating_mask = (df[temp_col] < 16) & (df['Month'].isin([12, 1, 2, 3]))
            df.loc[heating_mask, 'ac_load_ratio'] = np.minimum(
                (16 - df.loc[heating_mask, temp_col]) / 15 * 0.6, 0.7
            )
        else:
            # 如果没有温度数据，基于季节和时间创建简化的空调负荷比例
            logger.warning("未找到温度数据，使用简化的空调负荷估算")
            
            # 夏季制冷
            summer_mask = df['Month'].isin([6, 7, 8, 9]) & df['Hour'].isin(range(10, 22))
            df.loc[summer_mask, 'ac_load_ratio'] = 0.4
            
            # 冬季制热
            winter_mask = df['Month'].isin([12, 1, 2, 3]) & df['Hour'].isin(range(8, 20))
            df.loc[winter_mask, 'ac_load_ratio'] = 0.3
        
        # 添加随机噪声使数据更真实
        noise = np.random.normal(0, 0.05, len(df))
        df['ac_load_ratio'] = np.clip(df['ac_load_ratio'] + noise, 0, 1)
        
        # 计算空调负荷
        if 'HourlyAvgLoad' in df.columns:
            ac_load = df['HourlyAvgLoad'] * df['ac_load_ratio']
        else:
            # 如果没有负荷数据，创建模拟负荷
            base_load = 50 + 30 * np.sin(2 * np.pi * df['Hour'] / 24)  # 日周期
            seasonal_factor = 1 + 0.3 * np.sin(2 * np.pi * df['Month'] / 12)  # 季节周期
            ac_load = base_load * seasonal_factor * df['ac_load_ratio']
        
        logger.info(f"空调负荷统计: 均值={ac_load.mean():.2f}, 标准差={ac_load.std():.2f}")
        return ac_load
    
    def prepare_features(self) -> Tuple[pd.DataFrame, List[str]]:
        """准备特征数据"""
        logger.info("准备特征数据...")
        
        df = self.data.copy()
        
        # 选择特征列
        feature_cols = []
        
        # 1. 时间特征
        time_features = ['Hour', 'DayOfWeek', 'Month', 'IsWeekend']
        feature_cols.extend([col for col in time_features if col in df.columns])
        
        # 2. 负荷特征
        load_features = [col for col in df.columns if any(keyword in col for keyword in 
                        ['Load', 'Energy', 'Power']) and col != 'HourlyAvgLoad']
        feature_cols.extend(load_features[:10])  # 最多10个负荷特征
        
        # 3. 气象特征
        weather_features = [col for col in df.columns if any(keyword in col for keyword in 
                           ['Temperature', 'Humidity', 'Pressure', 'Precipitation', 'WindSpeed'])]
        feature_cols.extend(weather_features)
        
        # 4. 空气质量特征（选择覆盖率高的）
        aq_features = [col for col in df.columns if any(keyword in col for keyword in 
                      ['AQI', 'PM2_5', 'PM10', 'SO2', 'NO2', 'O3', 'CO'])]
        
        # 按覆盖率排序，选择前20个
        aq_coverage = {}
        for col in aq_features:
            coverage = df[col].notna().sum() / len(df)
            aq_coverage[col] = coverage
        
        top_aq_features = sorted(aq_coverage.items(), key=lambda x: x[1], reverse=True)[:20]
        feature_cols.extend([col for col, _ in top_aq_features])
        
        # 5. 创建交互特征
        if 'Hour' in df.columns and 'Month' in df.columns:
            df['Hour_Month'] = df['Hour'] * df['Month']
            feature_cols.append('Hour_Month')
        
        if 'IsWeekend' in df.columns and 'Hour' in df.columns:
            df['Weekend_Hour'] = df['IsWeekend'] * df['Hour']
            feature_cols.append('Weekend_Hour')
        
        # 移除重复和无效的特征
        feature_cols = list(set(feature_cols))
        valid_features = []

        for col in feature_cols:
            if col in df.columns:
                # 检查特征的有效性
                non_null_ratio = df[col].notna().sum() / len(df)

                # 检查是否为数值类型
                try:
                    # 尝试转换为数值类型
                    test_series = pd.to_numeric(df[col].dropna().head(100), errors='coerce')
                    if test_series.notna().sum() > 50:  # 至少50%能转换为数值
                        if non_null_ratio > 0.01:  # 至少1%的数据非空
                            valid_features.append(col)
                except:
                    # 如果转换失败，跳过该特征
                    continue
        
        logger.info(f"选择了 {len(valid_features)} 个特征")
        logger.info(f"特征列表: {valid_features[:10]}...")  # 显示前10个特征
        
        return df[valid_features], valid_features
    
    def prepare_data(self) -> None:
        """准备训练数据"""
        logger.info("准备训练数据...")
        
        # 创建目标变量
        y = self.create_target_variable()
        
        # 准备特征
        X, feature_names = self.prepare_features()
        
        # 处理缺失值
        X = X.fillna(X.median())
        
        # 移除异常值（基于目标变量）
        Q1 = y.quantile(0.25)
        Q3 = y.quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        valid_mask = (y >= lower_bound) & (y <= upper_bound)
        X = X[valid_mask]
        y = y[valid_mask]
        
        logger.info(f"移除异常值后数据形状: X={X.shape}, y={y.shape}")
        
        # 数据分割
        X_temp, self.X_test, y_temp, self.y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        self.X_train, self.X_val, self.y_train, self.y_val = train_test_split(
            X_temp, y_temp, test_size=0.25, random_state=42  # 0.25 * 0.8 = 0.2
        )
        
        # 数据标准化
        self.scaler = StandardScaler()
        self.X_train_scaled = self.scaler.fit_transform(self.X_train)
        self.X_val_scaled = self.scaler.transform(self.X_val)
        self.X_test_scaled = self.scaler.transform(self.X_test)
        
        # 保存特征名称
        self.feature_names = feature_names
        
        logger.info(f"数据分割完成:")
        logger.info(f"  训练集: {self.X_train.shape}")
        logger.info(f"  验证集: {self.X_val.shape}")
        logger.info(f"  测试集: {self.X_test.shape}")
    
    def train_traditional_models(self) -> Dict:
        """训练传统机器学习模型"""
        logger.info("训练传统机器学习模型...")
        
        models = {
            'XGBoost': xgb.XGBRegressor(
                n_estimators=200,
                max_depth=6,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                n_jobs=-1
            ),
            'LightGBM': lgb.LGBMRegressor(
                n_estimators=200,
                max_depth=6,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                n_jobs=-1,
                verbose=-1
            ),
            'RandomForest': RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            ),
            'Ridge': Ridge(alpha=1.0),
            'ElasticNet': ElasticNet(alpha=1.0, l1_ratio=0.5, random_state=42)
        }
        
        results = {}
        
        for name, model in models.items():
            logger.info(f"训练 {name} 模型...")
            
            try:
                # 训练模型
                if name in ['Ridge', 'ElasticNet']:
                    model.fit(self.X_train_scaled, self.y_train)
                    train_pred = model.predict(self.X_train_scaled)
                    val_pred = model.predict(self.X_val_scaled)
                    test_pred = model.predict(self.X_test_scaled)
                else:
                    model.fit(self.X_train, self.y_train)
                    train_pred = model.predict(self.X_train)
                    val_pred = model.predict(self.X_val)
                    test_pred = model.predict(self.X_test)
                
                # 评估模型
                train_r2 = r2_score(self.y_train, train_pred)
                val_r2 = r2_score(self.y_val, val_pred)
                test_r2 = r2_score(self.y_test, test_pred)
                
                train_rmse = np.sqrt(mean_squared_error(self.y_train, train_pred))
                val_rmse = np.sqrt(mean_squared_error(self.y_val, val_pred))
                test_rmse = np.sqrt(mean_squared_error(self.y_test, test_pred))
                
                results[name] = {
                    'model': model,
                    'train_r2': train_r2,
                    'val_r2': val_r2,
                    'test_r2': test_r2,
                    'train_rmse': train_rmse,
                    'val_rmse': val_rmse,
                    'test_rmse': test_rmse,
                    'predictions': {
                        'train': train_pred,
                        'val': val_pred,
                        'test': test_pred
                    }
                }
                
                logger.info(f"{name} - 验证集 R²: {val_r2:.4f}, RMSE: {val_rmse:.4f}")
                
            except Exception as e:
                logger.error(f"{name} 模型训练失败: {e}")
                continue
        
        self.models.update(results)
        return results

    def create_neural_network(self, input_dim: int) -> keras.Model:
        """创建神经网络模型"""
        model = keras.Sequential([
            layers.Dense(256, activation='relu', input_shape=(input_dim,)),
            layers.Dropout(0.3),
            layers.Dense(128, activation='relu'),
            layers.Dropout(0.2),
            layers.Dense(64, activation='relu'),
            layers.Dropout(0.1),
            layers.Dense(32, activation='relu'),
            layers.Dense(1, activation='linear')
        ])

        model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=0.001),
            loss='mse',
            metrics=['mae']
        )

        return model

    def create_lstm_model(self, sequence_length: int, n_features: int) -> keras.Model:
        """创建LSTM模型"""
        model = keras.Sequential([
            layers.LSTM(64, return_sequences=True, input_shape=(sequence_length, n_features)),
            layers.Dropout(0.2),
            layers.LSTM(32, return_sequences=False),
            layers.Dropout(0.2),
            layers.Dense(32, activation='relu'),
            layers.Dense(1, activation='linear')
        ])

        model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=0.001),
            loss='mse',
            metrics=['mae']
        )

        return model

    def prepare_lstm_data(self, sequence_length: int = 24) -> Tuple:
        """为LSTM准备序列数据"""
        logger.info(f"为LSTM准备序列数据，序列长度: {sequence_length}")

        # 按时间排序
        data_with_target = self.data.copy()
        data_with_target['target'] = self.create_target_variable()
        data_with_target = data_with_target.sort_values('Timestamp')

        # 选择特征
        _, feature_names = self.prepare_features()
        features = data_with_target[feature_names].fillna(method='ffill').fillna(0)
        target = data_with_target['target']

        # 创建序列
        X_sequences = []
        y_sequences = []

        for i in range(sequence_length, len(features)):
            X_sequences.append(features.iloc[i-sequence_length:i].values)
            y_sequences.append(target.iloc[i])

        X_sequences = np.array(X_sequences)
        y_sequences = np.array(y_sequences)

        # 数据分割
        train_size = int(0.7 * len(X_sequences))
        val_size = int(0.15 * len(X_sequences))

        X_train_lstm = X_sequences[:train_size]
        y_train_lstm = y_sequences[:train_size]

        X_val_lstm = X_sequences[train_size:train_size+val_size]
        y_val_lstm = y_sequences[train_size:train_size+val_size]

        X_test_lstm = X_sequences[train_size+val_size:]
        y_test_lstm = y_sequences[train_size+val_size:]

        return X_train_lstm, X_val_lstm, X_test_lstm, y_train_lstm, y_val_lstm, y_test_lstm

    def train_deep_learning_models(self) -> Dict:
        """训练深度学习模型"""
        if not TENSORFLOW_AVAILABLE:
            logger.warning("TensorFlow不可用，跳过深度学习模型")
            return {}

        logger.info("训练深度学习模型...")

        results = {}

        try:
            # 1. 训练神经网络
            logger.info("训练神经网络...")

            nn_model = self.create_neural_network(self.X_train_scaled.shape[1])

            # 早停和学习率调度
            early_stopping = keras.callbacks.EarlyStopping(
                monitor='val_loss', patience=10, restore_best_weights=True
            )

            reduce_lr = keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss', factor=0.5, patience=5, min_lr=1e-6
            )

            # 训练
            history = nn_model.fit(
                self.X_train_scaled, self.y_train,
                validation_data=(self.X_val_scaled, self.y_val),
                epochs=100,
                batch_size=256,
                callbacks=[early_stopping, reduce_lr],
                verbose=0
            )

            # 预测
            train_pred = nn_model.predict(self.X_train_scaled, verbose=0).flatten()
            val_pred = nn_model.predict(self.X_val_scaled, verbose=0).flatten()
            test_pred = nn_model.predict(self.X_test_scaled, verbose=0).flatten()

            # 评估
            train_r2 = r2_score(self.y_train, train_pred)
            val_r2 = r2_score(self.y_val, val_pred)
            test_r2 = r2_score(self.y_test, test_pred)

            train_rmse = np.sqrt(mean_squared_error(self.y_train, train_pred))
            val_rmse = np.sqrt(mean_squared_error(self.y_val, val_pred))
            test_rmse = np.sqrt(mean_squared_error(self.y_test, test_pred))

            results['NeuralNetwork'] = {
                'model': nn_model,
                'train_r2': train_r2,
                'val_r2': val_r2,
                'test_r2': test_r2,
                'train_rmse': train_rmse,
                'val_rmse': val_rmse,
                'test_rmse': test_rmse,
                'history': history.history,
                'predictions': {
                    'train': train_pred,
                    'val': val_pred,
                    'test': test_pred
                }
            }

            logger.info(f"神经网络 - 验证集 R²: {val_r2:.4f}, RMSE: {val_rmse:.4f}")

        except Exception as e:
            logger.error(f"神经网络训练失败: {e}")

        try:
            # 2. 训练LSTM模型
            logger.info("训练LSTM模型...")

            # 准备LSTM数据
            X_train_lstm, X_val_lstm, X_test_lstm, y_train_lstm, y_val_lstm, y_test_lstm = self.prepare_lstm_data()

            if len(X_train_lstm) > 1000:  # 确保有足够的数据
                lstm_model = self.create_lstm_model(X_train_lstm.shape[1], X_train_lstm.shape[2])

                # 训练LSTM
                history_lstm = lstm_model.fit(
                    X_train_lstm, y_train_lstm,
                    validation_data=(X_val_lstm, y_val_lstm),
                    epochs=50,
                    batch_size=128,
                    callbacks=[early_stopping, reduce_lr],
                    verbose=0
                )

                # 预测
                train_pred_lstm = lstm_model.predict(X_train_lstm, verbose=0).flatten()
                val_pred_lstm = lstm_model.predict(X_val_lstm, verbose=0).flatten()
                test_pred_lstm = lstm_model.predict(X_test_lstm, verbose=0).flatten()

                # 评估
                train_r2_lstm = r2_score(y_train_lstm, train_pred_lstm)
                val_r2_lstm = r2_score(y_val_lstm, val_pred_lstm)
                test_r2_lstm = r2_score(y_test_lstm, test_pred_lstm)

                train_rmse_lstm = np.sqrt(mean_squared_error(y_train_lstm, train_pred_lstm))
                val_rmse_lstm = np.sqrt(mean_squared_error(y_val_lstm, val_pred_lstm))
                test_rmse_lstm = np.sqrt(mean_squared_error(y_test_lstm, test_pred_lstm))

                results['LSTM'] = {
                    'model': lstm_model,
                    'train_r2': train_r2_lstm,
                    'val_r2': val_r2_lstm,
                    'test_r2': test_r2_lstm,
                    'train_rmse': train_rmse_lstm,
                    'val_rmse': val_rmse_lstm,
                    'test_rmse': test_rmse_lstm,
                    'history': history_lstm.history,
                    'predictions': {
                        'train': train_pred_lstm,
                        'val': val_pred_lstm,
                        'test': test_pred_lstm
                    }
                }

                logger.info(f"LSTM - 验证集 R²: {val_r2_lstm:.4f}, RMSE: {val_rmse_lstm:.4f}")
            else:
                logger.warning("LSTM数据不足，跳过LSTM模型训练")

        except Exception as e:
            logger.error(f"LSTM训练失败: {e}")

        self.models.update(results)
        return results

    def create_ensemble_model(self) -> Dict:
        """创建集成模型"""
        logger.info("创建集成模型...")

        # 选择表现最好的传统模型
        traditional_models = ['XGBoost', 'LightGBM', 'RandomForest']
        available_models = [(name, self.models[name]['model']) for name in traditional_models
                           if name in self.models]

        if len(available_models) < 2:
            logger.warning("可用模型不足，无法创建集成模型")
            return {}

        try:
            # 创建投票回归器
            voting_regressor = VotingRegressor(available_models)
            voting_regressor.fit(self.X_train, self.y_train)

            # 预测
            train_pred = voting_regressor.predict(self.X_train)
            val_pred = voting_regressor.predict(self.X_val)
            test_pred = voting_regressor.predict(self.X_test)

            # 评估
            train_r2 = r2_score(self.y_train, train_pred)
            val_r2 = r2_score(self.y_val, val_pred)
            test_r2 = r2_score(self.y_test, test_pred)

            train_rmse = np.sqrt(mean_squared_error(self.y_train, train_pred))
            val_rmse = np.sqrt(mean_squared_error(self.y_val, val_pred))
            test_rmse = np.sqrt(mean_squared_error(self.y_test, test_pred))

            ensemble_result = {
                'Ensemble': {
                    'model': voting_regressor,
                    'train_r2': train_r2,
                    'val_r2': val_r2,
                    'test_r2': test_r2,
                    'train_rmse': train_rmse,
                    'val_rmse': val_rmse,
                    'test_rmse': test_rmse,
                    'predictions': {
                        'train': train_pred,
                        'val': val_pred,
                        'test': test_pred
                    }
                }
            }

            logger.info(f"集成模型 - 验证集 R²: {val_r2:.4f}, RMSE: {val_rmse:.4f}")

            self.models.update(ensemble_result)
            return ensemble_result

        except Exception as e:
            logger.error(f"集成模型创建失败: {e}")
            return {}

    def evaluate_all_models(self) -> pd.DataFrame:
        """评估所有模型"""
        logger.info("评估所有模型...")

        results_df = []

        for name, result in self.models.items():
            if 'val_r2' in result:
                results_df.append({
                    'Model': name,
                    'Train_R2': result['train_r2'],
                    'Val_R2': result['val_r2'],
                    'Test_R2': result['test_r2'],
                    'Train_RMSE': result['train_rmse'],
                    'Val_RMSE': result['val_rmse'],
                    'Test_RMSE': result['test_rmse'],
                    'Overfit_Score': result['train_r2'] - result['val_r2']  # 过拟合指标
                })

        results_df = pd.DataFrame(results_df)
        results_df = results_df.sort_values('Val_R2', ascending=False)

        logger.info("模型评估结果:")
        logger.info(f"\n{results_df.to_string(index=False)}")

        return results_df

    def create_model_comparison_plots(self) -> None:
        """创建模型比较图表"""
        logger.info("创建模型比较图表...")

        # 1. 模型性能比较
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('高级空调负荷识别模型比较', fontsize=16, fontweight='bold')

        # R²比较
        ax1 = axes[0, 0]
        models_names = []
        val_r2_scores = []
        test_r2_scores = []

        for name, result in self.models.items():
            if 'val_r2' in result:
                models_names.append(name)
                val_r2_scores.append(result['val_r2'])
                test_r2_scores.append(result['test_r2'])

        x = np.arange(len(models_names))
        width = 0.35

        ax1.bar(x - width/2, val_r2_scores, width, label='验证集 R²', alpha=0.8)
        ax1.bar(x + width/2, test_r2_scores, width, label='测试集 R²', alpha=0.8)
        ax1.set_xlabel('模型')
        ax1.set_ylabel('R² 分数')
        ax1.set_title('模型 R² 分数比较')
        ax1.set_xticks(x)
        ax1.set_xticklabels(models_names, rotation=45)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # RMSE比较
        ax2 = axes[0, 1]
        val_rmse_scores = []
        test_rmse_scores = []

        for name in models_names:
            val_rmse_scores.append(self.models[name]['val_rmse'])
            test_rmse_scores.append(self.models[name]['test_rmse'])

        ax2.bar(x - width/2, val_rmse_scores, width, label='验证集 RMSE', alpha=0.8)
        ax2.bar(x + width/2, test_rmse_scores, width, label='测试集 RMSE', alpha=0.8)
        ax2.set_xlabel('模型')
        ax2.set_ylabel('RMSE')
        ax2.set_title('模型 RMSE 比较')
        ax2.set_xticks(x)
        ax2.set_xticklabels(models_names, rotation=45)
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 预测vs实际值散点图（最佳模型）
        ax3 = axes[1, 0]
        best_model_name = max(self.models.keys(),
                             key=lambda x: self.models[x].get('val_r2', 0))
        best_result = self.models[best_model_name]

        test_pred = best_result['predictions']['test']
        ax3.scatter(self.y_test, test_pred, alpha=0.5)
        ax3.plot([self.y_test.min(), self.y_test.max()],
                [self.y_test.min(), self.y_test.max()], 'r--', lw=2)
        ax3.set_xlabel('实际值')
        ax3.set_ylabel('预测值')
        ax3.set_title(f'{best_model_name} - 预测vs实际值')
        ax3.grid(True, alpha=0.3)

        # 残差图
        ax4 = axes[1, 1]
        residuals = self.y_test - test_pred
        ax4.scatter(test_pred, residuals, alpha=0.5)
        ax4.axhline(y=0, color='r', linestyle='--')
        ax4.set_xlabel('预测值')
        ax4.set_ylabel('残差')
        ax4.set_title(f'{best_model_name} - 残差分析')
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图表
        plot_file = 'figures/model_comparison/advanced_models_comparison.png'
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        logger.info(f"模型比较图表已保存至: {plot_file}")
        plt.close()

        # 2. 特征重要性图（针对树模型）
        if 'XGBoost' in self.models:
            self.plot_feature_importance('XGBoost')
        elif 'LightGBM' in self.models:
            self.plot_feature_importance('LightGBM')
        elif 'RandomForest' in self.models:
            self.plot_feature_importance('RandomForest')

    def plot_feature_importance(self, model_name: str) -> None:
        """绘制特征重要性"""
        if model_name not in self.models:
            return

        model = self.models[model_name]['model']

        if hasattr(model, 'feature_importances_'):
            importance = model.feature_importances_
            feature_importance = pd.DataFrame({
                'feature': self.feature_names,
                'importance': importance
            }).sort_values('importance', ascending=False)

            plt.figure(figsize=(10, 8))
            top_features = feature_importance.head(20)
            plt.barh(range(len(top_features)), top_features['importance'])
            plt.yticks(range(len(top_features)), top_features['feature'])
            plt.xlabel('特征重要性')
            plt.title(f'{model_name} - 前20个重要特征')
            plt.gca().invert_yaxis()
            plt.tight_layout()

            plot_file = f'figures/model_comparison/{model_name}_feature_importance.png'
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            logger.info(f"{model_name} 特征重要性图已保存至: {plot_file}")
            plt.close()

    def save_best_model(self) -> str:
        """保存最佳模型"""
        logger.info("保存最佳模型...")

        # 选择验证集R²最高的模型
        best_model_name = max(self.models.keys(),
                             key=lambda x: self.models[x].get('val_r2', 0))
        best_result = self.models[best_model_name]

        logger.info(f"最佳模型: {best_model_name}")
        logger.info(f"验证集 R²: {best_result['val_r2']:.4f}")
        logger.info(f"测试集 R²: {best_result['test_r2']:.4f}")

        # 保存模型
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        if best_model_name in ['NeuralNetwork', 'LSTM']:
            # 保存深度学习模型
            model_file = f'models/advanced/best_model_{best_model_name}_{timestamp}.h5'
            best_result['model'].save(model_file)
        else:
            # 保存传统机器学习模型
            model_file = f'models/advanced/best_model_{best_model_name}_{timestamp}.pkl'
            joblib.dump(best_result['model'], model_file)

        # 保存预处理器
        scaler_file = f'models/advanced/scaler_{timestamp}.pkl'
        joblib.dump(self.scaler, scaler_file)

        # 保存模型信息
        model_info = {
            'model_name': best_model_name,
            'model_type': 'deep_learning' if best_model_name in ['NeuralNetwork', 'LSTM'] else 'traditional',
            'features': self.feature_names,
            'performance': {
                'train_r2': best_result['train_r2'],
                'val_r2': best_result['val_r2'],
                'test_r2': best_result['test_r2'],
                'train_rmse': best_result['train_rmse'],
                'val_rmse': best_result['val_rmse'],
                'test_rmse': best_result['test_rmse']
            },
            'training_date': timestamp,
            'data_shape': self.data.shape,
            'feature_count': len(self.feature_names)
        }

        info_file = f'models/advanced/model_info_{timestamp}.json'
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(model_info, f, ensure_ascii=False, indent=2)

        logger.info(f"最佳模型已保存:")
        logger.info(f"  模型文件: {model_file}")
        logger.info(f"  预处理器: {scaler_file}")
        logger.info(f"  模型信息: {info_file}")

        return model_file

def main():
    """主函数"""
    print("空调负荷柔性调控能力分析系统 - 高级模型训练")
    print("=" * 70)

    try:
        # 检查依赖
        print("检查依赖包...")
        try:
            import xgboost
            import lightgbm
            print("✓ XGBoost 和 LightGBM 可用")
        except ImportError as e:
            print(f"✗ 缺少依赖包: {e}")
            print("请安装: pip install xgboost lightgbm")
            return 1

        if TENSORFLOW_AVAILABLE:
            print("✓ TensorFlow 可用，将训练深度学习模型")
        else:
            print("✗ TensorFlow 不可用，将跳过深度学习模型")

        # 创建训练器
        trainer = AdvancedACLoadModelTrainer()

        # 1. 加载数据
        print("\n1. 加载完整数据集...")
        trainer.load_data()

        # 2. 准备数据
        print("2. 准备训练数据...")
        trainer.prepare_data()

        # 3. 训练传统机器学习模型
        print("3. 训练传统机器学习模型...")
        traditional_results = trainer.train_traditional_models()

        # 4. 训练深度学习模型
        if TENSORFLOW_AVAILABLE:
            print("4. 训练深度学习模型...")
            dl_results = trainer.train_deep_learning_models()
        else:
            print("4. 跳过深度学习模型训练")

        # 5. 创建集成模型
        print("5. 创建集成模型...")
        ensemble_results = trainer.create_ensemble_model()

        # 6. 评估所有模型
        print("6. 评估所有模型...")
        results_df = trainer.evaluate_all_models()

        # 7. 创建比较图表
        print("7. 创建模型比较图表...")
        trainer.create_model_comparison_plots()

        # 8. 保存最佳模型
        print("8. 保存最佳模型...")
        best_model_file = trainer.save_best_model()

        # 9. 显示最终结果
        print("\n" + "=" * 70)
        print("高级模型训练完成！")
        print(f"训练了 {len(trainer.models)} 个模型")

        if len(results_df) > 0:
            best_model = results_df.iloc[0]
            print(f"\n最佳模型: {best_model['Model']}")
            print(f"验证集 R²: {best_model['Val_R2']:.4f}")
            print(f"测试集 R²: {best_model['Test_R2']:.4f}")
            print(f"测试集 RMSE: {best_model['Test_RMSE']:.4f}")

        print(f"\n模型文件已保存至: {best_model_file}")
        print("查看详细结果:")
        print("- 模型比较图表: figures/model_comparison/")
        print("- 训练日志: logs/advanced_model_training.log")

        return 0

    except Exception as e:
        logger.error(f"高级模型训练失败: {e}")
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
